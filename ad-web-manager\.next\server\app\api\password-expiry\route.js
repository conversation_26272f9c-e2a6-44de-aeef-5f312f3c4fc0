(()=>{var e={};e.id=67,e.ids=[67],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5792:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GET:()=>u,POST:()=>u,authOptions:()=>a});var t=s(19854),o=s.n(t),n=s(13581),i=s(12909);(0,i.lK)();let a={providers:[(0,n.A)({name:"credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;let r=(0,i.JE)(e.username);return r&&(0,i.Oj)(e.password,r.password)?((0,i.bx)(r.id),{id:r.id,name:r.username,email:r.username,role:r.role,permissions:r.permissions}):null}})],callbacks:{jwt:async({token:e,user:r,trigger:s,session:t})=>(r&&(e.role=r.role,e.permissions=r.permissions),"update"===s&&t&&(e.role=t.role,e.permissions=t.permissions),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.permissions=r.permissions),e)},pages:{signIn:"/login"},callbacks:{jwt:async({token:e,user:r,trigger:s,session:t})=>(r&&(e.role=r.role,e.permissions=r.permissions),"update"===s&&t&&(e.role=t.role,e.permissions=t.permissions),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.permissions=r.permissions),e),redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:e.startsWith(r)?e:`${r}/login`},session:{strategy:"jwt"},secret:process.env.NEXTAUTH_SECRET||"your-secret-key-here"},u=o()(a)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{JE:()=>c,Oj:()=>f,TK:()=>w,bx:()=>h,hG:()=>x,kg:()=>m,kl:()=>y,lK:()=>l,lo:()=>p});var t=s(85663),o=s(29021),n=s.n(o),i=s(33873);let a=s.n(i)().join(process.cwd(),"users.json"),u={id:"1",username:"admin",password:"$2b$10$t5.K7vyPesCG5IR5F5TkUuSSM/QNlar8Wr243/uQuG0yyrw1mJbBK",role:"admin",permissions:{viewUsers:!0,unlockUsers:!0,manageSettings:!0,manageUsers:!0},createdAt:new Date().toISOString()};function l(){n().existsSync(a)||n().writeFileSync(a,JSON.stringify([u],null,2))}function p(){try{n().existsSync(a)||l();let e=n().readFileSync(a,"utf8");return JSON.parse(e)}catch(e){return console.error("Error reading users file:",e),[u]}}function d(e){try{n().writeFileSync(a,JSON.stringify(e,null,2))}catch(e){throw console.error("Error saving users file:",e),Error("Failed to save users")}}function c(e){return p().find(r=>r.username===e)||null}function y(e){return p().find(r=>r.id===e)||null}function f(e,r){return t.Ay.compareSync(e,r)}function g(e){return t.Ay.hashSync(e,10)}function m(e){let r=p();if(r.find(r=>r.username===e.username))throw Error("Username already exists");let s={...e,id:Date.now().toString(),password:g(e.password),createdAt:new Date().toISOString()};return r.push(s),d(r),s}function w(e,r){let s=p(),t=s.findIndex(r=>r.id===e);if(-1===t)throw Error("User not found");return r.password&&(r.password=g(r.password)),s[t]={...s[t],...r},d(s),s[t]}function x(e){let r=p(),s=r.findIndex(r=>r.id===e);if(-1===s)return!1;if("admin"===r[s].username)throw Error("Cannot delete default admin user");return r.splice(s,1),d(r),!0}function h(e){let r=p(),s=r.findIndex(r=>r.id===e);-1!==s&&(r[s].lastLogin=new Date().toISOString(),d(r))}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38701:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>E,routeModule:()=>h,serverHooks:()=>v,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>A});var t={};s.r(t),s.d(t,{GET:()=>x});var o=s(96559),n=s(48088),i=s(37719),a=s(32190),u=s(19854),l=s(5792),p=s(73010),d=s.n(p),c=s(29021),y=s.n(c),f=s(33873),g=s.n(f);function m(e){try{let r=parseInt(e);if(0===r||isNaN(r))return null;return new Date(1e3*(r/1e7-0x2b6109100))}catch{return null}}async function w(){let e=function(){try{let e=g().join(process.cwd(),"ldap-settings.json"),r=y().readFileSync(e,"utf8");return JSON.parse(r)}catch(e){throw console.error("Error reading LDAP settings:",e),Error("LDAP settings not found")}}();return new Promise((r,s)=>{let t=`${e.useSSL?"ldaps":"ldap"}://${e.server}:${e.port}`;console.log("Connecting to LDAP for password expiry check:",t);let o=d().createClient({url:t,timeout:1e4,connectTimeout:1e4});o.bind(e.username,e.password,t=>{if(t)return console.error("LDAP bind error:",t),o.destroy(),s(Error("LDAP authentication failed"));console.log("LDAP bind successful, searching for users with expired passwords..."),o.search(e.baseDN,{scope:"base",filter:"(objectClass=domain)",attributes:["maxPwdAge"]},(t,n)=>{if(t)return console.error("Domain search error:",t),o.destroy(),s(Error("Failed to get domain password policy"));let i=0;n.on("searchEntry",e=>{let r=e.pojo.attributes.find(e=>"maxPwdAge"===e.type);r&&r.values&&r.values[0]&&(i=parseInt(r.values[0]))}),n.on("end",()=>{if(0===i)return console.log("Password expiry is disabled in domain policy"),o.destroy(),r({users:[],stats:{totalExpiredUsers:0,expiredToday:0,expiredThisWeek:0,expiredThisMonth:0}});o.search(e.baseDN,{scope:"sub",filter:"(&(objectClass=user)(objectCategory=person)(!(userAccountControl:1.2.840.113556.1.4.803:=2)))",attributes:["sAMAccountName","displayName","pwdLastSet","lastLogon","department","userAccountControl"]},(e,t)=>{if(e)return console.error("User search error:",e),o.destroy(),s(Error("Failed to search users"));let n=[],a=new Date,u=new Date(a.getFullYear(),a.getMonth(),a.getDate()),l=new Date(u.getTime()-6048e5),p=new Date(u.getTime()-2592e6);t.on("searchEntry",e=>{try{let r=e.pojo.attributes,s=r.find(e=>"sAMAccountName"===e.type)?.values[0],t=r.find(e=>"displayName"===e.type)?.values[0]||s,o=r.find(e=>"pwdLastSet"===e.type)?.values[0],u=r.find(e=>"lastLogon"===e.type)?.values[0],l=r.find(e=>"department"===e.type)?.values[0],p=r.find(e=>"userAccountControl"===e.type)?.values[0];if(!s||!o||"0"===o)return;if(p){let e=parseInt(p);if(65536&e)return void console.log(`✅ Skipping user ${s} - password never expires (UAC: ${e})`);console.log(`🔍 Checking user ${s} - password can expire (UAC: ${e})`)}else console.log(`⚠️ User ${s} - no userAccountControl found`);let d=function(e,r){let s=m(e);if(!s)return null;let t=Math.abs(r)/864e9,o=new Date(s);return o.setDate(o.getDate()+t),o}(o,i);if(!d)return;if(d<a){let e=Math.floor((a.getTime()-d.getTime())/864e5);console.log(`Found expired user: ${s}, expired ${e} days ago`),n.push({username:s,displayName:t,passwordExpires:d.toISOString(),daysSinceExpiry:e,lastLogon:u&&"0"!==u?m(u)?.toISOString():void 0,department:l})}}catch(e){console.error("Error processing user entry:",e)}}),t.on("end",()=>{o.destroy();let e={totalExpiredUsers:n.length,expiredToday:n.filter(e=>{let r=new Date(e.passwordExpires);return r>=u&&r<new Date(u.getTime()+864e5)}).length,expiredThisWeek:n.filter(e=>new Date(e.passwordExpires)>=l).length,expiredThisMonth:n.filter(e=>new Date(e.passwordExpires)>=p).length};n.sort((e,r)=>e.daysSinceExpiry-r.daysSinceExpiry),console.log(`Found ${n.length} users with expired passwords`),r({users:n,stats:e})}),t.on("error",e=>{console.error("User search result error:",e),o.destroy(),s(Error("Error during user search"))})})}),n.on("error",e=>{console.error("Domain search result error:",e),o.destroy(),s(Error("Error during domain search"))})})}),o.on("error",e=>{console.error("LDAP client error:",e),s(Error("LDAP connection failed"))})})}async function x(e){try{let e=await (0,u.getServerSession)(l.authOptions);if(!e||!e.user.permissions.unlockUsers)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=await w();return a.NextResponse.json(r)}catch(e){return console.error("Password expiry API error:",e),a.NextResponse.json({error:e.message||"Failed to get expired users"},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/password-expiry/route",pathname:"/api/password-expiry",filename:"route",bundlePath:"app/api/password-expiry/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\password-expiry\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:S,workUnitAsyncStorage:A,serverHooks:v}=h;function E(){return(0,i.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:A})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73010:e=>{"use strict";e.exports=require("ldapjs")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580,854,427],()=>s(38701));module.exports=t})();