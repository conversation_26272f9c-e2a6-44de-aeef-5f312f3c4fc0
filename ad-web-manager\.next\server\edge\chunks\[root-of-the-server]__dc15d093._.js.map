{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware';\nimport { NextResponse } from 'next/server';\n\nexport default withAuth(\n  function middleware(req) {\n    const hostname = req.headers.get('host') || '';\n\n    // Mobile subdomain detection\n    if (hostname.startsWith('m.')) {\n      // Mobile subdomain detected - add mobile context\n      const response = NextResponse.next();\n      response.headers.set('x-mobile-version', 'true');\n      return response;\n    }\n\n    // Check for mobile user agent\n    const userAgent = req.headers.get('user-agent') || '';\n    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n\n    if (isMobile) {\n      const response = NextResponse.next();\n      response.headers.set('x-is-mobile', 'true');\n      return response;\n    }\n\n    return NextResponse.next();\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // Allow access to login page without authentication\n        if (req.nextUrl.pathname === '/login') {\n          return true;\n        }\n\n        // Require authentication for all other pages\n        return !!token;\n      },\n    },\n  }\n);\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api/auth (NextAuth API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - bayraktar_holding_logo.jpeg (logo file)\n     */\n    '/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;IACrB,MAAM,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW;IAE5C,6BAA6B;IAC7B,IAAI,SAAS,UAAU,CAAC,OAAO;QAC7B,iDAAiD;QACjD,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;QAClC,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;QACzC,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACnD,MAAM,WAAW,iEAAiE,IAAI,CAAC;IAEvF,IAAI,UAAU;QACZ,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;QAClC,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe;QACpC,OAAO;IACT;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,oDAAoD;YACpD,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,UAAU;gBACrC,OAAO;YACT;YAEA,6CAA6C;YAC7C,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}