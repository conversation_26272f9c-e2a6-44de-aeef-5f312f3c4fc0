'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { MobileNav } from '@/components/ui/mobile-nav';
import { DesktopNav } from '@/components/ui/desktop-nav';
import { UserPlus } from 'lucide-react';

export default function ManageUsers() {
  const { data: session } = useSession();
  const device = useDeviceDetection();

  if (!session) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-96">
          <CardHeader>
            <CardTitle>Yetki Gerekli</CardTitle>
            <CardDescription>Bu sayfayı görüntülemek için giriş yapmanız gerekiyor.</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (!session.user.permissions.manageUsers) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-96">
          <CardHeader>
            <CardTitle>Yetkisiz Erişim</CardTitle>
            <CardDescription>Bu sayfayı görüntülemek için yeterli yetkiniz yok.</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Adaptive Navigation */}
      {device.isMobile ? (
        <MobileNav currentPath="/manage-users" />
      ) : (
        <DesktopNav currentPath="/manage-users" />
      )}

      {/* Main Content */}
      <main className={`
        ${device.isMobile ? 'pb-safe px-4 py-4' : 'container mx-auto px-4 py-6'}
      `}>
        <div className={`${device.isMobile ? 'space-y-4' : 'space-y-6'}`}>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-foreground">Kullanıcı Yönetimi</h1>
              <p className="text-muted-foreground">Sistem kullanıcılarını yönetin</p>
            </div>
            
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              Yeni Kullanıcı
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Kullanıcılar</CardTitle>
              <CardDescription>
                Sistem kullanıcılarının listesi ve yönetimi
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Kullanıcı yönetimi özellikleri yakında eklenecek...</p>
            </CardContent>
          </Card>
        </div>
      </main>
      
      {/* Debug info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-2 right-2 bg-black/80 text-white text-xs p-2 rounded z-50">
          <div>Device: {device.deviceType}</div>
          <div>Width: {device.screenWidth}px</div>
          <div>Mobile: {device.isMobile ? 'Yes' : 'No'}</div>
        </div>
      )}
    </div>
  );
}
