(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{170:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var s=r(5155),a=r(2115),n=r(2108),i=r(6695),l=r(285),c=r(2523),d=r(5057),o=r(7262),u=r(5365),h=r(420),x=r(3978),p=r(8931),m=r(9946);let f=(0,m.A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),g=(0,m.A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var v=r(1154),y=r(4186),b=r(646),j=r(4861);let k=(0,m.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),w=(0,m.A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);function N(){let{data:e,update:t}=(0,n.useSession)(),r=(0,h.X)();if(!e)return null;let[m,N]=(0,a.useState)({server:"",port:"389",baseDN:"",username:"",password:"",useSSL:!1}),[S,A]=(0,a.useState)(!1),[C,D]=(0,a.useState)(""),[M,L]=(0,a.useState)(""),[T,P]=(0,a.useState)("unknown"),[z,E]=(0,a.useState)(null),[B,F]=(0,a.useState)(null),[R,_]=(0,a.useState)("");(0,a.useEffect)(()=>{O()},[]);let O=async()=>{try{console.log("Loading settings...");let e=await fetch("/api/settings?includePassword=true");if(e.ok){let t=await e.json();console.log("Settings loaded:",t),N(t),t.server&&t.username&&t.password&&(console.log("Testing connection with loaded settings..."),setTimeout(()=>J(t),500))}}catch(e){console.error("Error loading settings:",e)}},J=async e=>{if(!e.server||!e.username||!e.password)return void P("unknown");try{console.log("Testing connection with data:",e);let t=await fetch("/api/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await t.json();console.log("Connection test result:",r),t.ok&&r.success?(P("connected"),_(r.serverInfo||""),E(r.latency||null)):(P("disconnected"),console.log("Connection test failed:",r.error))}catch(e){P("disconnected"),console.error("Connection test error:",e)}},I=async()=>{A(!0),D("");try{let e=await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)}),r=await e.json();e.ok?(D("Ayarlar başarıyla kaydedildi!"),L("success"),await t()):(D(r.error||"Ayarlar kaydedilirken hata oluştu!"),L("error"))}catch(e){D("Bağlantı hatası!"),L("error")}finally{A(!1)}},Z=async()=>{A(!0),D(""),P("testing");let e=Date.now();try{let t=await fetch("/api/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)}),r=Date.now(),s=await t.json();t.ok&&s.success?(D("LDAP bağlantısı başarılı!"),L("success"),P("connected"),E(r-e),_(s.serverInfo||"")):(D(s.error||"LDAP bağlantısı başarısız!"),L("error"),P("disconnected"),E(null))}catch(e){D("Bağlantı testi sırasında hata oluştu!"),L("error"),P("disconnected"),E(null)}finally{A(!1),F(new Date)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[r.isMobile?(0,s.jsx)(x.c,{currentPath:"/settings"}):(0,s.jsx)(p.u,{currentPath:"/settings"}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-foreground mb-4",children:"LDAP Ayarları"}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground",children:"Active Directory bağlantı ayarlarını yapılandırın"})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(()=>{switch(T){case"connected":return(0,s.jsx)(f,{className:"h-5 w-5 text-green-600"});case"disconnected":return(0,s.jsx)(g,{className:"h-5 w-5 text-red-600"});case"testing":return(0,s.jsx)(v.A,{className:"h-5 w-5 text-blue-600 animate-spin"});default:return(0,s.jsx)(y.A,{className:"h-5 w-5 text-gray-600"})}})(),(0,s.jsx)("span",{children:"LDAP Bağlantı Durumu"})]}),(0,s.jsx)(i.BT,{children:"Mevcut LDAP bağlantısının durumu ve performans bilgileri"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold mb-2",children:(0,s.jsx)("span",{className:(()=>{switch(T){case"connected":return"text-green-600";case"disconnected":return"text-red-600";case"testing":return"text-blue-600";default:return"text-gray-600"}})(),children:(()=>{switch(T){case"connected":return"Bağlı";case"disconnected":return"Bağlantısız";case"testing":return"Test Ediliyor...";default:return"Bilinmiyor"}})()})}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Durum"})]}),null!==z&&(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:[z,"ms"]}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Gecikme"})]}),R&&(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-lg font-bold text-purple-600 mb-2 font-mono",children:R}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Sunucu"})]}),B&&(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-lg font-medium text-gray-600 mb-2",children:B.toLocaleTimeString("tr-TR")}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Son Test"})]})]})})]}),C&&(0,s.jsxs)(u.Fc,{className:"success"===M?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:["success"===M?(0,s.jsx)(b.A,{className:"h-4 w-4 text-green-600"}):(0,s.jsx)(j.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)(u.TN,{className:"success"===M?"text-green-800":"text-red-800",children:C})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"LDAP Ayarları"}),(0,s.jsx)(i.BT,{children:"Active Directory sunucu bağlantı bilgilerini girin"})]}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"server",children:"LDAP Sunucu"}),(0,s.jsx)(c.p,{id:"server",placeholder:"ldap.example.com",value:m.server,onChange:e=>N({...m,server:e.target.value})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"port",children:"Port"}),(0,s.jsx)(c.p,{id:"port",placeholder:"389",value:m.port,onChange:e=>N({...m,port:e.target.value})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"baseDN",children:"Base DN"}),(0,s.jsx)(c.p,{id:"baseDN",placeholder:"DC=example,DC=com",value:m.baseDN,onChange:e=>N({...m,baseDN:e.target.value})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"username",children:"Kullanıcı Adı"}),(0,s.jsx)(c.p,{id:"username",placeholder:"<EMAIL>",value:m.username,onChange:e=>N({...m,username:e.target.value})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"password",children:"Şifre"}),(0,s.jsx)(c.p,{id:"password",type:"password",placeholder:"••••••••",value:m.password,onChange:e=>N({...m,password:e.target.value})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.S,{id:"useSSL",checked:m.useSSL,onCheckedChange:e=>N({...m,useSSL:e})}),(0,s.jsx)(d.J,{htmlFor:"useSSL",children:"SSL/TLS Kullan"})]}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)(l.$,{onClick:I,disabled:S,children:S?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Kaydediliyor..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k,{className:"mr-2 h-4 w-4"}),"Kaydet"]})}),(0,s.jsx)(l.$,{variant:"outline",onClick:Z,disabled:S,children:S?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Ediliyor..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(w,{className:"mr-2 h-4 w-4"}),"Bağlantıyı Test Et"]})})]})]})]})]})})]})}},646:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},655:(e,t,r)=>{Promise.resolve().then(r.bind(r,170))},968:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var s=r(2115),a=r(3655),n=r(5155),i=s.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},1154:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1275:(e,t,r)=>{"use strict";r.d(t,{X:()=>n});var s=r(2115),a=r(2712);function n(e){let[t,r]=s.useState(void 0);return(0,a.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let s,a;if(!Array.isArray(t)||!t.length)return;let n=t[0];if("borderBoxSize"in n){let e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;s=t.inlineSize,a=t.blockSize}else s=e.offsetWidth,a=e.offsetHeight;r({width:s,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(5155);r(2115);var a=r(9434);function n(e){let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},4186:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var s=r(5155);r(2115);var a=r(968),n=r(9434);function i(e){let{className:t,...r}=e;return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},5196:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>c});var s=r(5155);r(2115);var a=r(2085),n=r(9434);let i=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),t),...a})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},5503:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(2115);function a(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},6981:(e,t,r)=>{"use strict";r.d(t,{C1:()=>w,bL:()=>j});var s=r(2115),a=r(6101),n=r(6081),i=r(5185),l=r(5845),c=r(5503),d=r(1275),o=r(8905),u=r(3655),h=r(5155),x="Checkbox",[p,m]=(0,n.A)(x),[f,g]=p(x);function v(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:n,disabled:i,form:c,name:d,onCheckedChange:o,required:u,value:p="on",internal_do_not_use_render:m}=e,[g,v]=(0,l.i)({prop:r,defaultProp:null!=n&&n,onChange:o,caller:x}),[y,b]=s.useState(null),[j,k]=s.useState(null),w=s.useRef(!1),N=!y||!!c||!!y.closest("form"),S={checked:g,disabled:i,setChecked:v,control:y,setControl:b,name:d,form:c,value:p,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!A(n)&&n,isFormControl:N,bubbleInput:j,setBubbleInput:k};return(0,h.jsx)(f,{scope:t,...S,children:"function"==typeof m?m(S):a})}var y="CheckboxTrigger",b=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:n,onClick:l,...c}=e,{control:d,value:o,disabled:x,checked:p,required:m,setControl:f,setChecked:v,hasConsumerStoppedPropagationRef:b,isFormControl:j,bubbleInput:k}=g(y,r),w=(0,a.s)(t,f),N=s.useRef(p);return s.useEffect(()=>{let e=null==d?void 0:d.form;if(e){let t=()=>v(N.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[d,v]),(0,h.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":A(p)?"mixed":p,"aria-required":m,"data-state":C(p),"data-disabled":x?"":void 0,disabled:x,value:o,...c,ref:w,onKeyDown:(0,i.m)(n,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(l,e=>{v(e=>!!A(e)||!e),k&&j&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});b.displayName=y;var j=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:s,checked:a,defaultChecked:n,required:i,disabled:l,value:c,onCheckedChange:d,form:o,...u}=e;return(0,h.jsx)(v,{__scopeCheckbox:r,checked:a,defaultChecked:n,disabled:l,required:i,onCheckedChange:d,name:s,form:o,value:c,internal_do_not_use_render:e=>{let{isFormControl:s}=e;return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(b,{...u,ref:t,__scopeCheckbox:r}),s&&(0,h.jsx)(S,{__scopeCheckbox:r})]})}})});j.displayName=x;var k="CheckboxIndicator",w=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:s,...a}=e,n=g(k,r);return(0,h.jsx)(o.C,{present:s||A(n.checked)||!0===n.checked,children:(0,h.jsx)(u.sG.span,{"data-state":C(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=k;var N="CheckboxBubbleInput",S=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,...n}=e,{control:i,hasConsumerStoppedPropagationRef:l,checked:o,defaultChecked:x,required:p,disabled:m,name:f,value:v,form:y,bubbleInput:b,setBubbleInput:j}=g(N,r),k=(0,a.s)(t,j),w=(0,c.Z)(o),S=(0,d.X)(i);s.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(w!==o&&e){let r=new Event("click",{bubbles:t});b.indeterminate=A(o),e.call(b,!A(o)&&o),b.dispatchEvent(r)}},[b,w,o,l]);let C=s.useRef(!A(o)&&o);return(0,h.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=x?x:C.current,required:p,disabled:m,name:f,value:v,form:y,...n,tabIndex:-1,ref:k,style:{...n.style,...S,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(e){return"indeterminate"===e}function C(e){return A(e)?"indeterminate":e?"checked":"unchecked"}S.displayName=N},7262:(e,t,r)=>{"use strict";r.d(t,{S:()=>l});var s=r(5155);r(2115);var a=r(6981),n=r(5196),i=r(9434);function l(e){let{className:t,...r}=e;return(0,s.jsx)(a.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,s.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(n.A,{className:"size-3.5"})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[108,460,349,449,441,684,358],()=>t(655)),_N_E=e.O()}]);