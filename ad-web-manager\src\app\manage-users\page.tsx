'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, User<PERSON><PERSON>, Edit, Trash2, LogOut, CheckCircle, XCircle } from 'lucide-react';

interface User {
  id: string;
  username: string;
  role: 'admin' | 'user';
  permissions: {
    viewUsers: boolean;
    unlockUsers: boolean;
    manageSettings: boolean;
    manageUsers: boolean;
  };
  createdAt: string;
  lastLogin?: string;
}

interface NewUser {
  username: string;
  password: string;
  role: 'admin' | 'user';
  permissions: {
    viewUsers: boolean;
    unlockUsers: boolean;
    manageSettings: boolean;
    manageUsers: boolean;
  };
}

export default function ManageUsers() {
  const { data: session, update: updateSession } = useSession();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  
  const [newUser, setNewUser] = useState<NewUser>({
    username: '',
    password: '',
    role: 'user',
    permissions: {
      viewUsers: false,
      unlockUsers: false,
      manageSettings: false,
      manageUsers: false,
    }
  });

  useEffect(() => {
    loadUsers();
  }, []);

  const handleLogout = () => {
    signOut({ callbackUrl: '/login' });
  };

  const refreshCurrentUserSession = async () => {
    try {
      console.log('Refreshing session for current user...');
      const response = await fetch('/api/auth/refresh-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        console.log('Session refresh successful');
        await updateSession();
      } else {
        console.log('Session refresh failed');
      }
    } catch (error) {
      console.error('Error refreshing session:', error);
    }
  };

  const loadUsers = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        console.log('API Response:', data); // Debug log
        setUsers(data.users || []); // API returns { users: [...] }
      } else {
        setMessage('Kullanicilar yuklenemedi!');
        setMessageType('error');
      }
    } catch (error) {
      console.error('Load users error:', error);
      setMessage('Baglanti hatasi!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newUser),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage('Kullanici basariyla olusturuldu!');
        setMessageType('success');
        setIsDialogOpen(false);
        setNewUser({
          username: '',
          password: '',
          role: 'user',
          permissions: {
            viewUsers: false,
            unlockUsers: false,
            manageSettings: false,
            manageUsers: false,
          }
        });
        loadUsers();
      } else {
        setMessage(result.error || 'Kullanici olusturulamadi!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Baglanti hatasi!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Bu kullaniciyi silmek istediginizden emin misiniz?')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (response.ok) {
        setMessage('Kullanici basariyla silindi!');
        setMessageType('success');
        loadUsers();
      } else {
        setMessage(result.error || 'Kullanici silinemedi!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Baglanti hatasi!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('tr-TR');
    } catch {
      return 'Bilinmiyor';
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setNewUser({
      username: user.username,
      password: '',
      role: user.role,
      permissions: { ...user.permissions }
    });
    setIsDialogOpen(true);
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingUser) return;

    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch(`/api/users/${editingUser.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role: newUser.role,
          permissions: newUser.permissions,
          ...(newUser.password && { password: newUser.password })
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage('Kullanici basariyla guncellendi!');
        setMessageType('success');
        setIsDialogOpen(false);
        setEditingUser(null);
        setNewUser({
          username: '',
          password: '',
          role: 'user',
          permissions: {
            viewUsers: false,
            unlockUsers: false,
            manageSettings: false,
            manageUsers: false,
          }
        });
        loadUsers();

        const currentUsername = session?.user?.username || session?.user?.name;
        const isCurrentUser = editingUser.username === currentUsername;

        if (isCurrentUser) {
          setMessage('Yetkiniz guncellendi. Yeniden giris yapmaniz gerekiyor...');
          setMessageType('success');

          setTimeout(() => {
            signOut({ callbackUrl: '/login' });
          }, 2000);
        }
      } else {
        setMessage(result.error || 'Kullanici guncellenirken hata olustu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Baglanti hatasi!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  if (!session) {
    return null;
  }

  if (!session.user.permissions.manageUsers) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card>
          <CardHeader>
            <CardTitle>Erisim Reddedildi</CardTitle>
            <CardDescription>
              Bu sayfaya erisim yetkiniz bulunmamaktadir.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleLogout} variant="outline">
              <LogOut className="mr-2 h-4 w-4" />
              Cikis Yap
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <nav className="border-b bg-card">
        <div className="container mx-auto px-4">
          <div className="flex h-24 items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/Bayraktar Holding Logo.png"
                alt="Bayraktar Holding Logo"
                width={160}
                height={80}
                className="h-16 w-auto"
              />
              <span className="text-xl font-bold text-primary">AD Web Manager</span>
            </Link>
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-sm font-medium text-muted-foreground hover:text-primary">
                Dashboard
              </Link>
              <Link href="/users" className="text-sm font-medium text-muted-foreground hover:text-primary">
                Locked Users
              </Link>
              <Link href="/password-expiry" className="text-sm font-medium text-muted-foreground hover:text-primary">
                Password Expiry
              </Link>
              <Link href="/manage-users" className="text-sm font-medium text-foreground hover:text-primary">
                Manage Users
              </Link>
              <Link href="/settings" className="text-sm font-medium text-muted-foreground hover:text-primary">
                Settings
              </Link>
              <Button onClick={handleLogout} variant="outline" size="sm">
                <LogOut className="mr-2 h-4 w-4" />
                Cikis
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Kullanici Yonetimi</h1>
            <p className="text-muted-foreground">Sistem kullanicilarini yonetin</p>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => {
                setEditingUser(null);
                setNewUser({
                  username: '',
                  password: '',
                  role: 'user',
                  permissions: {
                    viewUsers: false,
                    unlockUsers: false,
                    manageSettings: false,
                    manageUsers: false,
                  }
                });
              }}>
                <UserPlus className="mr-2 h-4 w-4" />
                Yeni Kullanici
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>
                  {editingUser ? 'Kullanici Duzenle' : 'Yeni Kullanici Olustur'}
                </DialogTitle>
                <DialogDescription>
                  {editingUser ? 'Kullanici bilgilerini guncelleyin' : 'Yeni bir kullanici hesabi olusturun'}
                </DialogDescription>
              </DialogHeader>
              
              <form onSubmit={editingUser ? handleUpdateUser : handleCreateUser} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="username">Kullanici Adi</Label>
                    <Input
                      id="username"
                      value={newUser.username}
                      onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}
                      disabled={!!editingUser}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">
                      {editingUser ? 'Yeni Sifre (Bos birakabilirsiniz)' : 'Sifre'}
                    </Label>
                    <Input
                      id="password"
                      type="password"
                      value={newUser.password}
                      onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                      required={!editingUser}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Rol</Label>
                  <Select value={newUser.role} onValueChange={(value: 'admin' | 'user') => setNewUser({ ...newUser, role: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">Kullanici</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <Label>Yetkiler</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="viewUsers"
                        checked={newUser.permissions.viewUsers}
                        onCheckedChange={(checked) => 
                          setNewUser({
                            ...newUser,
                            permissions: { ...newUser.permissions, viewUsers: !!checked }
                          })
                        }
                      />
                      <Label htmlFor="viewUsers" className="text-sm">Kullanicilari Goruntule</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="unlockUsers"
                        checked={newUser.permissions.unlockUsers}
                        onCheckedChange={(checked) => 
                          setNewUser({
                            ...newUser,
                            permissions: { ...newUser.permissions, unlockUsers: !!checked }
                          })
                        }
                      />
                      <Label htmlFor="unlockUsers" className="text-sm">Kullanicilari Kilidi Ac</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="manageSettings"
                        checked={newUser.permissions.manageSettings}
                        onCheckedChange={(checked) => 
                          setNewUser({
                            ...newUser,
                            permissions: { ...newUser.permissions, manageSettings: !!checked }
                          })
                        }
                      />
                      <Label htmlFor="manageSettings" className="text-sm">Ayarlari Yonet</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="manageUsers"
                        checked={newUser.permissions.manageUsers}
                        onCheckedChange={(checked) => 
                          setNewUser({
                            ...newUser,
                            permissions: { ...newUser.permissions, manageUsers: !!checked }
                          })
                        }
                      />
                      <Label htmlFor="manageUsers" className="text-sm">Kullanicilari Yonet</Label>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Iptal
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {editingUser ? 'Guncelle' : 'Olustur'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {message && (
          <Alert className={`mb-6 ${messageType === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
            {messageType === 'success' ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <XCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertDescription className={messageType === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message}
            </AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Kullanicilar</CardTitle>
            <CardDescription>
              Sistemdeki tum kullanicilar ve yetkileri
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Kullanici Adi</TableHead>
                    <TableHead>Rol</TableHead>
                    <TableHead>Yetkiler</TableHead>
                    <TableHead>Olusturma Tarihi</TableHead>
                    <TableHead>Son Giris</TableHead>
                    <TableHead>Islemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.username}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          user.role === 'admin' 
                            ? 'bg-red-100 text-red-800' 
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {user.role === 'admin' ? 'Admin' : 'Kullanici'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {user.permissions.viewUsers && (
                            <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Goruntule</span>
                          )}
                          {user.permissions.unlockUsers && (
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">Kilidi Ac</span>
                          )}
                          {user.permissions.manageSettings && (
                            <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs">Ayarlar</span>
                          )}
                          {user.permissions.manageUsers && (
                            <span className="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs">Kullanici Yonetimi</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(user.createdAt)}</TableCell>
                      <TableCell>{user.lastLogin ? formatDate(user.lastLogin) : 'Hic giris yapmadi'}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditUser(user)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
