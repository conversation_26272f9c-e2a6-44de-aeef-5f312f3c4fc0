module.exports = {

"[project]/.next-internal/server/app/api/password-expiry/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createUser": (()=>createUser),
    "deleteUser": (()=>deleteUser),
    "getUserById": (()=>getUserById),
    "getUserByUsername": (()=>getUserByUsername),
    "getUsers": (()=>getUsers),
    "hashPassword": (()=>hashPassword),
    "initializeUsers": (()=>initializeUsers),
    "saveUsers": (()=>saveUsers),
    "updateLastLogin": (()=>updateLastLogin),
    "updateUser": (()=>updateUser),
    "validatePassword": (()=>validatePassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
const USERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'users.json');
// Default admin user
const defaultAdmin = {
    id: '1',
    username: 'admin',
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hashSync('581326Ob', 10),
    role: 'admin',
    permissions: {
        viewUsers: true,
        unlockUsers: true,
        manageSettings: true,
        manageUsers: true
    },
    createdAt: new Date().toISOString()
};
function initializeUsers() {
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(USERS_FILE)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(USERS_FILE, JSON.stringify([
            defaultAdmin
        ], null, 2));
    }
}
function getUsers() {
    try {
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(USERS_FILE)) {
            initializeUsers();
        }
        const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(USERS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error reading users file:', error);
        return [
            defaultAdmin
        ];
    }
}
function saveUsers(users) {
    try {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
    } catch (error) {
        console.error('Error saving users file:', error);
        throw new Error('Failed to save users');
    }
}
function getUserByUsername(username) {
    const users = getUsers();
    return users.find((user)=>user.username === username) || null;
}
function getUserById(id) {
    const users = getUsers();
    return users.find((user)=>user.id === id) || null;
}
function validatePassword(password, hashedPassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compareSync(password, hashedPassword);
}
function hashPassword(password) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hashSync(password, 10);
}
function createUser(userData) {
    const users = getUsers();
    // Check if username already exists
    if (users.find((user)=>user.username === userData.username)) {
        throw new Error('Username already exists');
    }
    const newUser = {
        ...userData,
        id: Date.now().toString(),
        password: hashPassword(userData.password),
        createdAt: new Date().toISOString()
    };
    users.push(newUser);
    saveUsers(users);
    return newUser;
}
function updateUser(id, updates) {
    const users = getUsers();
    const userIndex = users.findIndex((user)=>user.id === id);
    if (userIndex === -1) {
        throw new Error('User not found');
    }
    // If password is being updated, hash it
    if (updates.password) {
        updates.password = hashPassword(updates.password);
    }
    users[userIndex] = {
        ...users[userIndex],
        ...updates
    };
    saveUsers(users);
    return users[userIndex];
}
function deleteUser(id) {
    const users = getUsers();
    const userIndex = users.findIndex((user)=>user.id === id);
    if (userIndex === -1) {
        return false;
    }
    // Don't allow deleting the default admin
    if (users[userIndex].username === 'admin') {
        throw new Error('Cannot delete default admin user');
    }
    users.splice(userIndex, 1);
    saveUsers(users);
    return true;
}
function updateLastLogin(userId) {
    const users = getUsers();
    const userIndex = users.findIndex((user)=>user.id === userId);
    if (userIndex !== -1) {
        users[userIndex].lastLogin = new Date().toISOString();
        saveUsers(users);
    }
}
}}),
"[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>handler),
    "POST": (()=>handler),
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
;
// Initialize users file on startup
(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initializeUsers"])();
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                username: {
                    label: 'Username',
                    type: 'text'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.username || !credentials?.password) {
                    return null;
                }
                const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUserByUsername"])(credentials.username);
                if (!user) {
                    return null;
                }
                const isValidPassword = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validatePassword"])(credentials.password, user.password);
                if (!isValidPassword) {
                    return null;
                }
                // Update last login
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateLastLogin"])(user.id);
                return {
                    id: user.id,
                    name: user.username,
                    email: user.username,
                    role: user.role,
                    permissions: user.permissions
                };
            }
        })
    ],
    callbacks: {
        async jwt ({ token, user, trigger, session }) {
            if (user) {
                token.role = user.role;
                token.permissions = user.permissions;
            }
            // Handle session updates
            if (trigger === 'update' && session) {
                token.role = session.role;
                token.permissions = session.permissions;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.sub;
                session.user.role = token.role;
                session.user.permissions = token.permissions;
            }
            return session;
        }
    },
    pages: {
        signIn: '/login'
    },
    session: {
        strategy: 'jwt'
    },
    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here'
};
const handler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(authOptions);
;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[project]/src/app/api/password-expiry/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$api$2f$auth$2f5b2e2e2e$nextauth$5d2f$route$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ldapjs$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ldapjs/lib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
;
;
function getLdapSettings() {
    try {
        const settingsPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'ldap-settings.json');
        const settingsData = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(settingsPath, 'utf8');
        return JSON.parse(settingsData);
    } catch (error) {
        console.error('Error reading LDAP settings:', error);
        throw new Error('LDAP settings not found');
    }
}
function convertWindowsTimeToDate(windowsTime) {
    try {
        const windowsTimeNum = parseInt(windowsTime);
        if (windowsTimeNum === 0 || isNaN(windowsTimeNum)) return null;
        // Windows FILETIME epoch starts at January 1, 1601
        // JavaScript Date epoch starts at January 1, 1970
        // Difference is 11644473600 seconds
        const unixTime = windowsTimeNum / 10000000 - 11644473600;
        return new Date(unixTime * 1000);
    } catch  {
        return null;
    }
}
function calculatePasswordExpiry(pwdLastSet, maxPasswordAge) {
    const lastSetDate = convertWindowsTimeToDate(pwdLastSet);
    if (!lastSetDate) return null;
    // maxPasswordAge is in 100-nanosecond intervals (negative value)
    const maxAgeDays = Math.abs(maxPasswordAge) / (10000000 * 60 * 60 * 24);
    const expiryDate = new Date(lastSetDate);
    expiryDate.setDate(expiryDate.getDate() + maxAgeDays);
    return expiryDate;
}
async function getExpiredUsers() {
    const settings = getLdapSettings();
    return new Promise((resolve, reject)=>{
        const ldapUrl = `${settings.useSSL ? 'ldaps' : 'ldap'}://${settings.server}:${settings.port}`;
        console.log('Connecting to LDAP for password expiry check:', ldapUrl);
        const client = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ldapjs$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].createClient({
            url: ldapUrl,
            timeout: 10000,
            connectTimeout: 10000
        });
        client.bind(settings.username, settings.password, (bindErr)=>{
            if (bindErr) {
                console.error('LDAP bind error:', bindErr);
                client.destroy();
                return reject(new Error('LDAP authentication failed'));
            }
            console.log('LDAP bind successful, searching for users with expired passwords...');
            // First, get domain password policy
            const domainSearchOptions = {
                scope: 'base',
                filter: '(objectClass=domain)',
                attributes: [
                    'maxPwdAge'
                ]
            };
            client.search(settings.baseDN, domainSearchOptions, (domainErr, domainRes)=>{
                if (domainErr) {
                    console.error('Domain search error:', domainErr);
                    client.destroy();
                    return reject(new Error('Failed to get domain password policy'));
                }
                let maxPasswordAge = 0;
                domainRes.on('searchEntry', (entry)=>{
                    const maxPwdAge = entry.pojo.attributes.find((attr)=>attr.type === 'maxPwdAge');
                    if (maxPwdAge && maxPwdAge.values && maxPwdAge.values[0]) {
                        maxPasswordAge = parseInt(maxPwdAge.values[0]);
                    }
                });
                domainRes.on('end', ()=>{
                    if (maxPasswordAge === 0) {
                        console.log('Password expiry is disabled in domain policy');
                        client.destroy();
                        return resolve({
                            users: [],
                            stats: {
                                totalExpiredUsers: 0,
                                expiredToday: 0,
                                expiredThisWeek: 0,
                                expiredThisMonth: 0
                            }
                        });
                    }
                    // Now search for users
                    // Filter explanation:
                    // - objectClass=user: Only user objects
                    // - objectCategory=person: Only person objects (excludes computer accounts)
                    // - !(userAccountControl:1.2.840.113556.1.4.803:=2): Exclude disabled accounts
                    const userSearchOptions = {
                        scope: 'sub',
                        filter: '(&(objectClass=user)(objectCategory=person)(!(userAccountControl:1.2.840.113556.1.4.803:=2)))',
                        attributes: [
                            'sAMAccountName',
                            'displayName',
                            'pwdLastSet',
                            'lastLogon',
                            'department',
                            'userAccountControl'
                        ]
                    };
                    client.search(settings.baseDN, userSearchOptions, (userErr, userRes)=>{
                        if (userErr) {
                            console.error('User search error:', userErr);
                            client.destroy();
                            return reject(new Error('Failed to search users'));
                        }
                        const expiredUsers = [];
                        const now = new Date();
                        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                        userRes.on('searchEntry', (entry)=>{
                            try {
                                const attributes = entry.pojo.attributes;
                                const username = attributes.find((attr)=>attr.type === 'sAMAccountName')?.values[0];
                                const displayName = attributes.find((attr)=>attr.type === 'displayName')?.values[0] || username;
                                const pwdLastSet = attributes.find((attr)=>attr.type === 'pwdLastSet')?.values[0];
                                const lastLogon = attributes.find((attr)=>attr.type === 'lastLogon')?.values[0];
                                const department = attributes.find((attr)=>attr.type === 'department')?.values[0];
                                const userAccountControl = attributes.find((attr)=>attr.type === 'userAccountControl')?.values[0];
                                if (!username || !pwdLastSet || pwdLastSet === '0') return;
                                // Check if password never expires (DONT_EXPIRE_PASSWORD flag = 0x10000 = 65536)
                                if (userAccountControl) {
                                    const uacValue = parseInt(userAccountControl);
                                    const DONT_EXPIRE_PASSWORD = 0x10000; // 65536
                                    if (uacValue & DONT_EXPIRE_PASSWORD) {
                                        console.log(`✅ Skipping user ${username} - password never expires (UAC: ${uacValue})`);
                                        return; // Skip users with password never expires
                                    } else {
                                        console.log(`🔍 Checking user ${username} - password can expire (UAC: ${uacValue})`);
                                    }
                                } else {
                                    console.log(`⚠️ User ${username} - no userAccountControl found`);
                                }
                                const passwordExpiry = calculatePasswordExpiry(pwdLastSet, maxPasswordAge);
                                if (!passwordExpiry) return;
                                // Check if password has expired
                                if (passwordExpiry < now) {
                                    const daysSinceExpiry = Math.floor((now.getTime() - passwordExpiry.getTime()) / (1000 * 60 * 60 * 24));
                                    console.log(`Found expired user: ${username}, expired ${daysSinceExpiry} days ago`);
                                    expiredUsers.push({
                                        username,
                                        displayName,
                                        passwordExpires: passwordExpiry.toISOString(),
                                        daysSinceExpiry,
                                        lastLogon: lastLogon && lastLogon !== '0' ? convertWindowsTimeToDate(lastLogon)?.toISOString() : undefined,
                                        department
                                    });
                                }
                            } catch (error) {
                                console.error('Error processing user entry:', error);
                            }
                        });
                        userRes.on('end', ()=>{
                            client.destroy();
                            // Calculate stats
                            const stats = {
                                totalExpiredUsers: expiredUsers.length,
                                expiredToday: expiredUsers.filter((user)=>{
                                    const expiry = new Date(user.passwordExpires);
                                    return expiry >= today && expiry < new Date(today.getTime() + 24 * 60 * 60 * 1000);
                                }).length,
                                expiredThisWeek: expiredUsers.filter((user)=>{
                                    const expiry = new Date(user.passwordExpires);
                                    return expiry >= weekAgo;
                                }).length,
                                expiredThisMonth: expiredUsers.filter((user)=>{
                                    const expiry = new Date(user.passwordExpires);
                                    return expiry >= monthAgo;
                                }).length
                            };
                            // Sort by days since expiry (most recent first)
                            expiredUsers.sort((a, b)=>a.daysSinceExpiry - b.daysSinceExpiry);
                            console.log(`Found ${expiredUsers.length} users with expired passwords`);
                            resolve({
                                users: expiredUsers,
                                stats
                            });
                        });
                        userRes.on('error', (error)=>{
                            console.error('User search result error:', error);
                            client.destroy();
                            reject(new Error('Error during user search'));
                        });
                    });
                });
                domainRes.on('error', (error)=>{
                    console.error('Domain search result error:', error);
                    client.destroy();
                    reject(new Error('Error during domain search'));
                });
            });
        });
        client.on('error', (error)=>{
            console.error('LDAP client error:', error);
            reject(new Error('LDAP connection failed'));
        });
    });
}
async function GET(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$api$2f$auth$2f5b2e2e2e$nextauth$5d2f$route$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || !session.user.permissions.unlockUsers) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        const result = await getExpiredUsers();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('Password expiry API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error.message || 'Failed to get expired users'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e3770b75._.js.map