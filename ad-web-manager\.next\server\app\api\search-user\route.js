(()=>{var e={};e.id=102,e.ids=[102],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5792:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GET:()=>u,POST:()=>u,authOptions:()=>a});var t=s(19854),n=s.n(t),o=s(13581),i=s(12909);(0,i.lK)();let a={providers:[(0,o.A)({name:"credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;let r=(0,i.JE)(e.username);return r&&(0,i.Oj)(e.password,r.password)?((0,i.bx)(r.id),{id:r.id,name:r.username,email:r.username,role:r.role,permissions:r.permissions}):null}})],callbacks:{jwt:async({token:e,user:r,trigger:s,session:t})=>(r&&(e.role=r.role,e.permissions=r.permissions),"update"===s&&t&&(e.role=t.role,e.permissions=t.permissions),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.permissions=r.permissions),e)},pages:{signIn:"/login"},session:{strategy:"jwt"},secret:process.env.NEXTAUTH_SECRET||"your-secret-key-here"},u=n()(a)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{JE:()=>p,Oj:()=>f,TK:()=>h,bx:()=>x,hG:()=>w,kg:()=>g,kl:()=>m,lK:()=>l,lo:()=>c});var t=s(85663),n=s(29021),o=s.n(n),i=s(33873);let a=s.n(i)().join(process.cwd(),"users.json"),u={id:"1",username:"admin",password:"$2b$10$t5.K7vyPesCG5IR5F5TkUuSSM/QNlar8Wr243/uQuG0yyrw1mJbBK",role:"admin",permissions:{viewUsers:!0,unlockUsers:!0,manageSettings:!0,manageUsers:!0},createdAt:new Date().toISOString()};function l(){o().existsSync(a)||o().writeFileSync(a,JSON.stringify([u],null,2))}function c(){try{o().existsSync(a)||l();let e=o().readFileSync(a,"utf8");return JSON.parse(e)}catch(e){return console.error("Error reading users file:",e),[u]}}function d(e){try{o().writeFileSync(a,JSON.stringify(e,null,2))}catch(e){throw console.error("Error saving users file:",e),Error("Failed to save users")}}function p(e){return c().find(r=>r.username===e)||null}function m(e){return c().find(r=>r.id===e)||null}function f(e,r){return t.Ay.compareSync(e,r)}function y(e){return t.Ay.hashSync(e,10)}function g(e){let r=c();if(r.find(r=>r.username===e.username))throw Error("Username already exists");let s={...e,id:Date.now().toString(),password:y(e.password),createdAt:new Date().toISOString()};return r.push(s),d(r),s}function h(e,r){let s=c(),t=s.findIndex(r=>r.id===e);if(-1===t)throw Error("User not found");return r.password&&(r.password=y(r.password)),s[t]={...s[t],...r},d(s),s[t]}function w(e){let r=c(),s=r.findIndex(r=>r.id===e);if(-1===s)return!1;if("admin"===r[s].username)throw Error("Cannot delete default admin user");return r.splice(s,1),d(r),!0}function x(e){let r=c(),s=r.findIndex(r=>r.id===e);-1!==s&&(r[s].lastLogin=new Date().toISOString(),d(r))}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38078:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>b,routeModule:()=>x,serverHooks:()=>A,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>v});var t={};s.r(t),s.d(t,{GET:()=>w});var n=s(96559),o=s(48088),i=s(37719),a=s(32190),u=s(19854),l=s(5792),c=s(73010),d=s.n(c),p=s(29021),m=s.n(p),f=s(33873),y=s.n(f);function g(e){try{let r=parseInt(e);if(0===r||isNaN(r))return null;return new Date(1e3*(r/1e7-0x2b6109100))}catch{return null}}async function h(e){let r=function(){try{let e=y().join(process.cwd(),"ldap-settings.json"),r=m().readFileSync(e,"utf8");return JSON.parse(r)}catch(e){throw console.error("Error reading LDAP settings:",e),Error("LDAP settings not found")}}();return new Promise((s,t)=>{let n=`${r.useSSL?"ldaps":"ldap"}://${r.server}:${r.port}`;console.log("Searching for user:",e);let o=d().createClient({url:n,timeout:1e4,connectTimeout:1e4});o.bind(r.username,r.password,n=>{if(n)return console.error("LDAP bind error:",n),o.destroy(),t(Error("LDAP authentication failed"));o.search(r.baseDN,{scope:"base",filter:"(objectClass=domain)",attributes:["maxPwdAge"]},(n,i)=>{if(n)return console.error("Domain search error:",n),o.destroy(),t(Error("Failed to get domain password policy"));let a=0;i.on("searchEntry",e=>{let r=e.pojo.attributes.find(e=>"maxPwdAge"===e.type);r&&r.values&&r.values[0]&&(a=parseInt(r.values[0]))}),i.on("end",()=>{let n={scope:"sub",filter:`(sAMAccountName=${e})`,attributes:["sAMAccountName","displayName","pwdLastSet","lastLogon","department","userAccountControl"]};o.search(r.baseDN,n,(e,r)=>{if(e)return console.error("User search error:",e),o.destroy(),t(Error("Failed to search user"));let n=null;r.on("searchEntry",e=>{try{let r,s=e.pojo.attributes,t=s.find(e=>"sAMAccountName"===e.type)?.values[0],o=s.find(e=>"displayName"===e.type)?.values[0]||t,i=s.find(e=>"pwdLastSet"===e.type)?.values[0],u=s.find(e=>"lastLogon"===e.type)?.values[0],l=s.find(e=>"department"===e.type)?.values[0],c=s.find(e=>"userAccountControl"===e.type)?.values[0];if(!t)return;let d=c?parseInt(c):0,p=!!(2&d),m=!!(65536&d),f=null;if(!m&&i&&"0"!==i&&0!==a&&(f=function(e,r){let s=g(e);if(!s)return null;let t=Math.abs(r)/864e9,n=new Date(s);return n.setDate(n.getDate()+t),n}(i,a))){let e=new Date,s=f.getTime()-e.getTime();r=Math.ceil(s/864e5)}n={username:t,displayName:o,passwordExpires:f?.toISOString(),daysUntilExpiry:r,lastLogon:u&&"0"!==u?g(u)?.toISOString():void 0,department:l,passwordNeverExpires:m,accountDisabled:p},console.log(`Found user: ${t}, disabled: ${p}, never expires: ${m}, days until expiry: ${r}`)}catch(e){console.error("Error processing user entry:",e)}}),r.on("end",()=>{o.destroy(),s(n)}),r.on("error",e=>{console.error("User search result error:",e),o.destroy(),t(Error("Error during user search"))})})}),i.on("error",e=>{console.error("Domain search result error:",e),o.destroy(),t(Error("Error during domain search"))})})}),o.on("error",e=>{console.error("LDAP client error:",e),t(Error("LDAP connection failed"))})})}async function w(e){try{let r=await (0,u.getServerSession)(l.authOptions);if(!r||!r.user.permissions.unlockUsers)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url),t=s.get("username");if(!t)return a.NextResponse.json({error:"Username parameter is required"},{status:400});let n=await h(t);if(!n)return a.NextResponse.json({error:"User not found"},{status:404});return a.NextResponse.json({user:n})}catch(e){return console.error("Search user API error:",e),a.NextResponse.json({error:e.message||"Failed to search user"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/search-user/route",pathname:"/api/search-user",filename:"route",bundlePath:"app/api/search-user/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\search-user\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:S,workUnitAsyncStorage:v,serverHooks:A}=x;function b(){return(0,i.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:v})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73010:e=>{"use strict";e.exports=require("ldapjs")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580,854,427],()=>s(38078));module.exports=t})();