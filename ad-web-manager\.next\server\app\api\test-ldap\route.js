(()=>{var e={};e.id=48,e.ids=[48],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40153:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>D,routeModule:()=>A,serverHooks:()=>j,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>P});var s={};t.r(s),t.d(s,{GET:()=>x});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),u=t(73010),p=t.n(u),l=t(29021),c=t.n(l),d=t(33873);let m=t.n(d)().join(process.cwd(),"ldap-settings.json");async function x(){try{let e=function(){try{if(c().existsSync(m)){let e=c().readFileSync(m,"utf8");return JSON.parse(e)}return null}catch(e){return console.error("Error loading settings:",e),null}}();if(!e)return i.NextResponse.json({error:"LDAP ayarları bulunamadı"},{status:400});let r=e.useSSL?"ldaps":"ldap",t=`${r}://${e.server}:${e.port}`;console.log(`Testing LDAP connection to: ${t}`);let s=p().createClient({url:t,timeout:1e4,connectTimeout:5e3,tlsOptions:{rejectUnauthorized:!1}});return new Promise(r=>{let o=!1,n=e=>{o||(o=!0,s.destroy(),r(e))},a=setTimeout(()=>{console.error("LDAP test timeout"),n(i.NextResponse.json({error:"LDAP bağlantı testi zaman aşımına uğradı"},{status:408}))},15e3);s.on("error",e=>{clearTimeout(a),console.error("LDAP test connection error:",e),n(i.NextResponse.json({error:`LDAP bağlantı hatası: ${e.message}`},{status:500}))}),s.bind(e.username,e.password,r=>{if(clearTimeout(a),r){console.error("LDAP test bind error:",r),n(i.NextResponse.json({error:`LDAP kimlik doğrulama hatası: ${r.message}`},{status:401}));return}console.log("LDAP test bind successful"),n(i.NextResponse.json({success:!0,message:"LDAP bağlantısı başarılı",server:t,baseDN:e.baseDN}))})})}catch(e){return console.error("LDAP test error:",e),i.NextResponse.json({error:`Test hatası: ${e.message}`},{status:500})}}let A=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/test-ldap/route",pathname:"/api/test-ldap",filename:"route",bundlePath:"app/api/test-ldap/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\test-ldap\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:P,serverHooks:j}=A;function D(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:P})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73010:e=>{"use strict";e.exports=require("ldapjs")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580],()=>t(40153));module.exports=s})();