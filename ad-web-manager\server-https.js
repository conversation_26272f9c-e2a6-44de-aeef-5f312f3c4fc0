const { createServer } = require('https');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = 3000;

// Initialize Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

// Self-signed certificate for development
const httpsOptions = {
  key: `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wEiOfH3nzor9cwHXLbkiG+2XgQXpM6EA4Og49wZGrp68Y0rsPiJ5bYB4fS2U
YDAQab+2cINhw6NpycGgqDrfqeMDdCdNumber+7rhfAuisNaVdMReHHZ2cT+zDnvZp
YzBBjFffL6xzFcxDyESHVVfmZBVcUlWomkVzjlxtHzNqyDobfFBOkVrAEpJ8ExVcGUEOsmEFrpMmPiMYZNujcHdyR5AQUVM
-----END PRIVATE KEY-----`,
  cert: `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/OvD/XuWMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMTYxMjI4MjE0MjM1WhcNMjYxMjI2MjE0MjM1WjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAu1SU1L7VLPHCgcBIjnx9586K/XMB1y25IhvtlYEF6TOhAODoOPcGRq6e
vGNK7D4ieW2AeH0tlGAwEGm/tnCDYcOjacnBoKg636njA3QnTa6+7rhfAuisNaVd
MReHHZ2cT+zDnvZpYzBBjFffL6xzFcxDyESHVVfmZBVcUlWomkVzjlxtHzNqyDob
fFBOkVrAEpJ8ExVcGUEOsmEFrpMmPiMYZNujcHdyR5AQUVM
-----END CERTIFICATE-----`
};

app.prepare().then(() => {
  createServer(httpsOptions, async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  })
  .once('error', (err) => {
    console.error(err);
    process.exit(1);
  })
  .listen(port, () => {
    console.log(`> Ready on https://${hostname}:${port}`);
  });
});
