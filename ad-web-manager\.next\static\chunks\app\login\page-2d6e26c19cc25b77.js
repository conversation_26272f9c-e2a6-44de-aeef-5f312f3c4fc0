(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var a=t(5155);t(2115);var s=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:i,asChild:l=!1,...o}=e,c=l?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:t,size:i,className:r})),...o})}},968:(e,r,t)=>{"use strict";t.d(r,{b:()=>d});var a=t(2115),s=t(3655),i=t(5155),n=a.forwardRef((e,r)=>(0,i.jsx)(s.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var d=n},1154:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var a=t(5155);t(2115);var s=t(9434);function i(e){let{className:r,type:t,...i}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...i})}},3962:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var a=t(5155),s=t(2115),i=t(2108),n=t(8999),d=t(6695),l=t(285),o=t(2523),c=t(5057),u=t(5365),g=t(4861),v=t(1154);function p(){let[e,r]=(0,s.useState)(""),[t,p]=(0,s.useState)(""),[x,f]=(0,s.useState)(!1),[m,b]=(0,s.useState)(""),h=(0,n.useRouter)(),y=async r=>{r.preventDefault(),f(!0),b("");try{let r=await (0,i.signIn)("credentials",{username:e,password:t,redirect:!1,callbackUrl:"/"});(null==r?void 0:r.error)?b("Kullanıcı adı veya şifre hatalı!"):(null==r?void 0:r.ok)&&await (0,i.getSession)()?(h.push("/"),h.refresh()):b("Giriş başarısız!")}catch(e){b("Bir hata oluştu!")}finally{f(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",suppressHydrationWarning:!0,children:(0,a.jsx)("div",{className:"w-full max-w-md",children:(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)("img",{src:"/bayraktar_holding_logo.jpeg",alt:"Bayraktar Holding Logo",width:150,height:150,className:"rounded-lg object-contain"})}),(0,a.jsx)(d.ZB,{className:"text-2xl",children:"AD Web Manager"}),(0,a.jsx)(d.BT,{children:"Sisteme giriş yapmak i\xe7in kullanıcı bilgilerinizi girin"})]}),(0,a.jsxs)(d.Wu,{children:[m&&(0,a.jsxs)(u.Fc,{className:"mb-6 border-red-200 bg-red-50",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)(u.TN,{className:"text-red-800",children:m})]}),(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",suppressHydrationWarning:!0,children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"username",children:"Kullanıcı Adı"}),(0,a.jsx)(o.p,{id:"username",type:"text",value:e,onChange:e=>r(e.target.value),placeholder:"Kullanıcı adınızı girin",required:!0,disabled:x,suppressHydrationWarning:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"password",children:"Şifre"}),(0,a.jsx)(o.p,{id:"password",type:"password",value:t,onChange:e=>p(e.target.value),placeholder:"Şifrenizi girin",required:!0,disabled:x,suppressHydrationWarning:!0})]}),(0,a.jsxs)(l.$,{type:"submit",className:"w-full",disabled:x,children:[x&&(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4 animate-spin"}),x?"Giriş yapılıyor...":"Giriş Yap"]})]})]})]})})})}},4861:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5057:(e,r,t)=>{"use strict";t.d(r,{J:()=>n});var a=t(5155);t(2115);var s=t(968),i=t(9434);function n(e){let{className:r,...t}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},5365:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>d,TN:()=>l});var a=t(5155);t(2115);var s=t(2085),i=t(9434);let n=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:r,variant:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),r),...s})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",r),...t})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=t(5155);t(2115);var s=t(9434);function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...t})}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var a=t(2596),s=t(9688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},9593:(e,r,t)=>{Promise.resolve().then(t.bind(t,3962))}},e=>{var r=r=>e(e.s=r);e.O(0,[108,460,441,684,358],()=>r(9593)),_N_E=e.O()}]);