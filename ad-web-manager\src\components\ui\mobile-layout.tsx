'use client';

import React from 'react';
import { MobileNav } from './mobile-nav';

interface MobileLayoutProps {
  children: React.ReactNode;
  currentPath?: string;
  title?: string;
  showBackButton?: boolean;
}

export function MobileLayout({ 
  children, 
  currentPath = '/', 
  title,
  showBackButton = false 
}: MobileLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <MobileNav currentPath={currentPath} />
      
      {/* Mobile-specific header */}
      <div className="md:hidden">
        {title && (
          <div className="border-b bg-card px-4 py-3">
            <div className="flex items-center justify-between">
              {showBackButton && (
                <button 
                  onClick={() => window.history.back()}
                  className="p-2 hover:bg-accent rounded-md"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              <h1 className="text-lg font-semibold text-foreground flex-1 text-center">
                {title}
              </h1>
              <div className="w-9" /> {/* Spacer for centering */}
            </div>
          </div>
        )}
      </div>
      
      {/* Main content */}
      <main className="pb-safe">
        {children}
      </main>
    </div>
  );
}

// Mobile-optimized container
export function MobileContainer({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <div className={`container mx-auto px-4 py-4 sm:py-6 ${className}`}>
      {children}
    </div>
  );
}

// Mobile-optimized card grid
export function MobileCardGrid({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 ${className}`}>
      {children}
    </div>
  );
}

// Mobile-optimized table wrapper
export function MobileTableWrapper({ 
  children 
}: { 
  children: React.ReactNode; 
}) {
  return (
    <div className="overflow-x-auto -mx-4 sm:mx-0">
      <div className="inline-block min-w-full align-middle">
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
          {children}
        </div>
      </div>
    </div>
  );
}

// Mobile-optimized button group
export function MobileButtonGroup({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <div className={`flex flex-col sm:flex-row gap-2 sm:gap-4 ${className}`}>
      {children}
    </div>
  );
}
