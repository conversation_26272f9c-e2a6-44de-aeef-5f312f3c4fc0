@echo off
echo Starting AD Web Manager in Production Mode...
echo.

REM Set production environment
set NODE_ENV=production

REM Build the application
echo Building application...
npm run build

REM Check if build was successful
if %ERRORLEVEL% neq 0 (
    echo Build failed! Please check the errors above.
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.

REM Start the production server on port 80
echo Starting production server on port 80...
echo Access the application at: http://yourdomain.com
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start with port 80 (requires admin privileges)
npm start -- -p 80

pause
