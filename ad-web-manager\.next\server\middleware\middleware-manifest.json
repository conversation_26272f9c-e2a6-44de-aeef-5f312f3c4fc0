{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ioihxw0T6uLY6tVdvT/KSIElCQ4mXNoUEabkMtzHbR4=", "__NEXT_PREVIEW_MODE_ID": "7c2fe6a5c2176bc420b150e36f5cc837", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0c9c3e6ed345e03a4c835393145b0087ab42dd48a482299328510cd0f27847cf", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c92a77bc105aa282e2fea01a26bbc95b2a89b9545642b4ad64f701f3334b6f0b"}}}, "instrumentation": null, "functions": {}}