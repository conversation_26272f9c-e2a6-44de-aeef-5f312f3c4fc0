@echo off
title AD Web Manager Server
color 0A

echo.
echo ========================================
echo    AD Web Manager Server Baslatiliyor
echo ========================================
echo.

REM Gerekli dizine git
cd /d "%~dp0"

REM Node.js ve npm kontrolu
echo [1/4] Node.js kontrolu yapiliyor...
node --version >nul 2>&1
if errorlevel 1 (
    echo HATA: Node.js bulunamadi! Lutfen Node.js yukleyin.
    echo https://nodejs.org/
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo HATA: npm bulunamadi! Lutfen Node.js yukleyin.
    pause
    exit /b 1
)

echo Node.js ve npm basariyla bulundu.
echo.

REM Dependencies kontrolu
echo [2/4] Dependencies kontrolu yapiliyor...
if not exist "node_modules" (
    echo Dependencies bulunamadi. Yukleniyor...
    npm install
    if errorlevel 1 (
        echo HATA: Dependencies yuklenemedi!
        pause
        exit /b 1
    )
) else (
    echo Dependencies mevcut.
)
echo.

REM LDAP ayarlari kontrolu
echo [3/4] LDAP ayarlari kontrolu yapiliyor...
if not exist "ldap-settings.json" (
    echo UYARI: LDAP ayarlari bulunamadi!
    echo Varsayilan ayarlar olusturuluyor...
    echo {> ldap-settings.json
    echo   "server": "10.20.2.40",>> ldap-settings.json
    echo   "port": "389",>> ldap-settings.json
    echo   "baseDN": "DC=egefrn,DC=bayraktar,DC=com",>> ldap-settings.json
    echo   "username": "<EMAIL>",>> ldap-settings.json
    echo   "password": "Ebt1991.,",>> ldap-settings.json
    echo   "useSSL": false>> ldap-settings.json
    echo }>> ldap-settings.json
    echo LDAP ayarlari olusturuldu.
) else (
    echo LDAP ayarlari mevcut.
)
echo.

REM Environment dosyasi kontrolu
if not exist ".env.local" (
    echo Environment dosyasi olusturuluyor...
    echo NEXTAUTH_SECRET=your-secret-key-here-change-this-in-production> .env.local
    echo NEXTAUTH_URL=http://localhost:3000>> .env.local
)

echo [4/4] Server baslatiliyor...
echo.
echo ========================================
echo   Server http://localhost:3000 adresinde
echo   Kapatmak icin Ctrl+C basin
echo ========================================
echo.

REM Development server'i baslat
npm run dev

REM Server kapandiginda
echo.
echo Server kapatildi.
pause
