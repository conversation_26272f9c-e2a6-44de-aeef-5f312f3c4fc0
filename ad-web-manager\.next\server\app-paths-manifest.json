{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/refresh-session/route": "app/api/auth/refresh-session/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/extend-password/route": "app/api/extend-password/route.js", "/api/locked-users/route": "app/api/locked-users/route.js", "/api/auth/invalidate-sessions/route": "app/api/auth/invalidate-sessions/route.js", "/api/settings/route": "app/api/settings/route.js", "/api/password-expiry/route": "app/api/password-expiry/route.js", "/api/test-connection/route": "app/api/test-connection/route.js", "/api/search-user/route": "app/api/search-user/route.js", "/api/test-ldap/route": "app/api/test-ldap/route.js", "/api/users/route": "app/api/users/route.js", "/api/unlock-user/route": "app/api/unlock-user/route.js", "/api/unlock-user-ps/route": "app/api/unlock-user-ps/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/api/debug/session/route": "app/api/debug/session/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/login/page": "app/login/page.js", "/settings/page": "app/settings/page.js", "/password-expiry/page": "app/password-expiry/page.js", "/page": "app/page.js", "/manage-users/page": "app/manage-users/page.js", "/users/page": "app/users/page.js"}