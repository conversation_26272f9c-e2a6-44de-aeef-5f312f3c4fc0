(()=>{var e={};e.id=656,e.ids=[656],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63816:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>k,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{POST:()=>l});var o=s(96559),u=s(48088),i=s(37719),n=s(32190),a=s(19854);let p=require("child_process"),c=(0,s(28354).promisify)(p.exec);async function l(e){try{let r=await (0,a.getServerSession)();if(!r?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});if(!r.user.permissions?.unlockUsers)return n.NextResponse.json({error:"Insufficient permissions"},{status:403});let{username:s}=await e.json();if(!s)return n.NextResponse.json({error:"Kullanıcı adı gereklidir"},{status:400});let t=`Unlock-ADAccount -Identity "${s}"`;try{let{stdout:e,stderr:r}=await c(`powershell -Command "${t}"`,{timeout:15e3});return n.NextResponse.json({success:!0,message:`${s} kullanıcısı PowerShell ile unlock edildi`})}catch(e){return n.NextResponse.json({error:`PowerShell AD mod\xfcl\xfc mevcut değil veya erişim yok`},{status:500})}}catch(e){return console.error("PowerShell unlock error:",e),n.NextResponse.json({error:`PowerShell unlock hatası: ${e.message}`},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/unlock-user-ps/route",pathname:"/api/unlock-user-ps",filename:"route",bundlePath:"app/api/unlock-user-ps/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\unlock-user-ps\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:k}=d;function h(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580,854],()=>s(63816));module.exports=t})();