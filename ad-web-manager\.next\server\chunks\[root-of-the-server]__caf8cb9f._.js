module.exports = {

"[project]/.next-internal/server/app/api/extend-password/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/app/api/extend-password/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ldapjs$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ldapjs/lib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
;
const SETTINGS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'ldap-settings.json');
function loadSettings() {
    try {
        if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(SETTINGS_FILE)) {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(SETTINGS_FILE, 'utf8');
            return JSON.parse(data);
        }
        return null;
    } catch (error) {
        console.error('Error loading settings:', error);
        return null;
    }
}
async function POST(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])();
        if (!session?.user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        const { username } = await request.json();
        if (!username) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Kullanıcı adı gereklidir'
            }, {
                status: 400
            });
        }
        console.log(`Starting password extension for user: ${username}`);
        const settings = loadSettings();
        if (!settings) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'LDAP ayarları bulunamadı. Lütfen önce ayarları yapılandırın.'
            }, {
                status: 400
            });
        }
        if (!settings.server || !settings.baseDN || !settings.username || !settings.password) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'LDAP ayarları eksik. Lütfen ayarları kontrol edin.'
            }, {
                status: 400
            });
        }
        const protocol = settings.useSSL ? 'ldaps' : 'ldap';
        const url = `${protocol}://${settings.server}:${settings.port}`;
        console.log(`Attempting LDAP connection to: ${url}`);
        const client = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ldapjs$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].createClient({
            url: url,
            timeout: 60000,
            connectTimeout: 20000,
            tlsOptions: {
                rejectUnauthorized: false
            }
        });
        return new Promise((resolve)=>{
            let isResolved = false;
            const resolveOnce = (response)=>{
                if (!isResolved) {
                    isResolved = true;
                    client.destroy();
                    resolve(response);
                }
            };
            const timeout = setTimeout(()=>{
                console.error(`LDAP timeout for user: ${username}`);
                resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'LDAP işlemi zaman aşımına uğradı (60s)'
                }, {
                    status: 408
                }));
            }, 65000);
            client.on('error', (err)=>{
                clearTimeout(timeout);
                console.error('LDAP connection error:', err);
                resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: `LDAP bağlantı hatası: ${err.message}`
                }, {
                    status: 500
                }));
            });
            console.log(`Attempting LDAP bind with user: ${settings.username}`);
            client.bind(settings.username, settings.password, (err)=>{
                if (err) {
                    clearTimeout(timeout);
                    console.error('LDAP bind error:', err);
                    resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: `LDAP kimlik doğrulama hatası: ${err.message}`
                    }, {
                        status: 401
                    }));
                    return;
                }
                console.log('LDAP bind successful, searching for user...');
                // First, find the user's DN
                const searchFilter = `(&(objectClass=user)(sAMAccountName=${username}))`;
                const searchOptions = {
                    scope: 'sub',
                    filter: searchFilter,
                    attributes: [
                        'distinguishedName',
                        'pwdLastSet'
                    ]
                };
                client.search(settings.baseDN, searchOptions, (searchErr, searchRes)=>{
                    if (searchErr) {
                        clearTimeout(timeout);
                        console.error('LDAP search error:', searchErr);
                        resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            error: `Kullanıcı arama hatası: ${searchErr.message}`
                        }, {
                            status: 500
                        }));
                        return;
                    }
                    let userDN = '';
                    let foundUser = false;
                    searchRes.on('searchEntry', (entry)=>{
                        foundUser = true;
                        userDN = entry.pojo.objectName;
                        console.log(`Found user ${username} with DN: ${userDN}`);
                        // Clean up DN - remove escape sequences and fix encoding issues
                        if (userDN) {
                            // Remove backslash escape sequences like \c4\b0 and replace with proper characters
                            userDN = userDN.replace(/\\c4\\b0/g, 'İ'); // Turkish İ character
                            userDN = userDN.replace(/\\c4\\b1/g, 'ı'); // Turkish ı character
                            userDN = userDN.replace(/\\c5\\9f/g, 'ş'); // Turkish ş character
                            userDN = userDN.replace(/\\c5\\9e/g, 'Ş'); // Turkish Ş character
                            userDN = userDN.replace(/\\c3\\bc/g, 'ü'); // Turkish ü character
                            userDN = userDN.replace(/\\c3\\9c/g, 'Ü'); // Turkish Ü character
                            userDN = userDN.replace(/\\c3\\b6/g, 'ö'); // Turkish ö character
                            userDN = userDN.replace(/\\c3\\96/g, 'Ö'); // Turkish Ö character
                            userDN = userDN.replace(/\\c4\\9f/g, 'ğ'); // Turkish ğ character
                            userDN = userDN.replace(/\\c4\\9e/g, 'Ğ'); // Turkish Ğ character
                            userDN = userDN.replace(/\\c3\\a7/g, 'ç'); // Turkish ç character
                            userDN = userDN.replace(/\\c3\\87/g, 'Ç'); // Turkish Ç character
                            // Remove any remaining backslash escape sequences
                            userDN = userDN.replace(/\\[a-fA-F0-9]{2}/g, '');
                            // Clean up any double spaces
                            userDN = userDN.replace(/\s+/g, ' ');
                            console.log(`Cleaned DN: ${userDN}`);
                        }
                        // Also try to get DN from attributes if objectName is not available
                        if (!userDN) {
                            const attributes = entry.pojo.attributes;
                            const dnAttr = attributes.find((a)=>a.type === 'distinguishedName');
                            if (dnAttr && dnAttr.values && dnAttr.values.length > 0) {
                                userDN = dnAttr.values[0];
                                // Clean this DN too
                                if (userDN) {
                                    // Remove backslash escape sequences like \c4\b0 and replace with proper characters
                                    userDN = userDN.replace(/\\c4\\b0/g, 'İ'); // Turkish İ character
                                    userDN = userDN.replace(/\\c4\\b1/g, 'ı'); // Turkish ı character
                                    userDN = userDN.replace(/\\c5\\9f/g, 'ş'); // Turkish ş character
                                    userDN = userDN.replace(/\\c5\\9e/g, 'Ş'); // Turkish Ş character
                                    userDN = userDN.replace(/\\c3\\bc/g, 'ü'); // Turkish ü character
                                    userDN = userDN.replace(/\\c3\\9c/g, 'Ü'); // Turkish Ü character
                                    userDN = userDN.replace(/\\c3\\b6/g, 'ö'); // Turkish ö character
                                    userDN = userDN.replace(/\\c3\\96/g, 'Ö'); // Turkish Ö character
                                    userDN = userDN.replace(/\\c4\\9f/g, 'ğ'); // Turkish ğ character
                                    userDN = userDN.replace(/\\c4\\9e/g, 'Ğ'); // Turkish Ğ character
                                    userDN = userDN.replace(/\\c3\\a7/g, 'ç'); // Turkish ç character
                                    userDN = userDN.replace(/\\c3\\87/g, 'Ç'); // Turkish Ç character
                                    // Remove any remaining backslash escape sequences
                                    userDN = userDN.replace(/\\[a-fA-F0-9]{2}/g, '');
                                    // Clean up any double spaces
                                    userDN = userDN.replace(/\s+/g, ' ');
                                }
                                console.log(`Using distinguishedName attribute as DN: ${userDN}`);
                            }
                        }
                        // Final check - if still no DN, log error
                        if (!userDN) {
                            console.error(`No DN found for user ${username}. Entry:`, entry.pojo);
                        }
                    });
                    searchRes.on('error', (err)=>{
                        clearTimeout(timeout);
                        console.error('LDAP search result error:', err);
                        resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            error: `Kullanıcı arama sonuç hatası: ${err.message}`
                        }, {
                            status: 500
                        }));
                    });
                    searchRes.on('end', ()=>{
                        if (!foundUser || !userDN) {
                            clearTimeout(timeout);
                            console.error(`User not found or DN not available. Found: ${foundUser}, DN: ${userDN}, Username: ${username}`);
                            resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                                error: `Kullanıcı bulunamadı: ${username}`
                            }, {
                                status: 404
                            }));
                            return;
                        }
                        console.log(`Found user ${username} with DN: ${userDN}`);
                        // Step 1: Set pwdLastSet to 0 (forces password change)
                        console.log('Step 1: Setting pwdLastSet to 0');
                        const change1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ldapjs$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].Change({
                            operation: 'replace',
                            modification: {
                                type: 'pwdLastSet',
                                values: [
                                    '0'
                                ]
                            }
                        });
                        client.modify(userDN, change1, (modifyErr1)=>{
                            if (modifyErr1) {
                                clearTimeout(timeout);
                                console.error('LDAP modify error (step 1):', modifyErr1);
                                resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                                    error: `Şifre uzatma hatası (adım 1): ${modifyErr1.message}`
                                }, {
                                    status: 500
                                }));
                                return;
                            }
                            console.log('Step 1 completed successfully');
                            // Step 2: Set pwdLastSet to -1 (sets to current time)
                            console.log('Step 2: Setting pwdLastSet to -1');
                            const change2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ldapjs$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].Change({
                                operation: 'replace',
                                modification: {
                                    type: 'pwdLastSet',
                                    values: [
                                        '-1'
                                    ]
                                }
                            });
                            client.modify(userDN, change2, (modifyErr2)=>{
                                if (modifyErr2) {
                                    clearTimeout(timeout);
                                    console.error('LDAP modify error (step 2):', modifyErr2);
                                    resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                                        error: `Şifre uzatma hatası (adım 2): ${modifyErr2.message}`
                                    }, {
                                        status: 500
                                    }));
                                    return;
                                }
                                console.log('Step 2 completed successfully');
                                // Step 3: Unlock the user account (set lockoutTime to 0)
                                console.log('Step 3: Unlocking user account');
                                const change3 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ldapjs$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].Change({
                                    operation: 'replace',
                                    modification: {
                                        type: 'lockoutTime',
                                        values: [
                                            '0'
                                        ]
                                    }
                                });
                                client.modify(userDN, change3, (modifyErr3)=>{
                                    // Don't fail if unlock fails, just log it
                                    if (modifyErr3) {
                                        console.log('Step 3 info: User was not locked or unlock not needed:', modifyErr3.message);
                                    } else {
                                        console.log('Step 3 completed successfully - User unlocked');
                                    }
                                    clearTimeout(timeout);
                                    // Calculate new expiry date (90 days from now)
                                    const currentDate = new Date();
                                    const newExpiryDate = new Date(currentDate.getTime() + 90 * 24 * 60 * 60 * 1000);
                                    console.log(`Password extension and unlock completed successfully for user: ${username}`);
                                    resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                                        success: true,
                                        message: `Password extended and user unlocked successfully for user: ${username}`,
                                        username: username,
                                        extendedAt: currentDate.toISOString(),
                                        newExpiryDate: newExpiryDate.toISOString(),
                                        daysExtended: 90,
                                        unlocked: true
                                    }));
                                });
                            });
                        });
                    });
                });
            });
        });
    } catch (error) {
        console.error('Extend password API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: `Şifre uzatma hatası: ${error.message}`
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__caf8cb9f._.js.map