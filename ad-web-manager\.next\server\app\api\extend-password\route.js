(()=>{var e={};e.id=276,e.ids=[276],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37668:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>A,routeModule:()=>x,serverHooks:()=>y,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>w});var t={};s.r(t),s.d(t,{POST:()=>f});var o=s(96559),a=s(48088),n=s(37719),c=s(32190),l=s(19854),u=s(73010),i=s.n(u),p=s(29021),d=s.n(p),g=s(33873);let m=s.n(g)().join(process.cwd(),"ldap-settings.json");async function f(e){try{let r=await (0,l.getServerSession)();if(!r?.user)return c.NextResponse.json({error:"Unauthorized"},{status:401});let{username:s}=await e.json();if(!s)return c.NextResponse.json({error:"Kullanıcı adı gereklidir"},{status:400});console.log(`Starting password extension for user: ${s}`);let t=function(){try{if(d().existsSync(m)){let e=d().readFileSync(m,"utf8");return JSON.parse(e)}return null}catch(e){return console.error("Error loading settings:",e),null}}();if(!t)return c.NextResponse.json({error:"LDAP ayarları bulunamadı. L\xfctfen \xf6nce ayarları yapılandırın."},{status:400});if(!t.server||!t.baseDN||!t.username||!t.password)return c.NextResponse.json({error:"LDAP ayarları eksik. L\xfctfen ayarları kontrol edin."},{status:400});let o=t.useSSL?"ldaps":"ldap",a=`${o}://${t.server}:${t.port}`;console.log(`Attempting LDAP connection to: ${a}`);let n=i().createClient({url:a,timeout:6e4,connectTimeout:2e4,tlsOptions:{rejectUnauthorized:!1}});return new Promise(e=>{let r=!1,o=s=>{r||(r=!0,n.destroy(),e(s))},a=setTimeout(()=>{console.error(`LDAP timeout for user: ${s}`),o(c.NextResponse.json({error:"LDAP işlemi zaman aşımına uğradı (60s)"},{status:408}))},65e3);n.on("error",e=>{clearTimeout(a),console.error("LDAP connection error:",e),o(c.NextResponse.json({error:`LDAP bağlantı hatası: ${e.message}`},{status:500}))}),console.log(`Attempting LDAP bind with user: ${t.username}`),n.bind(t.username,t.password,e=>{if(e){clearTimeout(a),console.error("LDAP bind error:",e),o(c.NextResponse.json({error:`LDAP kimlik doğrulama hatası: ${e.message}`},{status:401}));return}console.log("LDAP bind successful, searching for user...");let r=`(&(objectClass=user)(sAMAccountName=${s}))`;n.search(t.baseDN,{scope:"sub",filter:r,attributes:["distinguishedName","pwdLastSet"]},(e,r)=>{if(e){clearTimeout(a),console.error("LDAP search error:",e),o(c.NextResponse.json({error:`Kullanıcı arama hatası: ${e.message}`},{status:500}));return}let t="",l=!1;r.on("searchEntry",e=>{if(l=!0,t=e.pojo.objectName,console.log(`Found user ${s} with DN: ${t}`),t&&(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace(/\\c4\\b0/g,"İ")).replace(/\\c4\\b1/g,"ı")).replace(/\\c5\\9f/g,"ş")).replace(/\\c5\\9e/g,"Ş")).replace(/\\c3\\bc/g,"\xfc")).replace(/\\c3\\9c/g,"\xdc")).replace(/\\c3\\b6/g,"\xf6")).replace(/\\c3\\96/g,"\xd6")).replace(/\\c4\\9f/g,"ğ")).replace(/\\c4\\9e/g,"Ğ")).replace(/\\c3\\a7/g,"\xe7")).replace(/\\c3\\87/g,"\xc7")).replace(/\\[a-fA-F0-9]{2}/g,"")).replace(/\s+/g," "),console.log(`Cleaned DN: ${t}`)),!t){let r=e.pojo.attributes.find(e=>"distinguishedName"===e.type);r&&r.values&&r.values.length>0&&((t=r.values[0])&&(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace(/\\c4\\b0/g,"İ")).replace(/\\c4\\b1/g,"ı")).replace(/\\c5\\9f/g,"ş")).replace(/\\c5\\9e/g,"Ş")).replace(/\\c3\\bc/g,"\xfc")).replace(/\\c3\\9c/g,"\xdc")).replace(/\\c3\\b6/g,"\xf6")).replace(/\\c3\\96/g,"\xd6")).replace(/\\c4\\9f/g,"ğ")).replace(/\\c4\\9e/g,"Ğ")).replace(/\\c3\\a7/g,"\xe7")).replace(/\\c3\\87/g,"\xc7")).replace(/\\[a-fA-F0-9]{2}/g,"")).replace(/\s+/g," ")),console.log(`Using distinguishedName attribute as DN: ${t}`))}t||console.error(`No DN found for user ${s}. Entry:`,e.pojo)}),r.on("error",e=>{clearTimeout(a),console.error("LDAP search result error:",e),o(c.NextResponse.json({error:`Kullanıcı arama sonu\xe7 hatası: ${e.message}`},{status:500}))}),r.on("end",()=>{if(!l||!t){clearTimeout(a),console.error(`User not found or DN not available. Found: ${l}, DN: ${t}, Username: ${s}`),o(c.NextResponse.json({error:`Kullanıcı bulunamadı: ${s}`},{status:404}));return}console.log(`Found user ${s} with DN: ${t}`),console.log("Step 1: Setting pwdLastSet to 0");let e=new(i()).Change({operation:"replace",modification:{type:"pwdLastSet",values:["0"]}});n.modify(t,e,e=>{if(e){clearTimeout(a),console.error("LDAP modify error (step 1):",e),o(c.NextResponse.json({error:`Şifre uzatma hatası (adım 1): ${e.message}`},{status:500}));return}console.log("Step 1 completed successfully"),console.log("Step 2: Setting pwdLastSet to -1");let r=new(i()).Change({operation:"replace",modification:{type:"pwdLastSet",values:["-1"]}});n.modify(t,r,e=>{if(e){clearTimeout(a),console.error("LDAP modify error (step 2):",e),o(c.NextResponse.json({error:`Şifre uzatma hatası (adım 2): ${e.message}`},{status:500}));return}console.log("Step 2 completed successfully"),console.log("Step 3: Unlocking user account");let r=new(i()).Change({operation:"replace",modification:{type:"lockoutTime",values:["0"]}});n.modify(t,r,e=>{e?console.log("Step 3 info: User was not locked or unlock not needed:",e.message):console.log("Step 3 completed successfully - User unlocked"),clearTimeout(a);let r=new Date,t=new Date(r.getTime()+7776e6);console.log(`Password extension and unlock completed successfully for user: ${s}`),o(c.NextResponse.json({success:!0,message:`Password extended and user unlocked successfully for user: ${s}`,username:s,extendedAt:r.toISOString(),newExpiryDate:t.toISOString(),daysExtended:90,unlocked:!0}))})})})})})})})}catch(e){return console.error("Extend password API error:",e),c.NextResponse.json({error:`Şifre uzatma hatası: ${e.message}`},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/extend-password/route",pathname:"/api/extend-password",filename:"route",bundlePath:"app/api/extend-password/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\extend-password\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:h,workUnitAsyncStorage:w,serverHooks:y}=x;function A(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:w})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73010:e=>{"use strict";e.exports=require("ldapjs")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580,854],()=>s(37668));module.exports=t})();