{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ioihxw0T6uLY6tVdvT/KSIElCQ4mXNoUEabkMtzHbR4=", "__NEXT_PREVIEW_MODE_ID": "8ade6b467a92c5425d602f64c93443af", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2c3b3d5b9070dc01d366880f16630464a7dd32b5a7c3eee0b704d289ec6ecea2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d49c9697c515d4f0ccf0ffae779a167cdeb9b88ab74a64741779e18f91f8f61b"}}}, "instrumentation": null, "functions": {}}