{"/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/auth/refresh-session/route": "/api/auth/refresh-session", "/_not-found/page": "/_not-found", "/api/extend-password/route": "/api/extend-password", "/api/locked-users/route": "/api/locked-users", "/api/auth/invalidate-sessions/route": "/api/auth/invalidate-sessions", "/api/settings/route": "/api/settings", "/api/password-expiry/route": "/api/password-expiry", "/api/test-connection/route": "/api/test-connection", "/api/search-user/route": "/api/search-user", "/api/test-ldap/route": "/api/test-ldap", "/api/users/route": "/api/users", "/api/unlock-user/route": "/api/unlock-user", "/api/unlock-user-ps/route": "/api/unlock-user-ps", "/api/users/[id]/route": "/api/users/[id]", "/api/debug/session/route": "/api/debug/session", "/favicon.ico/route": "/favicon.ico", "/login/page": "/login", "/settings/page": "/settings", "/password-expiry/page": "/password-expiry", "/page": "/", "/manage-users/page": "/manage-users", "/users/page": "/users"}