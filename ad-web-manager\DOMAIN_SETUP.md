# Domain Kurulum Rehberi

## 1. Domain Konfigürasyonu

### Adım 1: Environment Variables Güncelleme

`.env.production` dosyasını düzenleyin:

```bash
# Production Environment Variables
NEXTAUTH_SECRET=your-production-secret-key-change-this
NEXTAUTH_URL=http://yourdomain.com

# Domain configuration
DOMAIN_NAME=yourdomain.com
PORT=80
```

### Adım 2: DNS Ayarları

Domain'inizi sunucunuzun IP adresine yönlendirin:
- A Record: `yourdomain.com` → `SERVER_IP_ADDRESS`
- CNAME Record: `www.yourdomain.com` → `yourdomain.com`

## 2. Sunucu Kurulumu

### Adım 1: Uygulamayı Build Edin

```bash
npm run build
```

### Adım 2: Production Modunda Başlatın

**Seçenek 1: Batch Script ile (Önerilen)**
```bash
start-production.bat
```

**Seçenek 2: <PERSON>**
```bash
npm run start:production
```

**Seçenek 3: Port 80 ile**
```bash
npm run start:domain
```

## 3. Port 80 Kullanımı

### Windows'ta Port 80 Kullanımı

Port 80 kullanmak için **Administrator** yetkisi gereklidir:

1. **Command Prompt'u Administrator olarak açın**
2. Proje klasörüne gidin
3. `start-production.bat` çalıştırın

### Alternatif Port Kullanımı

Eğer port 80 kullanamıyorsanız:

```bash
# Port 8080 ile başlatın
npm start -- -p 8080
```

Sonra domain'inizi `yourdomain.com:8080` olarak erişin.

## 4. Firewall Ayarları

### Windows Firewall

Port 80'i açın:
1. Windows Defender Firewall açın
2. "Inbound Rules" → "New Rule"
3. Port → TCP → 80
4. Allow the connection
5. Apply to all profiles

### Router/Network Firewall

- Port 80'i sunucunuza yönlendirin
- Port forwarding ayarlarını yapın

## 5. Test Etme

### Lokal Test
```bash
http://localhost
```

### Domain Test
```bash
http://yourdomain.com
```

### Mobil Test
Telefonunuzdan domain adresine erişin.

## 6. Güvenlik Önerileri

### HTTPS Kurulumu (Opsiyonel)

Let's Encrypt ile ücretsiz SSL:
1. Certbot kurun
2. SSL sertifikası alın
3. `.env.production`'da HTTPS kullanın

### Güvenlik Headers

Next.js konfigürasyonunda güvenlik headers'ları zaten eklenmiştir:
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Referrer-Policy: origin-when-cross-origin

## 7. Sorun Giderme

### Port 80 Kullanılamıyor
- IIS veya Apache kapalı olduğundan emin olun
- Administrator yetkisi ile çalıştırın
- Alternatif port kullanın

### Domain Erişilemiyor
- DNS ayarlarını kontrol edin
- Firewall ayarlarını kontrol edin
- Sunucu IP adresini doğrulayın

### Mobil Erişim Sorunu
- Domain'in mobil cihazlardan erişilebilir olduğunu kontrol edin
- WiFi ağında aynı domain'e erişebildiğinizi test edin

## 8. Örnek Konfigürasyon

### Örnek .env.production
```bash
NEXTAUTH_SECRET=bayraktar-ad-web-manager-2024-production-key
NEXTAUTH_URL=http://ad.bayraktar.com
DOMAIN_NAME=ad.bayraktar.com
PORT=80
```

### DNS Ayarları (ad.bayraktar.com)
```
A     ad.bayraktar.com         SERVER_IP_ADDRESS
CNAME www.ad.bayraktar.com     ad.bayraktar.com
```

### Erişim Adresleri
- **Ana Adres**: http://ad.bayraktar.com
- **WWW Adresi**: http://www.ad.bayraktar.com
- **Mobil**: Telefondan http://ad.bayraktar.com

Bu rehberi takip ederek domain'inizi başarıyla kurabilirsiniz!
