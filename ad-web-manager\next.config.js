/** @type {import('next').NextConfig} */
const nextConfig = {
  // Keep Next.js development indicator in default position (bottom-left)
  devIndicators: {
    position: 'bottom-left',
  },

  // HTTP configuration for development
  async rewrites() {
    return []
  },

  // Allow cross-origin requests from mobile devices
  allowedDevOrigins: ['192.20.60.12:3000'],

  // External packages configuration
  serverExternalPackages: ['ldapjs'],

  // Suppress hydration warnings caused by browser extensions
  reactStrictMode: false,

  // Webpack configuration to handle client-side issues
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },
}

module.exports = nextConfig
