'use client';

import { useState, useEffect } from 'react';

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  screenWidth: number;
  userAgent: string;
}

export function useDeviceDetection(): DeviceInfo {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    deviceType: 'desktop',
    screenWidth: 1024,
    userAgent: ''
  });

  useEffect(() => {
    const detectDevice = () => {
      const userAgent = navigator.userAgent || '';
      const screenWidth = window.innerWidth;

      // Mobile device detection via user agent
      const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
      const tabletRegex = /iPad|Android(?=.*\bMobile\b)(?=.*\bTablet\b)|Android(?=.*\bTablet\b)/i;
      
      // Screen size based detection
      const isMobileScreen = screenWidth < 768;
      const isTabletScreen = screenWidth >= 768 && screenWidth < 1024;
      const isDesktopScreen = screenWidth >= 1024;

      // User agent based detection
      const isMobileUA = mobileRegex.test(userAgent) && !tabletRegex.test(userAgent);
      const isTabletUA = tabletRegex.test(userAgent);

      // Combined detection (prioritize user agent for mobile devices)
      const isMobile = isMobileUA || (isMobileScreen && !isTabletUA);
      const isTablet = isTabletUA || (isTabletScreen && !isMobileUA);
      const isDesktop = !isMobile && !isTablet;

      let deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop';
      if (isMobile) deviceType = 'mobile';
      else if (isTablet) deviceType = 'tablet';

      setDeviceInfo({
        isMobile,
        isTablet,
        isDesktop,
        deviceType,
        screenWidth,
        userAgent
      });
    };

    // Initial detection
    detectDevice();

    // Listen for window resize
    const handleResize = () => {
      detectDevice();
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return deviceInfo;
}

// Server-side device detection helper
export function getServerDeviceInfo(userAgent: string): Omit<DeviceInfo, 'screenWidth'> {
  const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  const tabletRegex = /iPad|Android(?=.*\bMobile\b)(?=.*\bTablet\b)|Android(?=.*\bTablet\b)/i;
  
  const isMobileUA = mobileRegex.test(userAgent) && !tabletRegex.test(userAgent);
  const isTabletUA = tabletRegex.test(userAgent);
  
  const isMobile = isMobileUA;
  const isTablet = isTabletUA;
  const isDesktop = !isMobile && !isTablet;

  let deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop';
  if (isMobile) deviceType = 'mobile';
  else if (isTablet) deviceType = 'tablet';

  return {
    isMobile,
    isTablet,
    isDesktop,
    deviceType,
    userAgent
  };
}
