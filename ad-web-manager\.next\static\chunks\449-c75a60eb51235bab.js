"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[449],{285:(e,s,t)=>{t.d(s,{$:()=>l});var a=t(5155);t(2115);var r=t(9708),i=t(2085),n=t(9434);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:s,variant:t,size:i,asChild:l=!1,...d}=e,c=l?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:i,className:s})),...d})}},420:(e,s,t)=>{t.d(s,{X:()=>r});var a=t(2115);function r(){let[e,s]=(0,a.useState)({isMobile:!1,isTablet:!1,isDesktop:!0,deviceType:"desktop",screenWidth:1024,userAgent:""}),[t,r]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{r(!0);let e=()=>{let e=navigator.userAgent||"",t=window.innerWidth,a=/iPad|Android(?=.*\bMobile\b)(?=.*\bTablet\b)|Android(?=.*\bTablet\b)/i,r=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(e)&&!a.test(e),i=a.test(e),n=r||t<768&&!i,o=i||t>=768&&t<1024&&!r,l="desktop";n?l="mobile":o&&(l="tablet"),s({isMobile:n,isTablet:o,isDesktop:!n&&!o,deviceType:l,screenWidth:t,userAgent:e})};e();let t=()=>{e()};return window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}},[]),t)?e:{isMobile:!1,isTablet:!1,isDesktop:!0,deviceType:"desktop",screenWidth:1024,userAgent:""}}},3978:(e,s,t)=>{t.d(s,{c:()=>_});var a=t(5155),r=t(2115),i=t(6874),n=t.n(i),o=t(6766),l=t(2108),d=t(285),c=t(5452),m=t(2085),u=t(4416),h=t(9434);let x=c.bL,f=c.l9;c.bm;let g=c.ZL,p=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(c.hJ,{className:(0,h.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r,ref:s})});p.displayName=c.hJ.displayName;let b=(0,m.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),v=r.forwardRef((e,s)=>{let{side:t="right",className:r,children:i,...n}=e;return(0,a.jsxs)(g,{children:[(0,a.jsx)(p,{}),(0,a.jsxs)(c.UC,{ref:s,className:(0,h.cn)(b({side:t}),r),...n,children:[i,(0,a.jsxs)(c.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});v.displayName=c.UC.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(c.hE,{ref:s,className:(0,h.cn)("text-lg font-semibold text-foreground",t),...r})}).displayName=c.hE.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(c.VY,{ref:s,className:(0,h.cn)("text-sm text-muted-foreground",t),...r})}).displayName=c.VY.displayName;var j=t(7340),y=t(7580),N=t(9530),w=t(2318),k=t(381),A=t(4835),z=t(4783);function _(e){let{currentPath:s="/"}=e,{data:t}=(0,l.useSession)(),[i,c]=(0,r.useState)(!1);if(!t)return null;let m=()=>{(0,l.signOut)({callbackUrl:"/login"}),c(!1)},u=[{href:"/",label:"Dashboard",icon:j.A,show:!0},{href:"/users",label:"Locked Users",icon:y.A,show:!0},{href:"/password-expiry",label:"Password Expiry",icon:N.A,show:!0},{href:"/manage-users",label:"Manage Users",icon:w.A,show:t.user.permissions.manageUsers},{href:"/settings",label:"Settings",icon:k.A,show:t.user.permissions.manageSettings}];return(0,a.jsx)("nav",{className:"border-b bg-card",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,a.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)(o.default,{src:"/bayraktar_holding_logo.jpeg",alt:"Bayraktar Holding Logo",width:40,height:40,className:"h-8 w-auto",priority:!0,quality:100,unoptimized:!0}),(0,a.jsx)("span",{className:"text-base font-bold text-primary truncate",children:"AD Web Manager"})]}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-6",children:[u.filter(e=>e.show).map(e=>(0,a.jsx)(n(),{href:e.href,className:"text-sm font-medium hover:text-primary transition-colors ".concat(s===e.href?"text-primary":"text-muted-foreground"),children:e.label},e.href)),(0,a.jsxs)(d.$,{onClick:m,variant:"outline",size:"sm",children:[(0,a.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"\xc7ıkış"]})]}),(0,a.jsxs)(x,{open:i,onOpenChange:c,children:[(0,a.jsx)(f,{asChild:!0,children:(0,a.jsxs)(d.$,{variant:"ghost",size:"sm",className:"md:hidden",children:[(0,a.jsx)(z.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"sr-only",children:"Menu"})]})}),(0,a.jsx)(v,{side:"right",className:"w-[300px] sm:w-[400px]",children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("div",{className:"flex items-center justify-between pb-4 border-b",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.default,{src:"/bayraktar_holding_logo.jpeg",alt:"Bayraktar Holding Logo",width:32,height:32,className:"h-8 w-auto",priority:!0,quality:100,unoptimized:!0}),(0,a.jsx)("span",{className:"text-lg font-bold text-primary",children:"AD Web Manager"})]})}),(0,a.jsxs)("div",{className:"py-4 border-b",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Hoş geldiniz,"}),(0,a.jsx)("p",{className:"font-medium",children:t.user.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:t.user.email})]}),(0,a.jsx)("div",{className:"flex-1 py-4",children:(0,a.jsx)("div",{className:"space-y-2",children:u.filter(e=>e.show).map(e=>{let t=e.icon;return(0,a.jsxs)(n(),{href:e.href,onClick:()=>c(!1),className:"flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(s===e.href?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-primary hover:bg-accent"),children:[(0,a.jsx)(t,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:e.label})]},e.href)})})}),(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)(d.$,{onClick:m,variant:"outline",className:"w-full",children:[(0,a.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"\xc7ıkış Yap"]})})]})})]})]})})})}},6695:(e,s,t)=>{t.d(s,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var a=t(5155);t(2115);var r=t(9434);function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function o(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t})}},8931:(e,s,t)=>{t.d(s,{u:()=>c});var a=t(5155);t(2115);var r=t(6874),i=t.n(r),n=t(6766),o=t(2108),l=t(285),d=t(4835);function c(e){let{currentPath:s="/"}=e,{data:t}=(0,o.useSession)();if(!t)return null;let r=[{href:"/",label:"Dashboard",show:!0},{href:"/users",label:"Locked Users",show:!0},{href:"/password-expiry",label:"Password Expiry",show:!0},{href:"/manage-users",label:"Manage Users",show:t.user.permissions.manageUsers},{href:"/settings",label:"Settings",show:t.user.permissions.manageSettings}];return(0,a.jsx)("nav",{className:"border-b bg-card",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex h-16 lg:h-20 items-center justify-between",children:[(0,a.jsxs)(i(),{href:"/",className:"flex items-center space-x-3",children:[(0,a.jsx)(n.default,{src:"/bayraktar_holding_logo.jpeg",alt:"Bayraktar Holding Logo",width:120,height:120,className:"h-10 lg:h-12 w-auto",priority:!0,quality:100,unoptimized:!0}),(0,a.jsx)("span",{className:"text-lg lg:text-xl font-bold text-primary",children:"AD Web Manager"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[r.filter(e=>e.show).map(e=>(0,a.jsx)(i(),{href:e.href,className:"text-sm font-medium hover:text-primary transition-colors ".concat(s===e.href?"text-primary":"text-muted-foreground"),children:e.label},e.href)),(0,a.jsxs)("div",{className:"flex items-center space-x-3 border-l pl-6",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-foreground",children:t.user.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:t.user.role})]}),(0,a.jsxs)(l.$,{onClick:()=>{(0,o.signOut)({callbackUrl:"/login"})},variant:"outline",size:"sm",children:[(0,a.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"\xc7ıkış"]})]})]})]})})})}},9434:(e,s,t)=>{t.d(s,{cn:()=>i});var a=t(2596),r=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}}}]);