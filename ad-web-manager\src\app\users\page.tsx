'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useNotifications, NotificationContainer } from '@/components/ui/notification';
import { Loader2, RefreshCw, CheckCircle, XCircle, Unlock, LogOut, UserCheck, Clock, AlertTriangle } from 'lucide-react';

interface LockedUser {
  username: string;
  displayName: string;
  lockoutTime: string;
  passwordExpires?: string;
  accountExpires?: string;
  lastLogon?: string;
}

interface UserStats {
  totalLocked: number;
  lockedToday: number;
  passwordExpired: number;
  lastUpdated: string;
}

export default function Users() {
  const { data: session } = useSession();
  const { notifications, addNotification, removeNotification } = useNotifications();
  const [users, setUsers] = useState<LockedUser[]>([]);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [unlockingUser, setUnlockingUser] = useState<string | null>(null);
  const [isUnlockingAll, setIsUnlockingAll] = useState(false);

  if (!session) {
    return null;
  }

  if (!session.user.permissions.viewUsers) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card>
          <CardHeader>
            <CardTitle>Erişim Reddedildi</CardTitle>
            <CardDescription>Bu sayfaya erişim yetkiniz bulunmamaktadır.</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/">Ana Sayfaya Dön</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleLogout = () => {
    signOut({ callbackUrl: '/login' });
  };

  useEffect(() => {
    loadLockedUsers();
  }, []);

  const loadLockedUsers = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/locked-users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
        setStats(data.stats || null);
        if (data.users.length === 0) {
          setMessage('Kilitlenen kullanıcı bulunamadı.');
          setMessageType('success');
        }
      } else {
        const error = await response.json();
        setMessage(error.error || 'Kullanıcılar yüklenirken hata oluştu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Bağlantı hatası!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const unlockUser = async (username: string) => {
    setUnlockingUser(username);
    setMessage('');

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 70000); // 70 second timeout

      const response = await fetch('/api/unlock-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const result = await response.json();

      if (response.ok && result.success) {
        // Show success notification
        addNotification({
          variant: "success",
          title: "✅ Unlock Başarılı!",
          description: `${username} kullanıcısının kilidi başarıyla kaldırıldı.`,
          duration: 8000
        });

        // Refresh the list
        setTimeout(() => {
          loadLockedUsers();
        }, 1000);
      } else {
        // Show error notification
        addNotification({
          variant: "error",
          title: "❌ Unlock Başarısız!",
          description: result.error || 'Unlock işlemi sırasında bir hata oluştu.',
          duration: 8000
        });
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        addNotification({
          variant: "error",
          title: "⏱️ Zaman Aşımı!",
          description: `${username} unlock işlemi 70 saniye içinde tamamlanamadı.`,
          duration: 8000
        });
      } else {
        addNotification({
          variant: "error",
          title: "❌ Bağlantı Hatası!",
          description: 'Unlock işlemi sırasında beklenmeyen bir hata oluştu.',
          duration: 8000
        });
      }
    } finally {
      setUnlockingUser(null);
    }
  };



  const unlockAllUsers = async () => {
    const lockedUsers = users.filter(u => u.lockoutTime);
    if (lockedUsers.length === 0) {
      setMessage('Kilitlenen kullanıcı bulunamadı.');
      setMessageType('error');
      return;
    }

    if (!confirm(`${lockedUsers.length} kullanıcının kilidini kaldırmak istediğinizden emin misiniz?`)) {
      return;
    }

    setIsUnlockingAll(true);
    setMessage('Toplu unlock işlemi başlatıldı...');
    setMessageType('success');

    let successCount = 0;
    let errorCount = 0;
    const results = [];

    try {
      for (let i = 0; i < lockedUsers.length; i++) {
        const user = lockedUsers[i];
        setMessage(`İşleniyor: ${user.username} (${i + 1}/${lockedUsers.length})`);

        try {
          // Only try LDAP method with timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

          const response = await fetch('/api/unlock-user', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username: user.username }),
            signal: controller.signal
          });

          clearTimeout(timeoutId);
          const result = await response.json();

          if (response.ok && result.success) {
            successCount++;
            results.push(`✅ ${user.username}`);
          } else {
            errorCount++;
            results.push(`❌ ${user.username}: ${result.error || 'LDAP hatası'}`);
          }
        } catch (error: any) {
          errorCount++;
          if (error.name === 'AbortError') {
            results.push(`❌ ${user.username}: Timeout (30s)`);
          } else {
            results.push(`❌ ${user.username}: ${error.message || 'Bağlantı hatası'}`);
          }
        }

        // Small delay between requests to prevent overwhelming
        if (i < lockedUsers.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
    } catch (error) {
      console.error('Bulk unlock error:', error);
      setMessage('Toplu unlock işlemi sırasında beklenmeyen hata oluştu.');
      setMessageType('error');
    } finally {
      setIsUnlockingAll(false);

      // Show completion notification
      if (errorCount === 0) {
        addNotification({
          variant: "success",
          title: "🎉 Toplu Unlock Tamamlandı!",
          description: `${successCount} kullanıcının kilidi başarıyla kaldırıldı.`,
          duration: 8000
        });
      } else if (successCount > 0) {
        addNotification({
          variant: "success",
          title: "⚠️ Toplu Unlock Kısmen Başarılı",
          description: `${successCount} başarılı, ${errorCount} hata oluştu.`,
          duration: 8000
        });
      } else {
        addNotification({
          variant: "error",
          title: "❌ Toplu Unlock Başarısız!",
          description: `Hiçbir kullanıcının kilidi kaldırılamadı. ${errorCount} hata oluştu.`,
          duration: 8000
        });
      }

      // Refresh the list
      setTimeout(() => {
        loadLockedUsers();
      }, 1000);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('tr-TR');
    } catch {
      return dateString;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="border-b bg-card">
        <div className="container mx-auto px-4">
          <div className="flex h-24 items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/bayraktar_holding_logo.jpeg"
                alt="Bayraktar Holding Logo"
                width={80}
                height={80}
                className="rounded-md"
              />
              <span className="text-xl font-bold text-primary">AD Web Manager</span>
            </Link>
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-sm font-medium text-muted-foreground hover:text-primary">
                Dashboard
              </Link>
              <Link href="/users" className="text-sm font-medium text-foreground hover:text-primary">
                Locked Users
              </Link>
              <Link href="/password-expiry" className="text-sm font-medium text-muted-foreground hover:text-primary">
                Password Expiry
              </Link>
              {session.user.permissions.manageSettings && (
                <Link href="/settings" className="text-sm font-medium text-muted-foreground hover:text-primary">
                  Settings
                </Link>
              )}
              {session.user.permissions.manageUsers && (
                <Link href="/manage-users" className="text-sm font-medium text-muted-foreground hover:text-primary">
                  Manage Users
                </Link>
              )}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  {session.user.name} ({session.user.role})
                </span>
                <Button variant="ghost" size="sm" onClick={handleLogout}>
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Dashboard Stats */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="border-red-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-center space-x-3">
                  <Unlock className="h-6 w-6 text-red-600" />
                  <CardTitle className="text-base font-semibold text-muted-foreground">Toplam Kilitli</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-4xl font-bold text-red-600">{stats.totalLocked}</div>
              </CardContent>
            </Card>
            <Card className="border-orange-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-center space-x-3">
                  <AlertTriangle className="h-6 w-6 text-orange-600" />
                  <CardTitle className="text-base font-semibold text-muted-foreground">Bugün Kilitlendi</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-4xl font-bold text-orange-600">{stats.lockedToday}</div>
              </CardContent>
            </Card>
            <Card className="border-yellow-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-center space-x-3">
                  <Clock className="h-6 w-6 text-yellow-600" />
                  <CardTitle className="text-base font-semibold text-muted-foreground">Şifre Süresi Doldu</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-4xl font-bold text-yellow-600">{stats.passwordExpired}</div>
              </CardContent>
            </Card>
            <Card className="border-blue-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-center space-x-3">
                  <RefreshCw className="h-6 w-6 text-blue-600" />
                  <CardTitle className="text-base font-semibold text-muted-foreground">Son Güncelleme</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-base font-medium text-muted-foreground">
                  {formatDate(stats.lastUpdated)}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-2xl">Kilitlenen Kullanıcılar</CardTitle>
                <CardDescription>
                  Active Directory'de kilitlenen kullanıcıları görüntüleyin ve unlock edin
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                {users.filter(u => u.lockoutTime).length > 0 && session.user.permissions.unlockUsers && (
                  <Button
                    variant="destructive"
                    onClick={unlockAllUsers}
                    disabled={isUnlockingAll || isLoading}
                  >
                    {isUnlockingAll ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Unlock className="mr-2 h-4 w-4" />
                    )}
                    {isUnlockingAll ? 'Tümü Unlock Ediliyor...' : 'Tümünün Kilidini Kaldır'}
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={loadLockedUsers}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 h-4 w-4" />
                  )}
                  {isLoading ? 'Yenileniyor...' : 'Yenile'}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>



            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Yükleniyor...</span>
              </div>
            ) : users.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Kullanıcı Adı</TableHead>
                      <TableHead>Tam Ad</TableHead>
                      <TableHead>Kilitlenme Zamanı</TableHead>
                      <TableHead>Şifre Süresi</TableHead>
                      <TableHead>İşlemler</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users
                      .sort((a, b) => {
                        // Sort by lockout time, newest first
                        if (!a.lockoutTime && !b.lockoutTime) return 0;
                        if (!a.lockoutTime) return 1;
                        if (!b.lockoutTime) return -1;
                        return new Date(b.lockoutTime).getTime() - new Date(a.lockoutTime).getTime();
                      })
                      .map((user, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{user.username}</TableCell>
                        <TableCell>{user.displayName || '-'}</TableCell>
                        <TableCell>
                          {user.lockoutTime ? formatDate(user.lockoutTime) : '-'}
                        </TableCell>
                        <TableCell>
                          {user.passwordExpires ? (
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              new Date(user.passwordExpires) <= new Date()
                                ? 'bg-red-100 text-red-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {formatDate(user.passwordExpires)}
                            </span>
                          ) : '-'}
                        </TableCell>
                        <TableCell>
                          {session.user.permissions.unlockUsers ? (
                            <Button
                              size="sm"
                              onClick={() => unlockUser(user.username)}
                              disabled={unlockingUser === user.username}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              {unlockingUser === user.username ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Unlock Ediliyor...
                                </>
                              ) : (
                                <>
                                  <Unlock className="mr-2 h-4 w-4" />
                                  Unlock
                                </>
                              )}
                            </Button>
                          ) : (
                            <span className="text-sm text-muted-foreground">Yetki yok</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : !isLoading && (
              <div className="text-center py-8 text-muted-foreground">
                <p>Kilitlenen kullanıcı bulunamadı.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Notification Container */}
      <NotificationContainer
        notifications={notifications}
        onRemove={removeNotification}
      />
    </div>
  );
}
