import { NextRequest, NextResponse } from 'next/server';
import ldap from 'ldapjs';
import fs from 'fs';
import path from 'path';

const SETTINGS_FILE = path.join(process.cwd(), 'ldap-settings.json');

interface LdapSettings {
  server: string;
  port: string;
  baseDN: string;
  username: string;
  password: string;
  useSSL: boolean;
}

function loadSettings(): LdapSettings | null {
  try {
    if (fs.existsSync(SETTINGS_FILE)) {
      const data = fs.readFileSync(SETTINGS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return null;
  } catch (error) {
    console.error('Error loading settings:', error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { username } = await request.json();
    
    if (!username) {
      return NextResponse.json(
        { error: 'Kullanıcı adı gereklidir' },
        { status: 400 }
      );
    }

    const settings = loadSettings();
    
    if (!settings) {
      return NextResponse.json(
        { error: 'LDAP ayarları bulunamadı. Lütfen önce ayarları yapılandırın.' },
        { status: 400 }
      );
    }

    if (!settings.server || !settings.baseDN || !settings.username || !settings.password) {
      return NextResponse.json(
        { error: 'LDAP ayarları eksik. Lütfen ayarları kontrol edin.' },
        { status: 400 }
      );
    }

    const protocol = settings.useSSL ? 'ldaps' : 'ldap';
    const url = `${protocol}://${settings.server}:${settings.port}`;
    
    console.log(`Attempting LDAP connection to: ${url}`);
    console.log(`Username for unlock: ${username}`);

    const client = ldap.createClient({
      url: url,
      timeout: 60000,
      connectTimeout: 20000,
      tlsOptions: {
        rejectUnauthorized: false
      }
    });

    return new Promise((resolve) => {
      let isResolved = false;

      const resolveOnce = (response: NextResponse) => {
        if (!isResolved) {
          isResolved = true;
          client.destroy();
          resolve(response);
        }
      };

      const timeout = setTimeout(() => {
        console.error(`LDAP timeout for user: ${username}`);
        resolveOnce(NextResponse.json(
          { error: 'LDAP işlemi zaman aşımına uğradı (60s)' },
          { status: 408 }
        ));
      }, 65000);

      client.on('error', (err) => {
        clearTimeout(timeout);
        console.error('LDAP connection error:', err);
        resolveOnce(NextResponse.json(
          { error: `LDAP bağlantı hatası: ${err.message}` },
          { status: 500 }
        ));
      });

      console.log(`Attempting LDAP bind with user: ${settings.username}`);

      client.bind(settings.username, settings.password, (err) => {
        if (err) {
          clearTimeout(timeout);
          console.error('LDAP bind error:', err);
          resolveOnce(NextResponse.json(
            { error: `LDAP kimlik doğrulama hatası: ${err.message}` },
            { status: 401 }
          ));
          return;
        }

        console.log('LDAP bind successful, searching for user...');

        // First, find the user's DN
        const searchFilter = `(&(objectClass=user)(sAMAccountName=${username}))`;
        const searchOptions = {
          scope: 'sub' as const,
          filter: searchFilter,
          attributes: ['distinguishedName', 'lockoutTime', 'userAccountControl']
        };

        client.search(settings.baseDN, searchOptions, (searchErr, searchRes) => {
          if (searchErr) {
            clearTimeout(timeout);
            console.error('LDAP search error:', searchErr);
            resolveOnce(NextResponse.json(
              { error: `Kullanıcı arama hatası: ${searchErr.message}` },
              { status: 500 }
            ));
            return;
          }

          let userDN = '';
          let foundUser = false;

          searchRes.on('searchEntry', (entry) => {
            foundUser = true;
            userDN = entry.pojo.objectName;

            // Also try to get DN from attributes if objectName is not available
            if (!userDN) {
              const attributes = entry.pojo.attributes;
              const dnAttr = attributes.find((a: any) => a.type === 'distinguishedName');
              if (dnAttr && dnAttr.values && dnAttr.values.length > 0) {
                userDN = dnAttr.values[0];
              }
            }
          });

          searchRes.on('error', (searchResErr) => {
            clearTimeout(timeout);
            console.error('LDAP search result error:', searchResErr);
            resolveOnce(NextResponse.json(
              { error: `Kullanıcı arama sonuç hatası: ${searchResErr.message}` },
              { status: 500 }
            ));
          });

          searchRes.on('end', () => {
            if (!foundUser || !userDN) {
              clearTimeout(timeout);
              console.error(`User not found or DN not available. Found: ${foundUser}, DN: ${userDN}, Username: ${username}`);
              resolveOnce(NextResponse.json(
                { error: `Kullanıcı bulunamadı veya DN alınamadı: ${username}` },
                { status: 404 }
              ));
              return;
            }

            console.log(`Found user ${username} with DN: ${userDN}`);

            // Try multiple unlock methods
            const unlockMethods = [
              // Method 1: Set lockoutTime to 0
              {
                operation: 'replace',
                modification: {
                  type: 'lockoutTime',
                  values: ['0']
                }
              },
              // Method 2: Remove lockoutTime attribute
              {
                operation: 'delete',
                modification: {
                  type: 'lockoutTime'
                }
              }
            ];

            let methodIndex = 0;

            const tryUnlock = () => {
              if (methodIndex >= unlockMethods.length) {
                clearTimeout(timeout);
                resolveOnce(NextResponse.json(
                  { error: `Tüm unlock yöntemleri başarısız oldu` },
                  { status: 500 }
                ));
                return;
              }

              const change = new ldap.Change(unlockMethods[methodIndex]);

              client.modify(userDN, change, (modifyErr) => {
                if (modifyErr) {
                  console.error(`LDAP modify error (method ${methodIndex + 1}):`, modifyErr);
                  methodIndex++;
                  tryUnlock(); // Try next method
                } else {
                  clearTimeout(timeout);
                  resolveOnce(NextResponse.json({
                    success: true,
                    message: `${username} kullanıcısı başarıyla unlock edildi (method ${methodIndex + 1})`
                  }));
                }
              });
            };

            tryUnlock();
          });
        });
      });
    });

  } catch (error) {
    console.error('Unlock user error:', error);
    return NextResponse.json(
      { error: 'Unlock işlemi sırasında hata oluştu' },
      { status: 500 }
    );
  }
}
