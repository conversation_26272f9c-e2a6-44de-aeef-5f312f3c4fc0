import NextAuth from 'next-auth';
import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import { getUserByUsername, validatePassword, updateLastLogin, initializeUsers } from '@/lib/auth';

// Initialize users file on startup
initializeUsers();

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        username: { label: 'Username', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        console.log('🔐 Server: Login attempt', {
          username: credentials?.username,
          hasPassword: !!credentials?.password,
          passwordLength: credentials?.password?.length
        });

        if (!credentials?.username || !credentials?.password) {
          console.log('❌ Server: Missing credentials');
          return null;
        }

        const user = getUserByUsername(credentials.username);
        console.log('👤 Server: User lookup', { found: !!user, username: credentials.username });

        if (!user) {
          console.log('❌ Server: User not found');
          return null;
        }

        const isValidPassword = validatePassword(credentials.password, user.password);
        console.log('🔑 Server: Password validation', { valid: isValidPassword });

        if (!isValidPassword) {
          console.log('❌ Server: Invalid password');
          return null;
        }

        // Update last login
        updateLastLogin(user.id);
        console.log('✅ Server: Login successful', { username: credentials.username, role: user.role });

        return {
          id: user.id,
          name: user.username,
          email: user.username,
          role: user.role,
          permissions: user.permissions,
        };
      },
    })
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.role = user.role;
        token.permissions = user.permissions;
      }

      // Handle session updates
      if (trigger === 'update' && session) {
        token.role = session.role;
        token.permissions = session.permissions;
      }

      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.permissions = token.permissions as any;
      }
      return session;
    },
  },
  pages: {
    signIn: '/login',
  },
  session: {
    strategy: 'jwt',
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',
  debug: true,
  logger: {
    error(code, metadata) {
      console.error('❌ NextAuth Error:', code, metadata);
    },
    warn(code) {
      console.warn('⚠️ NextAuth Warning:', code);
    },
    debug(code, metadata) {
      console.log('🔍 NextAuth Debug:', code, metadata);
    }
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
