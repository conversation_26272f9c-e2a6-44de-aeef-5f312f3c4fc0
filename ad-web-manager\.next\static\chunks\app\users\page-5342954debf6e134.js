(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9],{646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},836:(e,s,t)=>{Promise.resolve().then(t.bind(t,963))},963:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(5155),l=t(2115),r=t(6874),n=t.n(r),i=t(2108),c=t(6695),o=t(285),d=t(5127),m=t(3280),u=t(420),x=t(3978),h=t(8931);let j=(0,t(9946).A)("lock-open",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]);var p=t(1243),f=t(4186),k=t(3904),b=t(1154);function y(){let{data:e}=(0,i.useSession)(),s=(0,u.X)(),{notifications:t,addNotification:r,removeNotification:y}=(0,m.E$)(),[N,g]=(0,l.useState)([]),[w,v]=(0,l.useState)(null),[T,A]=(0,l.useState)(!1),[E,S]=(0,l.useState)(""),[B,M]=(0,l.useState)(""),[U,z]=(0,l.useState)(null),[K,Z]=(0,l.useState)(!1);if(!e)return null;if(!e.user.permissions.viewUsers)return(0,a.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center",children:(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"Erişim Reddedildi"}),(0,a.jsx)(c.BT,{children:"Bu sayfaya erişim yetkiniz bulunmamaktadır."})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)(o.$,{asChild:!0,children:(0,a.jsx)(n(),{href:"/",children:"Ana Sayfaya D\xf6n"})})})]})});(0,l.useEffect)(()=>{C()},[]);let C=async()=>{A(!0),S("");try{let e=await fetch("/api/locked-users");if(e.ok){let s=await e.json();g(s.users||[]),v(s.stats||null),0===s.users.length&&(S("Kilitlenen kullanıcı bulunamadı."),M("success"))}else{let s=await e.json();S(s.error||"Kullanıcılar y\xfcklenirken hata oluştu!"),M("error")}}catch(e){S("Bağlantı hatası!"),M("error")}finally{A(!1)}},D=async e=>{z(e),S("");try{let s=new AbortController,t=setTimeout(()=>s.abort(),7e4),a=await fetch("/api/unlock-user",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e}),signal:s.signal});clearTimeout(t);let l=await a.json();a.ok&&l.success?(r({variant:"success",title:"✅ Unlock Başarılı!",description:"".concat(e," kullanıcısının kilidi başarıyla kaldırıldı."),duration:8e3}),setTimeout(()=>{C()},1e3)):r({variant:"error",title:"❌ Unlock Başarısız!",description:l.error||"Unlock işlemi sırasında bir hata oluştu.",duration:8e3})}catch(s){"AbortError"===s.name?r({variant:"error",title:"⏱️ Zaman Aşımı!",description:"".concat(e," unlock işlemi 70 saniye i\xe7inde tamamlanamadı."),duration:8e3}):r({variant:"error",title:"❌ Bağlantı Hatası!",description:"Unlock işlemi sırasında beklenmeyen bir hata oluştu.",duration:8e3})}finally{z(null)}},R=async()=>{let e=N.filter(e=>e.lockoutTime);if(0===e.length){S("Kilitlenen kullanıcı bulunamadı."),M("error");return}if(!confirm("".concat(e.length," kullanıcının kilidini kaldırmak istediğinizden emin misiniz?")))return;Z(!0),S("Toplu unlock işlemi başlatıldı..."),M("success");let s=0,t=0,a=[];try{for(let l=0;l<e.length;l++){let r=e[l];S("İşleniyor: ".concat(r.username," (").concat(l+1,"/").concat(e.length,")"));try{let e=new AbortController,l=setTimeout(()=>e.abort(),3e4),n=await fetch("/api/unlock-user",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:r.username}),signal:e.signal});clearTimeout(l);let i=await n.json();n.ok&&i.success?(s++,a.push("✅ ".concat(r.username))):(t++,a.push("❌ ".concat(r.username,": ").concat(i.error||"LDAP hatası")))}catch(e){t++,"AbortError"===e.name?a.push("❌ ".concat(r.username,": Timeout (30s)")):a.push("❌ ".concat(r.username,": ").concat(e.message||"Bağlantı hatası"))}l<e.length-1&&await new Promise(e=>setTimeout(e,200))}}catch(e){console.error("Bulk unlock error:",e),S("Toplu unlock işlemi sırasında beklenmeyen hata oluştu."),M("error")}finally{Z(!1),0===t?r({variant:"success",title:"\uD83C\uDF89 Toplu Unlock Tamamlandı!",description:"".concat(s," kullanıcının kilidi başarıyla kaldırıldı."),duration:8e3}):s>0?r({variant:"success",title:"⚠️ Toplu Unlock Kısmen Başarılı",description:"".concat(s," başarılı, ").concat(t," hata oluştu."),duration:8e3}):r({variant:"error",title:"❌ Toplu Unlock Başarısız!",description:"Hi\xe7bir kullanıcının kilidi kaldırılamadı. ".concat(t," hata oluştu."),duration:8e3}),setTimeout(()=>{C()},1e3)}},L=e=>{try{return new Date(e).toLocaleString("tr-TR")}catch(s){return e}};return(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[s.isMobile?(0,a.jsx)(x.c,{currentPath:"/users"}):(0,a.jsx)(h.u,{currentPath:"/users"}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[w&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)(c.Zp,{className:"border-red-200",children:[(0,a.jsx)(c.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,a.jsx)(j,{className:"h-6 w-6 text-red-600"}),(0,a.jsx)(c.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Toplam Kilitli"})]})}),(0,a.jsx)(c.Wu,{className:"text-center",children:(0,a.jsx)("div",{className:"text-4xl font-bold text-red-600",children:w.totalLocked})})]}),(0,a.jsxs)(c.Zp,{className:"border-orange-200",children:[(0,a.jsx)(c.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,a.jsx)(p.A,{className:"h-6 w-6 text-orange-600"}),(0,a.jsx)(c.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Bug\xfcn Kilitlendi"})]})}),(0,a.jsx)(c.Wu,{className:"text-center",children:(0,a.jsx)("div",{className:"text-4xl font-bold text-orange-600",children:w.lockedToday})})]}),(0,a.jsxs)(c.Zp,{className:"border-yellow-200",children:[(0,a.jsx)(c.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,a.jsx)(f.A,{className:"h-6 w-6 text-yellow-600"}),(0,a.jsx)(c.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Şifre S\xfcresi Doldu"})]})}),(0,a.jsx)(c.Wu,{className:"text-center",children:(0,a.jsx)("div",{className:"text-4xl font-bold text-yellow-600",children:w.passwordExpired})})]}),(0,a.jsxs)(c.Zp,{className:"border-blue-200",children:[(0,a.jsx)(c.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,a.jsx)(k.A,{className:"h-6 w-6 text-blue-600"}),(0,a.jsx)(c.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Son G\xfcncelleme"})]})}),(0,a.jsx)(c.Wu,{className:"text-center",children:(0,a.jsx)("div",{className:"text-base font-medium text-muted-foreground",children:L(w.lastUpdated)})})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)("div",{className:"".concat(s.isMobile?"space-y-4":"flex justify-between items-center"),children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.ZB,{className:"".concat(s.isMobile?"text-xl":"text-2xl"),children:"Kilitlenen Kullanıcılar"}),(0,a.jsx)(c.BT,{children:"Active Directory'de kilitlenen kullanıcıları g\xf6r\xfcnt\xfcleyin ve unlock edin"})]}),(0,a.jsxs)("div",{className:"".concat(s.isMobile?"flex flex-col space-y-2":"flex space-x-2"),children:[N.filter(e=>e.lockoutTime).length>0&&e.user.permissions.unlockUsers&&(0,a.jsxs)(o.$,{variant:"destructive",onClick:R,disabled:K||T,className:s.isMobile?"w-full":"",size:(s.isMobile,"default"),children:[K?(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(j,{className:"mr-2 h-4 w-4"}),K?"T\xfcm\xfc Unlock Ediliyor...":s.isMobile?"T\xfcm\xfcn\xfc Unlock Et":"T\xfcm\xfcn\xfcn Kilidini Kaldır"]}),(0,a.jsxs)(o.$,{variant:"outline",onClick:C,disabled:T,className:s.isMobile?"w-full":"",size:(s.isMobile,"default"),children:[T?(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(k.A,{className:"mr-2 h-4 w-4"}),T?"Yenileniyor...":"Yenile"]})]})]})}),(0,a.jsx)(c.Wu,{children:T?(0,a.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,a.jsx)(b.A,{className:"h-8 w-8 animate-spin"}),(0,a.jsx)("span",{className:"ml-2",children:"Y\xfckleniyor..."})]}):N.length>0?s.isMobile?(0,a.jsx)("div",{className:"space-y-3",children:N.sort((e,s)=>e.lockoutTime||s.lockoutTime?e.lockoutTime?s.lockoutTime?new Date(s.lockoutTime).getTime()-new Date(e.lockoutTime).getTime():-1:1:0).map((s,t)=>(0,a.jsxs)(c.Zp,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg text-foreground",children:s.username}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:s.displayName||"Tam ad yok"})]}),s.lockoutTime&&(0,a.jsx)("div",{className:"bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium",children:"Kilitli"})]}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Kilitlenme:"}),(0,a.jsx)("span",{className:"font-medium",children:s.lockoutTime?L(s.lockoutTime):"Kilitli değil"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Şifre S\xfcresi:"}),(0,a.jsx)("span",{className:"font-medium ".concat(s.passwordExpires&&new Date(s.passwordExpires)<=new Date?"text-red-600":"text-green-600"),children:s.passwordExpires?L(s.passwordExpires):"Belirsiz"})]})]}),e.user.permissions.unlockUsers?(0,a.jsx)(o.$,{className:"w-full bg-green-600 hover:bg-green-700",onClick:()=>D(s.username),disabled:U===s.username,children:U===s.username?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Unlock Ediliyor..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j,{className:"mr-2 h-4 w-4"}),"Unlock"]})}):(0,a.jsx)("div",{className:"text-center text-muted-foreground text-sm py-2",children:"Unlock yetkisi yok"})]},t))}):(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(d.XI,{children:[(0,a.jsx)(d.A0,{children:(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nd,{children:"Kullanıcı Adı"}),(0,a.jsx)(d.nd,{children:"Tam Ad"}),(0,a.jsx)(d.nd,{children:"Kilitlenme Zamanı"}),(0,a.jsx)(d.nd,{children:"Şifre S\xfcresi"}),(0,a.jsx)(d.nd,{children:"İşlemler"})]})}),(0,a.jsx)(d.BF,{children:N.sort((e,s)=>e.lockoutTime||s.lockoutTime?e.lockoutTime?s.lockoutTime?new Date(s.lockoutTime).getTime()-new Date(e.lockoutTime).getTime():-1:1:0).map((s,t)=>(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nA,{className:"font-medium",children:s.username}),(0,a.jsx)(d.nA,{children:s.displayName||"-"}),(0,a.jsx)(d.nA,{children:s.lockoutTime?L(s.lockoutTime):"-"}),(0,a.jsx)(d.nA,{children:s.passwordExpires?(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat(new Date(s.passwordExpires)<=new Date?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:L(s.passwordExpires)}):"-"}),(0,a.jsx)(d.nA,{children:e.user.permissions.unlockUsers?(0,a.jsx)(o.$,{size:"sm",onClick:()=>D(s.username),disabled:U===s.username,className:"bg-green-600 hover:bg-green-700",children:U===s.username?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Unlock Ediliyor..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j,{className:"mr-2 h-4 w-4"}),"Unlock"]})}):(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Yetki yok"})})]},t))})]})}):!T&&(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:(0,a.jsx)("p",{children:"Kilitlenen kullanıcı bulunamadı."})})})]})]}),(0,a.jsx)(m.bL,{notifications:t,onRemove:y})]})}},1154:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1243:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3280:(e,s,t)=>{"use strict";t.d(s,{E$:()=>m,bL:()=>d});var a=t(5155),l=t(2115),r=t(646),n=t(4861),i=t(4416),c=t(9434);function o(e){let{id:s,title:t,description:o,variant:d,duration:m=8e3,onClose:u}=e,[x,h]=(0,l.useState)(!1),[j,p]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=setTimeout(()=>h(!0),100),s=setTimeout(()=>{f()},m);return()=>{clearTimeout(e),clearTimeout(s)}},[m]);let f=()=>{p(!0),setTimeout(()=>{u(s)},300)};return(0,a.jsx)("div",{className:(0,c.cn)("fixed bottom-4 right-4 z-50 w-96 max-w-sm p-4 rounded-lg shadow-lg border transition-all duration-300 transform",{"bg-green-50 border-green-200 text-green-800":"success"===d,"bg-red-50 border-red-200 text-red-800":"error"===d,"translate-x-full opacity-0":!x,"translate-x-0 opacity-100":x&&!j,"translate-x-full opacity-0":j}),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:"success"===d?(0,a.jsx)(r.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(n.A,{className:"h-5 w-5 text-red-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-semibold",children:t}),(0,a.jsx)("p",{className:"text-sm mt-1 whitespace-pre-line",children:o})]}),(0,a.jsx)("button",{onClick:f,className:"flex-shrink-0 ml-2 p-1 rounded-md hover:bg-black/5 transition-colors",children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})]})})}function d(e){let{notifications:s,onRemove:t}=e;return(0,a.jsx)("div",{className:"fixed bottom-0 right-0 z-50 p-4 space-y-2",children:s.map(e=>(0,a.jsx)(o,{...e,onClose:t},e.id))})}function m(){let[e,s]=(0,l.useState)([]);return{notifications:e,addNotification:e=>{let t=Math.random().toString(36).substr(2,9);s(s=>[...s,{...e,id:t}])},removeNotification:e=>{s(s=>s.filter(s=>s.id!==e))}}}},3904:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5127:(e,s,t)=>{"use strict";t.d(s,{A0:()=>n,BF:()=>i,Hj:()=>c,XI:()=>r,nA:()=>d,nd:()=>o});var a=t(5155);t(2115);var l=t(9434);function r(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,l.cn)("w-full caption-bottom text-sm",s),...t})})}function n(e){let{className:s,...t}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,l.cn)("[&_tr]:border-b",s),...t})}function i(e){let{className:s,...t}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,l.cn)("[&_tr:last-child]:border-0",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,l.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...t})}function o(e){let{className:s,...t}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,l.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,l.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...t})}}},e=>{var s=s=>e(e.s=s);e.O(0,[108,460,349,449,441,684,358],()=>s(836)),_N_E=e.O()}]);