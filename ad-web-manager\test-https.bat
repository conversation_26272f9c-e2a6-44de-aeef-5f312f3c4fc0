@echo off
title HTTPS Test
color 0C

echo.
echo ========================================
echo    HTTPS Baglantisi Test Ediliyor
echo ========================================
echo.

echo [1/3] SSL sertifikasi kontrolu...
if not exist "ssl\localhost.crt" (
    echo HATA: SSL sertifikasi bulunamadi!
    echo Lutfen once create-ssl-cert.bat calistirin.
    pause
    exit /b 1
)
echo SSL sertifikasi mevcut.

echo.
echo [2/3] Server durumu kontrol ediliyor...
curl -k -s -o nul -w "HTTP Status: %%{http_code}\nResponse Time: %%{time_total}s\n" https://localhost:3000/
if errorlevel 1 (
    echo HATA: Server'a baglanamadi!
    echo Lutfen server'in calistigini kontrol edin.
) else (
    echo Server'a basari<PERSON> baglandi.
)

echo.
echo [3/3] SSL sertifika bilgileri...
openssl x509 -in ssl\localhost.crt -text -noout | findstr "Subject:\|Not Before:\|Not After:\|DNS:"

echo.
echo ========================================
echo    Test Tamamlandi
echo ========================================
echo.
echo HTTPS URL: https://localhost:3000
echo.
pause
