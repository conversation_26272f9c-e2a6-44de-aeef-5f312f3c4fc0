import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import ldap from 'ldapjs';
import fs from 'fs';
import path from 'path';

interface LdapSettings {
  server: string;
  port: string;
  baseDN: string;
  username: string;
  password: string;
  useSSL: boolean;
}

interface UserInfo {
  username: string;
  displayName: string;
  passwordExpires?: string;
  daysUntilExpiry?: number;
  lastLogon?: string;
  department?: string;
  passwordNeverExpires: boolean;
  accountDisabled: boolean;
}

function getLdapSettings(): LdapSettings {
  try {
    const settingsPath = path.join(process.cwd(), 'ldap-settings.json');
    const settingsData = fs.readFileSync(settingsPath, 'utf8');
    return JSON.parse(settingsData);
  } catch (error) {
    console.error('Error reading LDAP settings:', error);
    throw new Error('LDAP settings not found');
  }
}

function convertWindowsTimeToDate(windowsTime: string): Date | null {
  try {
    const windowsTimeNum = parseInt(windowsTime);
    if (windowsTimeNum === 0 || isNaN(windowsTimeNum)) return null;
    
    // Windows FILETIME epoch starts at January 1, 1601
    // JavaScript Date epoch starts at January 1, 1970
    // Difference is *********** seconds
    const unixTime = (windowsTimeNum / ********) - ***********;
    return new Date(unixTime * 1000);
  } catch {
    return null;
  }
}

function calculatePasswordExpiry(pwdLastSet: string, maxPasswordAge: number): Date | null {
  const lastSetDate = convertWindowsTimeToDate(pwdLastSet);
  if (!lastSetDate) return null;
  
  // maxPasswordAge is in 100-nanosecond intervals (negative value)
  const maxAgeDays = Math.abs(maxPasswordAge) / (******** * 60 * 60 * 24);
  
  const expiryDate = new Date(lastSetDate);
  expiryDate.setDate(expiryDate.getDate() + maxAgeDays);
  
  return expiryDate;
}

async function searchUser(username: string): Promise<UserInfo | null> {
  const settings = getLdapSettings();
  
  return new Promise((resolve, reject) => {
    const ldapUrl = `${settings.useSSL ? 'ldaps' : 'ldap'}://${settings.server}:${settings.port}`;
    console.log('Searching for user:', username);
    
    const client = ldap.createClient({
      url: ldapUrl,
      timeout: 10000,
      connectTimeout: 10000,
    });

    client.bind(settings.username, settings.password, (bindErr) => {
      if (bindErr) {
        console.error('LDAP bind error:', bindErr);
        client.destroy();
        return reject(new Error('LDAP authentication failed'));
      }

      // First, get domain password policy
      const domainSearchOptions = {
        scope: 'base' as const,
        filter: '(objectClass=domain)',
        attributes: ['maxPwdAge']
      };

      client.search(settings.baseDN, domainSearchOptions, (domainErr, domainRes) => {
        if (domainErr) {
          console.error('Domain search error:', domainErr);
          client.destroy();
          return reject(new Error('Failed to get domain password policy'));
        }

        let maxPasswordAge = 0;

        domainRes.on('searchEntry', (entry) => {
          const maxPwdAge = entry.pojo.attributes.find((attr: any) => attr.type === 'maxPwdAge');
          if (maxPwdAge && maxPwdAge.values && maxPwdAge.values[0]) {
            maxPasswordAge = parseInt(maxPwdAge.values[0]);
          }
        });

        domainRes.on('end', () => {
          // Now search for the specific user
          const userSearchOptions = {
            scope: 'sub' as const,
            filter: `(sAMAccountName=${username})`,
            attributes: [
              'sAMAccountName',
              'displayName',
              'pwdLastSet',
              'lastLogon',
              'department',
              'userAccountControl'
            ]
          };

          client.search(settings.baseDN, userSearchOptions, (userErr, userRes) => {
            if (userErr) {
              console.error('User search error:', userErr);
              client.destroy();
              return reject(new Error('Failed to search user'));
            }

            let userFound: UserInfo | null = null;

            userRes.on('searchEntry', (entry) => {
              try {
                const attributes = entry.pojo.attributes;
                const foundUsername = attributes.find((attr: any) => attr.type === 'sAMAccountName')?.values[0];
                const displayName = attributes.find((attr: any) => attr.type === 'displayName')?.values[0] || foundUsername;
                const pwdLastSet = attributes.find((attr: any) => attr.type === 'pwdLastSet')?.values[0];
                const lastLogon = attributes.find((attr: any) => attr.type === 'lastLogon')?.values[0];
                const department = attributes.find((attr: any) => attr.type === 'department')?.values[0];
                const userAccountControl = attributes.find((attr: any) => attr.type === 'userAccountControl')?.values[0];

                if (!foundUsername) return;

                // Check account status
                const uacValue = userAccountControl ? parseInt(userAccountControl) : 0;
                const ACCOUNT_DISABLED = 0x2; // 2
                const DONT_EXPIRE_PASSWORD = 0x10000; // 65536
                
                const accountDisabled = !!(uacValue & ACCOUNT_DISABLED);
                const passwordNeverExpires = !!(uacValue & DONT_EXPIRE_PASSWORD);

                let passwordExpiry: Date | null = null;
                let daysUntilExpiry: number | undefined = undefined;

                // Calculate password expiry if password can expire
                if (!passwordNeverExpires && pwdLastSet && pwdLastSet !== '0' && maxPasswordAge !== 0) {
                  passwordExpiry = calculatePasswordExpiry(pwdLastSet, maxPasswordAge);
                  if (passwordExpiry) {
                    const now = new Date();
                    const timeDiff = passwordExpiry.getTime() - now.getTime();
                    daysUntilExpiry = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
                  }
                }

                userFound = {
                  username: foundUsername,
                  displayName,
                  passwordExpires: passwordExpiry?.toISOString(),
                  daysUntilExpiry,
                  lastLogon: lastLogon && lastLogon !== '0' ? convertWindowsTimeToDate(lastLogon)?.toISOString() : undefined,
                  department,
                  passwordNeverExpires,
                  accountDisabled
                };

                console.log(`Found user: ${foundUsername}, disabled: ${accountDisabled}, never expires: ${passwordNeverExpires}, days until expiry: ${daysUntilExpiry}`);
              } catch (error) {
                console.error('Error processing user entry:', error);
              }
            });

            userRes.on('end', () => {
              client.destroy();
              resolve(userFound);
            });

            userRes.on('error', (error) => {
              console.error('User search result error:', error);
              client.destroy();
              reject(new Error('Error during user search'));
            });
          });
        });

        domainRes.on('error', (error) => {
          console.error('Domain search result error:', error);
          client.destroy();
          reject(new Error('Error during domain search'));
        });
      });
    });

    client.on('error', (error) => {
      console.error('LDAP client error:', error);
      reject(new Error('LDAP connection failed'));
    });
  });
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user.permissions.unlockUsers) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');
    
    if (!username) {
      return NextResponse.json(
        { error: 'Username parameter is required' },
        { status: 400 }
      );
    }

    const user = await searchUser(username);
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ user });
  } catch (error: any) {
    console.error('Search user API error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to search user' },
      { status: 500 }
    );
  }
}
