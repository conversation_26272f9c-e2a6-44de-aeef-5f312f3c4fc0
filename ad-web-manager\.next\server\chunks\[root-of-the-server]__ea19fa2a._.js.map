{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport fs from 'fs';\nimport path from 'path';\n\nconst USERS_FILE = path.join(process.cwd(), 'users.json');\n\nexport interface User {\n  id: string;\n  username: string;\n  password: string;\n  role: 'admin' | 'user';\n  permissions: {\n    viewUsers: boolean;\n    unlockUsers: boolean;\n    manageSettings: boolean;\n    manageUsers: boolean;\n  };\n  createdAt: string;\n  lastLogin?: string;\n}\n\n// Default admin user\nconst defaultAdmin: User = {\n  id: '1',\n  username: 'admin',\n  password: bcrypt.hashSync('581326Ob', 10),\n  role: 'admin',\n  permissions: {\n    viewUsers: true,\n    unlockUsers: true,\n    manageSettings: true,\n    manageUsers: true,\n  },\n  createdAt: new Date().toISOString(),\n};\n\nexport function initializeUsers() {\n  if (!fs.existsSync(USERS_FILE)) {\n    fs.writeFileSync(USERS_FILE, JSON.stringify([defaultAdmin], null, 2));\n  }\n}\n\nexport function getUsers(): User[] {\n  try {\n    if (!fs.existsSync(USERS_FILE)) {\n      initializeUsers();\n    }\n    const data = fs.readFileSync(USERS_FILE, 'utf8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error('Error reading users file:', error);\n    return [defaultAdmin];\n  }\n}\n\nexport function saveUsers(users: User[]) {\n  try {\n    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));\n  } catch (error) {\n    console.error('Error saving users file:', error);\n    throw new Error('Failed to save users');\n  }\n}\n\nexport function getUserByUsername(username: string): User | null {\n  const users = getUsers();\n  return users.find(user => user.username === username) || null;\n}\n\nexport function getUserById(id: string): User | null {\n  const users = getUsers();\n  return users.find(user => user.id === id) || null;\n}\n\nexport function validatePassword(password: string, hashedPassword: string): boolean {\n  return bcrypt.compareSync(password, hashedPassword);\n}\n\n\n\nexport function hashPassword(password: string): string {\n  return bcrypt.hashSync(password, 10);\n}\n\nexport function createUser(userData: Omit<User, 'id' | 'createdAt' | 'password'> & { password: string }): User {\n  const users = getUsers();\n  \n  // Check if username already exists\n  if (users.find(user => user.username === userData.username)) {\n    throw new Error('Username already exists');\n  }\n\n  const newUser: User = {\n    ...userData,\n    id: Date.now().toString(),\n    password: hashPassword(userData.password),\n    createdAt: new Date().toISOString(),\n  };\n\n  users.push(newUser);\n  saveUsers(users);\n  \n  return newUser;\n}\n\nexport function updateUser(id: string, updates: Partial<Omit<User, 'id' | 'createdAt'>>): User {\n  const users = getUsers();\n  const userIndex = users.findIndex(user => user.id === id);\n  \n  if (userIndex === -1) {\n    throw new Error('User not found');\n  }\n\n  // If password is being updated, hash it\n  if (updates.password) {\n    updates.password = hashPassword(updates.password);\n  }\n\n  users[userIndex] = { ...users[userIndex], ...updates };\n  saveUsers(users);\n  \n  return users[userIndex];\n}\n\nexport function deleteUser(id: string): boolean {\n  const users = getUsers();\n  const userIndex = users.findIndex(user => user.id === id);\n  \n  if (userIndex === -1) {\n    return false;\n  }\n\n  // Don't allow deleting the default admin\n  if (users[userIndex].username === 'admin') {\n    throw new Error('Cannot delete default admin user');\n  }\n\n  users.splice(userIndex, 1);\n  saveUsers(users);\n  \n  return true;\n}\n\nexport function updateLastLogin(userId: string) {\n  const users = getUsers();\n  const userIndex = users.findIndex(user => user.id === userId);\n  \n  if (userIndex !== -1) {\n    users[userIndex].lastLogin = new Date().toISOString();\n    saveUsers(users);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAiB5C,qBAAqB;AACrB,MAAM,eAAqB;IACzB,IAAI;IACJ,UAAU;IACV,UAAU,mIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,YAAY;IACtC,MAAM;IACN,aAAa;QACX,WAAW;QACX,aAAa;QACb,gBAAgB;QAChB,aAAa;IACf;IACA,WAAW,IAAI,OAAO,WAAW;AACnC;AAEO,SAAS;IACd,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;QAC9B,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC;YAAC;SAAa,EAAE,MAAM;IACpE;AACF;AAEO,SAAS;IACd,IAAI;QACF,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;YAC9B;QACF;QACA,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,YAAY;QACzC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAC;SAAa;IACvB;AACF;AAEO,SAAS,UAAU,KAAa;IACrC,IAAI;QACF,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC,OAAO,MAAM;IAC3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,SAAS,kBAAkB,QAAgB;IAChD,MAAM,QAAQ;IACd,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,aAAa;AAC3D;AAEO,SAAS,YAAY,EAAU;IACpC,MAAM,QAAQ;IACd,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO;AAC/C;AAEO,SAAS,iBAAiB,QAAgB,EAAE,cAAsB;IACvE,OAAO,mIAAA,CAAA,UAAM,CAAC,WAAW,CAAC,UAAU;AACtC;AAIO,SAAS,aAAa,QAAgB;IAC3C,OAAO,mIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,UAAU;AACnC;AAEO,SAAS,WAAW,QAA4E;IACrG,MAAM,QAAQ;IAEd,mCAAmC;IACnC,IAAI,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,SAAS,QAAQ,GAAG;QAC3D,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAgB;QACpB,GAAG,QAAQ;QACX,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,UAAU,aAAa,SAAS,QAAQ;QACxC,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,MAAM,IAAI,CAAC;IACX,UAAU;IAEV,OAAO;AACT;AAEO,SAAS,WAAW,EAAU,EAAE,OAAgD;IACrF,MAAM,QAAQ;IACd,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEtD,IAAI,cAAc,CAAC,GAAG;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,wCAAwC;IACxC,IAAI,QAAQ,QAAQ,EAAE;QACpB,QAAQ,QAAQ,GAAG,aAAa,QAAQ,QAAQ;IAClD;IAEA,KAAK,CAAC,UAAU,GAAG;QAAE,GAAG,KAAK,CAAC,UAAU;QAAE,GAAG,OAAO;IAAC;IACrD,UAAU;IAEV,OAAO,KAAK,CAAC,UAAU;AACzB;AAEO,SAAS,WAAW,EAAU;IACnC,MAAM,QAAQ;IACd,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEtD,IAAI,cAAc,CAAC,GAAG;QACpB,OAAO;IACT;IAEA,yCAAyC;IACzC,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,KAAK,SAAS;QACzC,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,MAAM,CAAC,WAAW;IACxB,UAAU;IAEV,OAAO;AACT;AAEO,SAAS,gBAAgB,MAAc;IAC5C,MAAM,QAAQ;IACd,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEtD,IAAI,cAAc,CAAC,GAAG;QACpB,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,OAAO,WAAW;QACnD,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials';\nimport { getUserByUsername, validatePassword, updateLastLogin, initializeUsers } from '@/lib/auth';\n\n// Initialize users file on startup\ninitializeUsers();\n\nexport const authOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        username: { label: 'Username', type: 'text' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        const user = getUserByUsername(credentials.username);\n\n        if (!user) {\n          return null;\n        }\n\n        const isValidPassword = validatePassword(credentials.password, user.password);\n\n        if (!isValidPassword) {\n          return null;\n        }\n\n        // Update last login\n        updateLastLogin(user.id);\n\n        return {\n          id: user.id,\n          name: user.username,\n          email: user.username,\n          role: user.role,\n          permissions: user.permissions,\n        };\n      },\n    })\n  ],\n  callbacks: {\n    async jwt({ token, user, trigger, session }) {\n      if (user) {\n        token.role = user.role;\n        token.permissions = user.permissions;\n      }\n\n      // Handle session updates\n      if (trigger === 'update' && session) {\n        token.role = session.role;\n        token.permissions = session.permissions;\n      }\n\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!;\n        session.user.role = token.role as string;\n        session.user.permissions = token.permissions as any;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/login',\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',\n};\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,mCAAmC;AACnC,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD;AAEP,MAAM,cAAc;IACzB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAO;gBAC5C,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,QAAQ;gBAEnD,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,QAAQ,EAAE,KAAK,QAAQ;gBAE5E,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,oBAAoB;gBACpB,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;gBAEvB,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,QAAQ;oBACnB,OAAO,KAAK,QAAQ;oBACpB,MAAM,KAAK,IAAI;oBACf,aAAa,KAAK,WAAW;gBAC/B;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACzC,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,WAAW,GAAG,KAAK,WAAW;YACtC;YAEA,yBAAyB;YACzB,IAAI,YAAY,YAAY,SAAS;gBACnC,MAAM,IAAI,GAAG,QAAQ,IAAI;gBACzB,MAAM,WAAW,GAAG,QAAQ,WAAW;YACzC;YAEA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;YAC9C;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe,IAAI;AACzC;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/app/api/search-user/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '../auth/[...nextauth]/route';\nimport ldap from 'ldapjs';\nimport fs from 'fs';\nimport path from 'path';\n\ninterface LdapSettings {\n  server: string;\n  port: string;\n  baseDN: string;\n  username: string;\n  password: string;\n  useSSL: boolean;\n}\n\ninterface UserInfo {\n  username: string;\n  displayName: string;\n  passwordExpires?: string;\n  daysUntilExpiry?: number;\n  lastLogon?: string;\n  department?: string;\n  passwordNeverExpires: boolean;\n  accountDisabled: boolean;\n}\n\nfunction getLdapSettings(): LdapSettings {\n  try {\n    const settingsPath = path.join(process.cwd(), 'ldap-settings.json');\n    const settingsData = fs.readFileSync(settingsPath, 'utf8');\n    return JSON.parse(settingsData);\n  } catch (error) {\n    console.error('Error reading LDAP settings:', error);\n    throw new Error('LDAP settings not found');\n  }\n}\n\nfunction convertWindowsTimeToDate(windowsTime: string): Date | null {\n  try {\n    const windowsTimeNum = parseInt(windowsTime);\n    if (windowsTimeNum === 0 || isNaN(windowsTimeNum)) return null;\n    \n    // Windows FILETIME epoch starts at January 1, 1601\n    // JavaScript Date epoch starts at January 1, 1970\n    // Difference is *********** seconds\n    const unixTime = (windowsTimeNum / ********) - ***********;\n    return new Date(unixTime * 1000);\n  } catch {\n    return null;\n  }\n}\n\nfunction calculatePasswordExpiry(pwdLastSet: string, maxPasswordAge: number): Date | null {\n  const lastSetDate = convertWindowsTimeToDate(pwdLastSet);\n  if (!lastSetDate) return null;\n  \n  // maxPasswordAge is in 100-nanosecond intervals (negative value)\n  const maxAgeDays = Math.abs(maxPasswordAge) / (******** * 60 * 60 * 24);\n  \n  const expiryDate = new Date(lastSetDate);\n  expiryDate.setDate(expiryDate.getDate() + maxAgeDays);\n  \n  return expiryDate;\n}\n\nasync function searchUser(username: string): Promise<UserInfo | null> {\n  const settings = getLdapSettings();\n  \n  return new Promise((resolve, reject) => {\n    const ldapUrl = `${settings.useSSL ? 'ldaps' : 'ldap'}://${settings.server}:${settings.port}`;\n    console.log('Searching for user:', username);\n    \n    const client = ldap.createClient({\n      url: ldapUrl,\n      timeout: 10000,\n      connectTimeout: 10000,\n    });\n\n    client.bind(settings.username, settings.password, (bindErr) => {\n      if (bindErr) {\n        console.error('LDAP bind error:', bindErr);\n        client.destroy();\n        return reject(new Error('LDAP authentication failed'));\n      }\n\n      // First, get domain password policy\n      const domainSearchOptions = {\n        scope: 'base' as const,\n        filter: '(objectClass=domain)',\n        attributes: ['maxPwdAge']\n      };\n\n      client.search(settings.baseDN, domainSearchOptions, (domainErr, domainRes) => {\n        if (domainErr) {\n          console.error('Domain search error:', domainErr);\n          client.destroy();\n          return reject(new Error('Failed to get domain password policy'));\n        }\n\n        let maxPasswordAge = 0;\n\n        domainRes.on('searchEntry', (entry) => {\n          const maxPwdAge = entry.pojo.attributes.find((attr: any) => attr.type === 'maxPwdAge');\n          if (maxPwdAge && maxPwdAge.values && maxPwdAge.values[0]) {\n            maxPasswordAge = parseInt(maxPwdAge.values[0]);\n          }\n        });\n\n        domainRes.on('end', () => {\n          // Now search for the specific user\n          const userSearchOptions = {\n            scope: 'sub' as const,\n            filter: `(sAMAccountName=${username})`,\n            attributes: [\n              'sAMAccountName',\n              'displayName',\n              'pwdLastSet',\n              'lastLogon',\n              'department',\n              'userAccountControl'\n            ]\n          };\n\n          client.search(settings.baseDN, userSearchOptions, (userErr, userRes) => {\n            if (userErr) {\n              console.error('User search error:', userErr);\n              client.destroy();\n              return reject(new Error('Failed to search user'));\n            }\n\n            let userFound: UserInfo | null = null;\n\n            userRes.on('searchEntry', (entry) => {\n              try {\n                const attributes = entry.pojo.attributes;\n                const foundUsername = attributes.find((attr: any) => attr.type === 'sAMAccountName')?.values[0];\n                const displayName = attributes.find((attr: any) => attr.type === 'displayName')?.values[0] || foundUsername;\n                const pwdLastSet = attributes.find((attr: any) => attr.type === 'pwdLastSet')?.values[0];\n                const lastLogon = attributes.find((attr: any) => attr.type === 'lastLogon')?.values[0];\n                const department = attributes.find((attr: any) => attr.type === 'department')?.values[0];\n                const userAccountControl = attributes.find((attr: any) => attr.type === 'userAccountControl')?.values[0];\n\n                if (!foundUsername) return;\n\n                // Check account status\n                const uacValue = userAccountControl ? parseInt(userAccountControl) : 0;\n                const ACCOUNT_DISABLED = 0x2; // 2\n                const DONT_EXPIRE_PASSWORD = 0x10000; // 65536\n                \n                const accountDisabled = !!(uacValue & ACCOUNT_DISABLED);\n                const passwordNeverExpires = !!(uacValue & DONT_EXPIRE_PASSWORD);\n\n                let passwordExpiry: Date | null = null;\n                let daysUntilExpiry: number | undefined = undefined;\n\n                // Calculate password expiry if password can expire\n                if (!passwordNeverExpires && pwdLastSet && pwdLastSet !== '0' && maxPasswordAge !== 0) {\n                  passwordExpiry = calculatePasswordExpiry(pwdLastSet, maxPasswordAge);\n                  if (passwordExpiry) {\n                    const now = new Date();\n                    const timeDiff = passwordExpiry.getTime() - now.getTime();\n                    daysUntilExpiry = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));\n                  }\n                }\n\n                userFound = {\n                  username: foundUsername,\n                  displayName,\n                  passwordExpires: passwordExpiry?.toISOString(),\n                  daysUntilExpiry,\n                  lastLogon: lastLogon && lastLogon !== '0' ? convertWindowsTimeToDate(lastLogon)?.toISOString() : undefined,\n                  department,\n                  passwordNeverExpires,\n                  accountDisabled\n                };\n\n                console.log(`Found user: ${foundUsername}, disabled: ${accountDisabled}, never expires: ${passwordNeverExpires}, days until expiry: ${daysUntilExpiry}`);\n              } catch (error) {\n                console.error('Error processing user entry:', error);\n              }\n            });\n\n            userRes.on('end', () => {\n              client.destroy();\n              resolve(userFound);\n            });\n\n            userRes.on('error', (error) => {\n              console.error('User search result error:', error);\n              client.destroy();\n              reject(new Error('Error during user search'));\n            });\n          });\n        });\n\n        domainRes.on('error', (error) => {\n          console.error('Domain search result error:', error);\n          client.destroy();\n          reject(new Error('Error during domain search'));\n        });\n      });\n    });\n\n    client.on('error', (error) => {\n      console.error('LDAP client error:', error);\n      reject(new Error('LDAP connection failed'));\n    });\n  });\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session || !session.user.permissions.unlockUsers) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      );\n    }\n\n    const { searchParams } = new URL(request.url);\n    const username = searchParams.get('username');\n    \n    if (!username) {\n      return NextResponse.json(\n        { error: 'Username parameter is required' },\n        { status: 400 }\n      );\n    }\n\n    const user = await searchUser(username);\n    \n    if (!user) {\n      return NextResponse.json(\n        { error: 'User not found' },\n        { status: 404 }\n      );\n    }\n    \n    return NextResponse.json({ user });\n  } catch (error: any) {\n    console.error('Search user API error:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to search user' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAsBA,SAAS;IACP,IAAI;QACF,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAC9C,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,cAAc;QACnD,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,SAAS,yBAAyB,WAAmB;IACnD,IAAI;QACF,MAAM,iBAAiB,SAAS;QAChC,IAAI,mBAAmB,KAAK,MAAM,iBAAiB,OAAO;QAE1D,mDAAmD;QACnD,kDAAkD;QAClD,oCAAoC;QACpC,MAAM,WAAW,AAAC,iBAAiB,WAAY;QAC/C,OAAO,IAAI,KAAK,WAAW;IAC7B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,SAAS,wBAAwB,UAAkB,EAAE,cAAsB;IACzE,MAAM,cAAc,yBAAyB;IAC7C,IAAI,CAAC,aAAa,OAAO;IAEzB,iEAAiE;IACjE,MAAM,aAAa,KAAK,GAAG,CAAC,kBAAkB,CAAC,WAAW,KAAK,KAAK,EAAE;IAEtE,MAAM,aAAa,IAAI,KAAK;IAC5B,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;IAE1C,OAAO;AACT;AAEA,eAAe,WAAW,QAAgB;IACxC,MAAM,WAAW;IAEjB,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,UAAU,GAAG,SAAS,MAAM,GAAG,UAAU,OAAO,GAAG,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,IAAI,EAAE;QAC7F,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,MAAM,SAAS,qGAAA,CAAA,UAAI,CAAC,YAAY,CAAC;YAC/B,KAAK;YACL,SAAS;YACT,gBAAgB;QAClB;QAEA,OAAO,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,QAAQ,EAAE,CAAC;YACjD,IAAI,SAAS;gBACX,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,OAAO,OAAO;gBACd,OAAO,OAAO,IAAI,MAAM;YAC1B;YAEA,oCAAoC;YACpC,MAAM,sBAAsB;gBAC1B,OAAO;gBACP,QAAQ;gBACR,YAAY;oBAAC;iBAAY;YAC3B;YAEA,OAAO,MAAM,CAAC,SAAS,MAAM,EAAE,qBAAqB,CAAC,WAAW;gBAC9D,IAAI,WAAW;oBACb,QAAQ,KAAK,CAAC,wBAAwB;oBACtC,OAAO,OAAO;oBACd,OAAO,OAAO,IAAI,MAAM;gBAC1B;gBAEA,IAAI,iBAAiB;gBAErB,UAAU,EAAE,CAAC,eAAe,CAAC;oBAC3B,MAAM,YAAY,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAc,KAAK,IAAI,KAAK;oBAC1E,IAAI,aAAa,UAAU,MAAM,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE;wBACxD,iBAAiB,SAAS,UAAU,MAAM,CAAC,EAAE;oBAC/C;gBACF;gBAEA,UAAU,EAAE,CAAC,OAAO;oBAClB,mCAAmC;oBACnC,MAAM,oBAAoB;wBACxB,OAAO;wBACP,QAAQ,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;wBACtC,YAAY;4BACV;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;oBACH;oBAEA,OAAO,MAAM,CAAC,SAAS,MAAM,EAAE,mBAAmB,CAAC,SAAS;wBAC1D,IAAI,SAAS;4BACX,QAAQ,KAAK,CAAC,sBAAsB;4BACpC,OAAO,OAAO;4BACd,OAAO,OAAO,IAAI,MAAM;wBAC1B;wBAEA,IAAI,YAA6B;wBAEjC,QAAQ,EAAE,CAAC,eAAe,CAAC;4BACzB,IAAI;gCACF,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU;gCACxC,MAAM,gBAAgB,WAAW,IAAI,CAAC,CAAC,OAAc,KAAK,IAAI,KAAK,mBAAmB,MAAM,CAAC,EAAE;gCAC/F,MAAM,cAAc,WAAW,IAAI,CAAC,CAAC,OAAc,KAAK,IAAI,KAAK,gBAAgB,MAAM,CAAC,EAAE,IAAI;gCAC9F,MAAM,aAAa,WAAW,IAAI,CAAC,CAAC,OAAc,KAAK,IAAI,KAAK,eAAe,MAAM,CAAC,EAAE;gCACxF,MAAM,YAAY,WAAW,IAAI,CAAC,CAAC,OAAc,KAAK,IAAI,KAAK,cAAc,MAAM,CAAC,EAAE;gCACtF,MAAM,aAAa,WAAW,IAAI,CAAC,CAAC,OAAc,KAAK,IAAI,KAAK,eAAe,MAAM,CAAC,EAAE;gCACxF,MAAM,qBAAqB,WAAW,IAAI,CAAC,CAAC,OAAc,KAAK,IAAI,KAAK,uBAAuB,MAAM,CAAC,EAAE;gCAExG,IAAI,CAAC,eAAe;gCAEpB,uBAAuB;gCACvB,MAAM,WAAW,qBAAqB,SAAS,sBAAsB;gCACrE,MAAM,mBAAmB,KAAK,IAAI;gCAClC,MAAM,uBAAuB,SAAS,QAAQ;gCAE9C,MAAM,kBAAkB,CAAC,CAAC,CAAC,WAAW,gBAAgB;gCACtD,MAAM,uBAAuB,CAAC,CAAC,CAAC,WAAW,oBAAoB;gCAE/D,IAAI,iBAA8B;gCAClC,IAAI,kBAAsC;gCAE1C,mDAAmD;gCACnD,IAAI,CAAC,wBAAwB,cAAc,eAAe,OAAO,mBAAmB,GAAG;oCACrF,iBAAiB,wBAAwB,YAAY;oCACrD,IAAI,gBAAgB;wCAClB,MAAM,MAAM,IAAI;wCAChB,MAAM,WAAW,eAAe,OAAO,KAAK,IAAI,OAAO;wCACvD,kBAAkB,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;oCAC7D;gCACF;gCAEA,YAAY;oCACV,UAAU;oCACV;oCACA,iBAAiB,gBAAgB;oCACjC;oCACA,WAAW,aAAa,cAAc,MAAM,yBAAyB,YAAY,gBAAgB;oCACjG;oCACA;oCACA;gCACF;gCAEA,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,cAAc,YAAY,EAAE,gBAAgB,iBAAiB,EAAE,qBAAqB,qBAAqB,EAAE,iBAAiB;4BACzJ,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,gCAAgC;4BAChD;wBACF;wBAEA,QAAQ,EAAE,CAAC,OAAO;4BAChB,OAAO,OAAO;4BACd,QAAQ;wBACV;wBAEA,QAAQ,EAAE,CAAC,SAAS,CAAC;4BACnB,QAAQ,KAAK,CAAC,6BAA6B;4BAC3C,OAAO,OAAO;4BACd,OAAO,IAAI,MAAM;wBACnB;oBACF;gBACF;gBAEA,UAAU,EAAE,CAAC,SAAS,CAAC;oBACrB,QAAQ,KAAK,CAAC,+BAA+B;oBAC7C,OAAO,OAAO;oBACd,OAAO,IAAI,MAAM;gBACnB;YACF;QACF;QAEA,OAAO,EAAE,CAAC,SAAS,CAAC;YAClB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO,IAAI,MAAM;QACnB;IACF;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,0JAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAElC,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,WAAW;QAE9B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAClC,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAAwB,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}