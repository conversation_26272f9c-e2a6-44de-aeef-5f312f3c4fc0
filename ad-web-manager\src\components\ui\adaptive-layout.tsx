'use client';

import React from 'react';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { MobileNav } from './mobile-nav';
import { DesktopNav } from './desktop-nav';

interface AdaptiveLayoutProps {
  children: React.ReactNode;
  currentPath?: string;
}

export function AdaptiveLayout({ children, currentPath = '/' }: AdaptiveLayoutProps) {
  const device = useDeviceDetection();

  return (
    <div className="min-h-screen bg-background">
      {/* Adaptive Navigation */}
      {device.isMobile ? (
        <MobileNav currentPath={currentPath} />
      ) : (
        <DesktopNav currentPath={currentPath} />
      )}
      
      {/* Main Content */}
      <main className={`
        ${device.isMobile ? 'pb-safe' : ''}
        ${device.deviceType === 'mobile' ? 'px-4 py-4' : 'container mx-auto px-4 py-6'}
      `}>
        {children}
      </main>
      
      {/* Debug info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-2 right-2 bg-black/80 text-white text-xs p-2 rounded z-50">
          <div>Device: {device.deviceType}</div>
          <div>Width: {device.screenWidth}px</div>
          <div>Mobile: {device.isMobile ? 'Yes' : 'No'}</div>
        </div>
      )}
    </div>
  );
}

// Adaptive Container
export function AdaptiveContainer({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  const device = useDeviceDetection();
  
  return (
    <div className={`
      ${device.isMobile ? 'space-y-4' : 'space-y-6'}
      ${className}
    `}>
      {children}
    </div>
  );
}

// Adaptive Card Grid
export function AdaptiveCardGrid({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  const device = useDeviceDetection();
  
  const gridClasses = {
    mobile: 'grid-cols-1 gap-4',
    tablet: 'grid-cols-2 gap-4',
    desktop: 'grid-cols-3 gap-6'
  };
  
  return (
    <div className={`
      grid 
      ${gridClasses[device.deviceType]}
      ${className}
    `}>
      {children}
    </div>
  );
}

// Adaptive Button Group
export function AdaptiveButtonGroup({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  const device = useDeviceDetection();
  
  return (
    <div className={`
      flex 
      ${device.isMobile ? 'flex-col gap-2' : 'flex-row gap-4'}
      ${className}
    `}>
      {children}
    </div>
  );
}
