import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!session.user.permissions?.unlockUsers) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { username } = await request.json();
    
    if (!username) {
      return NextResponse.json(
        { error: 'Kullanıcı adı gereklidir' },
        { status: 400 }
      );
    }

    // Simple PowerShell command to unlock user
    const psCommand = `Unlock-ADAccount -Identity "${username}"`;

    try {
      const { stdout, stderr } = await execAsync(`powershell -Command "${psCommand}"`, {
        timeout: 15000
      });

      // If no error, consider it successful
      return NextResponse.json({
        success: true,
        message: `${username} kullanıcısı PowerShell ile unlock edildi`
      });
    } catch (error: any) {
      // PowerShell not available or AD module not installed
      return NextResponse.json(
        { error: `PowerShell AD modülü mevcut değil veya erişim yok` },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('PowerShell unlock error:', error);
    return NextResponse.json(
      { error: `PowerShell unlock hatası: ${error.message}` },
      { status: 500 }
    );
  }
}
