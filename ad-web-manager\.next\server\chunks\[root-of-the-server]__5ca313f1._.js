module.exports = {

"[project]/.next-internal/server/app/api/locked-users/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/app/api/locked-users/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ldapjs$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ldapjs/lib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
const SETTINGS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'ldap-settings.json');
function loadSettings() {
    try {
        if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(SETTINGS_FILE)) {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(SETTINGS_FILE, 'utf8');
            return JSON.parse(data);
        }
        return null;
    } catch (error) {
        console.error('Error loading settings:', error);
        return null;
    }
}
async function GET() {
    try {
        const settings = loadSettings();
        if (!settings) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'LDAP ayarları bulunamadı. Lütfen önce ayarları yapılandırın.'
            }, {
                status: 400
            });
        }
        if (!settings.server || !settings.baseDN || !settings.username || !settings.password) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'LDAP ayarları eksik. Lütfen ayarları kontrol edin.'
            }, {
                status: 400
            });
        }
        const protocol = settings.useSSL ? 'ldaps' : 'ldap';
        const url = `${protocol}://${settings.server}:${settings.port}`;
        const client = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ldapjs$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].createClient({
            url: url,
            timeout: 30000,
            connectTimeout: 10000,
            tlsOptions: {
                rejectUnauthorized: false
            }
        });
        return new Promise((resolve)=>{
            let isResolved = false;
            const resolveOnce = (response)=>{
                if (!isResolved) {
                    isResolved = true;
                    client.destroy();
                    resolve(response);
                }
            };
            const timeout = setTimeout(()=>{
                resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'LDAP bağlantısı zaman aşımına uğradı'
                }, {
                    status: 408
                }));
            }, 35000);
            client.on('error', (err)=>{
                clearTimeout(timeout);
                console.error('LDAP connection error:', err);
                resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: `LDAP bağlantı hatası: ${err.message}`
                }, {
                    status: 500
                }));
            });
            client.bind(settings.username, settings.password, (err)=>{
                if (err) {
                    clearTimeout(timeout);
                    console.error('LDAP bind error:', err);
                    resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: `LDAP kimlik doğrulama hatası: ${err.message}`
                    }, {
                        status: 401
                    }));
                    return;
                }
                // Search for locked users and users with expired passwords
                const searchFilter = '(&(objectClass=user)(|(lockoutTime>=1)(userAccountControl:1.2.840.113556.1.4.803:=8388608)))';
                const searchOptions = {
                    scope: 'sub',
                    filter: searchFilter,
                    attributes: [
                        'sAMAccountName',
                        'displayName',
                        'lockoutTime',
                        'pwdLastSet',
                        'accountExpires',
                        'lastLogon',
                        'userAccountControl',
                        'msDS-UserPasswordExpiryTimeComputed'
                    ]
                };
                const lockedUsers = [];
                client.search(settings.baseDN, searchOptions, (searchErr, searchRes)=>{
                    if (searchErr) {
                        clearTimeout(timeout);
                        console.error('LDAP search error:', searchErr);
                        resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            error: `LDAP arama hatası: ${searchErr.message}`
                        }, {
                            status: 500
                        }));
                        return;
                    }
                    searchRes.on('searchEntry', (entry)=>{
                        try {
                            const attributes = entry.pojo.attributes;
                            const getAttributeValue = (name)=>{
                                const attr = attributes.find((a)=>a.type === name);
                                return attr && attr.values && attr.values.length > 0 ? attr.values[0] : '';
                            };
                            const lockoutTime = getAttributeValue('lockoutTime');
                            const pwdLastSet = getAttributeValue('pwdLastSet');
                            const accountExpires = getAttributeValue('accountExpires');
                            const lastLogon = getAttributeValue('lastLogon');
                            const passwordExpiryComputed = getAttributeValue('msDS-UserPasswordExpiryTimeComputed');
                            // Convert Windows FILETIME to JavaScript Date
                            const convertFileTime = (fileTime)=>{
                                if (!fileTime || fileTime === '0' || fileTime === '9223372036854775807') return '';
                                try {
                                    const windowsEpoch = new Date('1601-01-01T00:00:00Z').getTime();
                                    const jsDate = new Date(windowsEpoch + parseInt(fileTime) / 10000);
                                    return jsDate.toISOString();
                                } catch  {
                                    return '';
                                }
                            };
                            // Get password expiration date
                            let passwordExpires = '';
                            if (passwordExpiryComputed && passwordExpiryComputed !== '0') {
                                // Use the computed password expiry time from AD
                                passwordExpires = convertFileTime(passwordExpiryComputed);
                            } else if (pwdLastSet && pwdLastSet !== '0') {
                                // Fallback: calculate based on pwdLastSet + 90 days
                                try {
                                    const pwdDate = convertFileTime(pwdLastSet);
                                    if (pwdDate) {
                                        const expireDate = new Date(pwdDate);
                                        expireDate.setDate(expireDate.getDate() + 90); // 90 days default
                                        passwordExpires = expireDate.toISOString();
                                    }
                                } catch  {
                                    passwordExpires = '';
                                }
                            }
                            const user = {
                                username: getAttributeValue('sAMAccountName'),
                                displayName: getAttributeValue('displayName'),
                                lockoutTime: convertFileTime(lockoutTime),
                                passwordExpires,
                                accountExpires: convertFileTime(accountExpires),
                                lastLogon: convertFileTime(lastLogon)
                            };
                            if (user.username) {
                                lockedUsers.push(user);
                            }
                        } catch (entryErr) {
                            console.error('Error processing search entry:', entryErr);
                        }
                    });
                    searchRes.on('error', (searchResErr)=>{
                        clearTimeout(timeout);
                        console.error('LDAP search result error:', searchResErr);
                        resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            error: `LDAP arama sonuç hatası: ${searchResErr.message}`
                        }, {
                            status: 500
                        }));
                    });
                    searchRes.on('end', ()=>{
                        clearTimeout(timeout);
                        // Calculate statistics
                        const now = new Date();
                        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        const stats = {
                            totalLocked: lockedUsers.filter((u)=>u.lockoutTime).length,
                            lockedToday: lockedUsers.filter((u)=>{
                                if (!u.lockoutTime) return false;
                                const lockDate = new Date(u.lockoutTime);
                                return lockDate >= today;
                            }).length,
                            passwordExpired: lockedUsers.filter((u)=>{
                                if (!u.passwordExpires) return false;
                                return new Date(u.passwordExpires) <= now;
                            }).length,
                            lastUpdated: now.toISOString()
                        };
                        resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            success: true,
                            users: lockedUsers,
                            count: lockedUsers.length,
                            stats
                        }));
                    });
                });
            });
        });
    } catch (error) {
        console.error('Get locked users error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Kilitlenen kullanıcılar alınırken hata oluştu'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__5ca313f1._.js.map