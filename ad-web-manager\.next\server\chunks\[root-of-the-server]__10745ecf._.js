module.exports = {

"[project]/.next-internal/server/app/api/unlock-user/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/ldapjs [external] (ldapjs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("ldapjs", () => require("ldapjs"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/app/api/unlock-user/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$ldapjs__$5b$external$5d$__$28$ldapjs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/ldapjs [external] (ldapjs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
const SETTINGS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'ldap-settings.json');
function loadSettings() {
    try {
        if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(SETTINGS_FILE)) {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(SETTINGS_FILE, 'utf8');
            return JSON.parse(data);
        }
        return null;
    } catch (error) {
        console.error('Error loading settings:', error);
        return null;
    }
}
async function POST(request) {
    try {
        const { username } = await request.json();
        if (!username) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Kullanıcı adı gereklidir'
            }, {
                status: 400
            });
        }
        const settings = loadSettings();
        if (!settings) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'LDAP ayarları bulunamadı. Lütfen önce ayarları yapılandırın.'
            }, {
                status: 400
            });
        }
        if (!settings.server || !settings.baseDN || !settings.username || !settings.password) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'LDAP ayarları eksik. Lütfen ayarları kontrol edin.'
            }, {
                status: 400
            });
        }
        const protocol = settings.useSSL ? 'ldaps' : 'ldap';
        const url = `${protocol}://${settings.server}:${settings.port}`;
        console.log(`Attempting LDAP connection to: ${url}`);
        console.log(`Username for unlock: ${username}`);
        const client = __TURBOPACK__imported__module__$5b$externals$5d2f$ldapjs__$5b$external$5d$__$28$ldapjs$2c$__cjs$29$__["default"].createClient({
            url: url,
            timeout: 60000,
            connectTimeout: 20000,
            tlsOptions: {
                rejectUnauthorized: false
            }
        });
        return new Promise((resolve)=>{
            let isResolved = false;
            const resolveOnce = (response)=>{
                if (!isResolved) {
                    isResolved = true;
                    client.destroy();
                    resolve(response);
                }
            };
            const timeout = setTimeout(()=>{
                console.error(`LDAP timeout for user: ${username}`);
                resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'LDAP işlemi zaman aşımına uğradı (60s)'
                }, {
                    status: 408
                }));
            }, 65000);
            client.on('error', (err)=>{
                clearTimeout(timeout);
                console.error('LDAP connection error:', err);
                resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: `LDAP bağlantı hatası: ${err.message}`
                }, {
                    status: 500
                }));
            });
            console.log(`Attempting LDAP bind with user: ${settings.username}`);
            client.bind(settings.username, settings.password, (err)=>{
                if (err) {
                    clearTimeout(timeout);
                    console.error('LDAP bind error:', err);
                    resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: `LDAP kimlik doğrulama hatası: ${err.message}`
                    }, {
                        status: 401
                    }));
                    return;
                }
                console.log('LDAP bind successful, searching for user...');
                // First, find the user's DN
                const searchFilter = `(&(objectClass=user)(sAMAccountName=${username}))`;
                const searchOptions = {
                    scope: 'sub',
                    filter: searchFilter,
                    attributes: [
                        'distinguishedName',
                        'lockoutTime',
                        'userAccountControl'
                    ]
                };
                client.search(settings.baseDN, searchOptions, (searchErr, searchRes)=>{
                    if (searchErr) {
                        clearTimeout(timeout);
                        console.error('LDAP search error:', searchErr);
                        resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            error: `Kullanıcı arama hatası: ${searchErr.message}`
                        }, {
                            status: 500
                        }));
                        return;
                    }
                    let userDN = '';
                    let foundUser = false;
                    searchRes.on('searchEntry', (entry)=>{
                        foundUser = true;
                        userDN = entry.pojo.objectName;
                        // Also try to get DN from attributes if objectName is not available
                        if (!userDN) {
                            const attributes = entry.pojo.attributes;
                            const dnAttr = attributes.find((a)=>a.type === 'distinguishedName');
                            if (dnAttr && dnAttr.values && dnAttr.values.length > 0) {
                                userDN = dnAttr.values[0];
                            }
                        }
                    });
                    searchRes.on('error', (searchResErr)=>{
                        clearTimeout(timeout);
                        console.error('LDAP search result error:', searchResErr);
                        resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            error: `Kullanıcı arama sonuç hatası: ${searchResErr.message}`
                        }, {
                            status: 500
                        }));
                    });
                    searchRes.on('end', ()=>{
                        if (!foundUser || !userDN) {
                            clearTimeout(timeout);
                            console.error(`User not found or DN not available. Found: ${foundUser}, DN: ${userDN}, Username: ${username}`);
                            resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                                error: `Kullanıcı bulunamadı veya DN alınamadı: ${username}`
                            }, {
                                status: 404
                            }));
                            return;
                        }
                        console.log(`Found user ${username} with DN: ${userDN}`);
                        // Try multiple unlock methods
                        const unlockMethods = [
                            // Method 1: Set lockoutTime to 0
                            {
                                operation: 'replace',
                                modification: {
                                    type: 'lockoutTime',
                                    values: [
                                        '0'
                                    ]
                                }
                            },
                            // Method 2: Remove lockoutTime attribute
                            {
                                operation: 'delete',
                                modification: {
                                    type: 'lockoutTime'
                                }
                            }
                        ];
                        let methodIndex = 0;
                        const tryUnlock = ()=>{
                            if (methodIndex >= unlockMethods.length) {
                                clearTimeout(timeout);
                                resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                                    error: `Tüm unlock yöntemleri başarısız oldu`
                                }, {
                                    status: 500
                                }));
                                return;
                            }
                            const change = new __TURBOPACK__imported__module__$5b$externals$5d2f$ldapjs__$5b$external$5d$__$28$ldapjs$2c$__cjs$29$__["default"].Change(unlockMethods[methodIndex]);
                            client.modify(userDN, change, (modifyErr)=>{
                                if (modifyErr) {
                                    console.error(`LDAP modify error (method ${methodIndex + 1}):`, modifyErr);
                                    methodIndex++;
                                    tryUnlock(); // Try next method
                                } else {
                                    clearTimeout(timeout);
                                    resolveOnce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                                        success: true,
                                        message: `${username} kullanıcısı başarıyla unlock edildi (method ${methodIndex + 1})`
                                    }));
                                }
                            });
                        };
                        tryUnlock();
                    });
                });
            });
        });
    } catch (error) {
        console.error('Unlock user error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Unlock işlemi sırasında hata oluştu'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__10745ecf._.js.map