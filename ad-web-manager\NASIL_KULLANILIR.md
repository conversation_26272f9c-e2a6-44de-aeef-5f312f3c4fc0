# AD Web Manager - Kullan<PERSON>m Kılavuzu

## 🚀 Hızlı Başlangıç

### Otomatik Başlatma (Önerilen)

1. **start-server.bat** dosyasına çift tıklayın
2. Script otomatik olarak:
   - Node.js kontrolü yapar
   - Dependencies'leri kontrol eder
   - LDAP ayarlarını kontrol eder
   - Server'ı başlatır

### Server'ı Durdurma

1. **stop-server.bat** dosyasına çift tıklayın
2. Script otomatik olarak:
   - Tüm Node.js process'lerini durdurur
   - Port 3000-3010 arasındaki process'leri temizler
   - Sistem kaynaklarını serbest bırakır

### HTTPS ile Başlatma (Gerçek SSL Sertifikalı)

1. **start-server.bat** ile HTTP server'ı başlatın
2. **start-https-tunnel.bat** dosyasına çift tıklayın
3. Tunnel URL'ini kopyalayın (örn: https://loud-suns-refuse.loca.lt)
4. Tarayıcıda bu URL'i açın - ✅ **Gerçek SSL sertifikalı!**

### Manuel Başlatma

```bash
# HTTP
npm run dev

# HTTPS
npm run dev:https
```

## 📋 Gereksinimler

- **Node.js** (v18 veya üzeri)
- **npm** (Node.js ile birlikte gelir)
- **Windows** işletim sistemi
- **LDAP/Active Directory** erişimi

## 🔧 İlk Kurulum

1. **Node.js Yükleme**:
   - https://nodejs.org/ adresinden indirin
   - LTS versiyonunu seçin

2. **Dependencies Yükleme**:
   ```bash
   npm install
   ```

3. **LDAP Ayarları**:
   - `ldap-settings.json` dosyası otomatik oluşturulur
   - Gerekirse Settings sayfasından düzenleyin

## 🌐 Erişim

### HTTP (Varsayılan)
- **Ana Sayfa**: http://localhost:3000
- **Locked Users**: http://localhost:3000/users
- **Password Expiry**: http://localhost:3000/password-expiry
- **Settings**: http://localhost:3000/settings

### HTTPS (Gerçek SSL Sertifikalı)
- **Tunnel URL**: https://loud-suns-refuse.loca.lt (örnek)
- **Ana Sayfa**: https://[tunnel-url]/
- **Locked Users**: https://[tunnel-url]/users
- **Password Expiry**: https://[tunnel-url]/password-expiry
- **Settings**: https://[tunnel-url]/settings

## 🔐 Giriş Bilgileri

- **Kullanıcı Adı**: admin
- **Şifre**: 581326Ob

## 📁 Dosya Yapısı

```
ad-web-manager/
├── start-server.bat          # HTTP başlatma script'i
├── start-https-server.bat    # HTTPS başlatma script'i
├── stop-server.bat           # Durdurma script'i
├── create-ssl-cert.bat       # SSL sertifika oluşturucu
├── test-https.bat           # HTTPS test script'i
├── start-server.ps1          # PowerShell başlatma script'i
├── stop-server.ps1           # PowerShell durdurma script'i
├── create-shortcut.bat       # Desktop shortcut oluşturucu
├── nginx.conf               # Nginx reverse proxy config
├── ssl/                     # SSL sertifikaları
├── ldap-settings.json        # LDAP ayarları
├── .env.local               # Environment değişkenleri
├── package.json             # Proje bağımlılıkları
└── src/                     # Kaynak kodlar
```

## 🛠️ Özellikler

### 1. Locked Users Yönetimi
- Kilitli kullanıcıları görüntüleme
- Tek tek unlock işlemi
- Toplu unlock işlemi
- Dashboard metrikleri

### 2. Password Expiry Yönetimi
- Şifre süresi dolan kullanıcıları görüntüleme
- Şifre süresini uzatma (3 adımlı)
- Otomatik unlock işlemi
- Kullanıcı arama

### 3. Settings
- LDAP bağlantı ayarları
- Bağlantı testi
- LDAP durumu görüntüleme

## 🔧 Sorun Giderme

### LDAP Bağlantı Hatası
1. Settings sayfasına gidin
2. LDAP ayarlarını kontrol edin
3. "Test Connection" butonuna tıklayın
4. Hata mesajını kontrol edin

### Port Kullanımda Hatası
- Server otomatik olarak uygun port bulur
- Genellikle 3000, 3001, 3002 portlarını dener

### Dependencies Hatası
```bash
npm install --force
```

## 📞 Destek

Sorun yaşadığınızda:
1. Console loglarını kontrol edin
2. LDAP ayarlarını doğrulayın
3. Network bağlantısını kontrol edin

## 🔄 Güncelleme

Yeni versiyon için:
1. Yeni dosyaları kopyalayın
2. `npm install` çalıştırın
3. Server'ı yeniden başlatın

## 🎯 Kısayollar

- **Desktop Shortcut**: `create-shortcut.bat` çalıştırın
- **Hızlı Başlatma**: `start-server.bat`
- **Hızlı Durdurma**: `stop-server.bat`
- **PowerShell Başlatma**: `start-server.ps1`
- **PowerShell Durdurma**: `stop-server.ps1`
