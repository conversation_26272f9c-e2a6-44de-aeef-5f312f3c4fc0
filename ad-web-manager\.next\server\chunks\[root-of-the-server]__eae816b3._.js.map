{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport fs from 'fs';\nimport path from 'path';\n\nconst USERS_FILE = path.join(process.cwd(), 'users.json');\n\nexport interface User {\n  id: string;\n  username: string;\n  password: string;\n  role: 'admin' | 'user';\n  permissions: {\n    viewUsers: boolean;\n    unlockUsers: boolean;\n    manageSettings: boolean;\n    manageUsers: boolean;\n  };\n  createdAt: string;\n  lastLogin?: string;\n}\n\n// Default admin user\nconst defaultAdmin: User = {\n  id: '1',\n  username: 'admin',\n  password: bcrypt.hashSync('581326Ob', 10),\n  role: 'admin',\n  permissions: {\n    viewUsers: true,\n    unlockUsers: true,\n    manageSettings: true,\n    manageUsers: true,\n  },\n  createdAt: new Date().toISOString(),\n};\n\nexport function initializeUsers() {\n  if (!fs.existsSync(USERS_FILE)) {\n    fs.writeFileSync(USERS_FILE, JSON.stringify([defaultAdmin], null, 2));\n  }\n}\n\nexport function getUsers(): User[] {\n  try {\n    if (!fs.existsSync(USERS_FILE)) {\n      initializeUsers();\n    }\n    const data = fs.readFileSync(USERS_FILE, 'utf8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error('Error reading users file:', error);\n    return [defaultAdmin];\n  }\n}\n\nexport function saveUsers(users: User[]) {\n  try {\n    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));\n  } catch (error) {\n    console.error('Error saving users file:', error);\n    throw new Error('Failed to save users');\n  }\n}\n\nexport function getUserByUsername(username: string): User | null {\n  const users = getUsers();\n  return users.find(user => user.username === username) || null;\n}\n\nexport function getUserById(id: string): User | null {\n  const users = getUsers();\n  return users.find(user => user.id === id) || null;\n}\n\nexport function validatePassword(password: string, hashedPassword: string): boolean {\n  return bcrypt.compareSync(password, hashedPassword);\n}\n\n\n\nexport function hashPassword(password: string): string {\n  return bcrypt.hashSync(password, 10);\n}\n\nexport function createUser(userData: Omit<User, 'id' | 'createdAt' | 'password'> & { password: string }): User {\n  const users = getUsers();\n  \n  // Check if username already exists\n  if (users.find(user => user.username === userData.username)) {\n    throw new Error('Username already exists');\n  }\n\n  const newUser: User = {\n    ...userData,\n    id: Date.now().toString(),\n    password: hashPassword(userData.password),\n    createdAt: new Date().toISOString(),\n  };\n\n  users.push(newUser);\n  saveUsers(users);\n  \n  return newUser;\n}\n\nexport function updateUser(id: string, updates: Partial<Omit<User, 'id' | 'createdAt'>>): User {\n  const users = getUsers();\n  const userIndex = users.findIndex(user => user.id === id);\n  \n  if (userIndex === -1) {\n    throw new Error('User not found');\n  }\n\n  // If password is being updated, hash it\n  if (updates.password) {\n    updates.password = hashPassword(updates.password);\n  }\n\n  users[userIndex] = { ...users[userIndex], ...updates };\n  saveUsers(users);\n  \n  return users[userIndex];\n}\n\nexport function deleteUser(id: string): boolean {\n  const users = getUsers();\n  const userIndex = users.findIndex(user => user.id === id);\n  \n  if (userIndex === -1) {\n    return false;\n  }\n\n  // Don't allow deleting the default admin\n  if (users[userIndex].username === 'admin') {\n    throw new Error('Cannot delete default admin user');\n  }\n\n  users.splice(userIndex, 1);\n  saveUsers(users);\n  \n  return true;\n}\n\nexport function updateLastLogin(userId: string) {\n  const users = getUsers();\n  const userIndex = users.findIndex(user => user.id === userId);\n  \n  if (userIndex !== -1) {\n    users[userIndex].lastLogin = new Date().toISOString();\n    saveUsers(users);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAiB5C,qBAAqB;AACrB,MAAM,eAAqB;IACzB,IAAI;IACJ,UAAU;IACV,UAAU,mIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,YAAY;IACtC,MAAM;IACN,aAAa;QACX,WAAW;QACX,aAAa;QACb,gBAAgB;QAChB,aAAa;IACf;IACA,WAAW,IAAI,OAAO,WAAW;AACnC;AAEO,SAAS;IACd,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;QAC9B,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC;YAAC;SAAa,EAAE,MAAM;IACpE;AACF;AAEO,SAAS;IACd,IAAI;QACF,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;YAC9B;QACF;QACA,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,YAAY;QACzC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAC;SAAa;IACvB;AACF;AAEO,SAAS,UAAU,KAAa;IACrC,IAAI;QACF,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC,OAAO,MAAM;IAC3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,SAAS,kBAAkB,QAAgB;IAChD,MAAM,QAAQ;IACd,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,aAAa;AAC3D;AAEO,SAAS,YAAY,EAAU;IACpC,MAAM,QAAQ;IACd,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO;AAC/C;AAEO,SAAS,iBAAiB,QAAgB,EAAE,cAAsB;IACvE,OAAO,mIAAA,CAAA,UAAM,CAAC,WAAW,CAAC,UAAU;AACtC;AAIO,SAAS,aAAa,QAAgB;IAC3C,OAAO,mIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,UAAU;AACnC;AAEO,SAAS,WAAW,QAA4E;IACrG,MAAM,QAAQ;IAEd,mCAAmC;IACnC,IAAI,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,SAAS,QAAQ,GAAG;QAC3D,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAgB;QACpB,GAAG,QAAQ;QACX,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,UAAU,aAAa,SAAS,QAAQ;QACxC,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,MAAM,IAAI,CAAC;IACX,UAAU;IAEV,OAAO;AACT;AAEO,SAAS,WAAW,EAAU,EAAE,OAAgD;IACrF,MAAM,QAAQ;IACd,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEtD,IAAI,cAAc,CAAC,GAAG;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,wCAAwC;IACxC,IAAI,QAAQ,QAAQ,EAAE;QACpB,QAAQ,QAAQ,GAAG,aAAa,QAAQ,QAAQ;IAClD;IAEA,KAAK,CAAC,UAAU,GAAG;QAAE,GAAG,KAAK,CAAC,UAAU;QAAE,GAAG,OAAO;IAAC;IACrD,UAAU;IAEV,OAAO,KAAK,CAAC,UAAU;AACzB;AAEO,SAAS,WAAW,EAAU;IACnC,MAAM,QAAQ;IACd,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEtD,IAAI,cAAc,CAAC,GAAG;QACpB,OAAO;IACT;IAEA,yCAAyC;IACzC,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,KAAK,SAAS;QACzC,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,MAAM,CAAC,WAAW;IACxB,UAAU;IAEV,OAAO;AACT;AAEO,SAAS,gBAAgB,MAAc;IAC5C,MAAM,QAAQ;IACd,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEtD,IAAI,cAAc,CAAC,GAAG;QACpB,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,OAAO,WAAW;QACnD,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials';\nimport { getUserByUsername, validatePassword, updateLastLogin, initializeUsers } from '@/lib/auth';\n\n// Initialize users file on startup\ninitializeUsers();\n\nexport const authOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        username: { label: 'Username', type: 'text' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        const user = getUserByUsername(credentials.username);\n\n        if (!user) {\n          return null;\n        }\n\n        const isValidPassword = validatePassword(credentials.password, user.password);\n\n        if (!isValidPassword) {\n          return null;\n        }\n\n        // Update last login\n        updateLastLogin(user.id);\n\n        return {\n          id: user.id,\n          name: user.username,\n          email: user.username,\n          role: user.role,\n          permissions: user.permissions,\n        };\n      },\n    })\n  ],\n  callbacks: {\n    async jwt({ token, user, trigger, session }) {\n      if (user) {\n        token.role = user.role;\n        token.permissions = user.permissions;\n      }\n\n      // Handle session updates\n      if (trigger === 'update' && session) {\n        token.role = session.role;\n        token.permissions = session.permissions;\n      }\n\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!;\n        session.user.role = token.role as string;\n        session.user.permissions = token.permissions as any;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/login',\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',\n};\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,mCAAmC;AACnC,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD;AAEP,MAAM,cAAc;IACzB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAO;gBAC5C,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,QAAQ;gBAEnD,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,QAAQ,EAAE,KAAK,QAAQ;gBAE5E,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,oBAAoB;gBACpB,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;gBAEvB,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,QAAQ;oBACnB,OAAO,KAAK,QAAQ;oBACpB,MAAM,KAAK,IAAI;oBACf,aAAa,KAAK,WAAW;gBAC/B;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACzC,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,WAAW,GAAG,KAAK,WAAW;YACtC;YAEA,yBAAyB;YACzB,IAAI,YAAY,YAAY,SAAS;gBACnC,MAAM,IAAI,GAAG,QAAQ,IAAI;gBACzB,MAAM,WAAW,GAAG,QAAQ,WAAW;YACzC;YAEA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;YAC9C;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe,IAAI;AACzC;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}