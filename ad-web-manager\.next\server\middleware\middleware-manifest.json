{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ioihxw0T6uLY6tVdvT/KSIElCQ4mXNoUEabkMtzHbR4=", "__NEXT_PREVIEW_MODE_ID": "08f223d6fe1d34c2a2d05f7420f395fe", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "52589fc79414180c68875b17030060bed362fddf96d7fdf9fa32428fd79d1e5f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "374c9feedfc48b60c352ecdfecb10f7d3600175f9567582c96baf7a285426018"}}}, "instrumentation": null, "functions": {}}