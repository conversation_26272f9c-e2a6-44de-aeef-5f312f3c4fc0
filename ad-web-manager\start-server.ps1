# AD Web Manager Server Başlatma Script'i
# PowerShell versiyonu

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   AD Web Manager Server Başlatılıyor" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Gerekli dizine git
Set-Location $PSScriptRoot

# Node.js ve npm kontrolü
Write-Host "[1/4] Node.js kontrolü yapılıyor..." -ForegroundColor Yellow

try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
    Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ HATA: Node.js bulunamadı! Lütfen Node.js yükleyin." -ForegroundColor Red
    Write-Host "https://nodejs.org/" -ForegroundColor Cyan
    Read-Host "Devam etmek için Enter'a basın"
    exit 1
}

Write-Host ""

# Dependencies kontrolü
Write-Host "[2/4] Dependencies kontrolü yapılıyor..." -ForegroundColor Yellow

if (-not (Test-Path "node_modules")) {
    Write-Host "Dependencies bulunamadı. Yükleniyor..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ HATA: Dependencies yüklenemedi!" -ForegroundColor Red
        Read-Host "Devam etmek için Enter'a basın"
        exit 1
    }
    Write-Host "✅ Dependencies başarıyla yüklendi." -ForegroundColor Green
} else {
    Write-Host "✅ Dependencies mevcut." -ForegroundColor Green
}

Write-Host ""

# LDAP ayarları kontrolü
Write-Host "[3/4] LDAP ayarları kontrolü yapılıyor..." -ForegroundColor Yellow

if (-not (Test-Path "ldap-settings.json")) {
    Write-Host "⚠️  UYARI: LDAP ayarları bulunamadı!" -ForegroundColor Yellow
    Write-Host "Varsayılan ayarlar oluşturuluyor..." -ForegroundColor Yellow
    
    $ldapSettings = @{
        server = "**********"
        port = "389"
        baseDN = "DC=egefrn,DC=bayraktar,DC=com"
        username = "<EMAIL>"
        password = "Ebt1991.,"
        useSSL = $false
    }
    
    $ldapSettings | ConvertTo-Json -Depth 10 | Out-File -FilePath "ldap-settings.json" -Encoding UTF8
    Write-Host "✅ LDAP ayarları oluşturuldu." -ForegroundColor Green
} else {
    Write-Host "✅ LDAP ayarları mevcut." -ForegroundColor Green
}

# Environment dosyası kontrolü
if (-not (Test-Path ".env.local")) {
    Write-Host "Environment dosyası oluşturuluyor..." -ForegroundColor Yellow
    @"
NEXTAUTH_SECRET=your-secret-key-here-change-this-in-production
NEXTAUTH_URL=http://localhost:3000
"@ | Out-File -FilePath ".env.local" -Encoding UTF8
    Write-Host "✅ Environment dosyası oluşturuldu." -ForegroundColor Green
}

Write-Host ""

# Server başlatma
Write-Host "[4/4] Server başlatılıyor..." -ForegroundColor Yellow
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  Server http://localhost:3000 adresinde" -ForegroundColor Green
Write-Host "  Kapatmak için Ctrl+C basın" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Development server'ı başlat
npm run dev

# Server kapandığında
Write-Host ""
Write-Host "Server kapatıldı." -ForegroundColor Yellow
Read-Host "Devam etmek için Enter'a basın"
