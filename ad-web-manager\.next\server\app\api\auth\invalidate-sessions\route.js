(()=>{var e={};e.id=870,e.ids=[870],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5792:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GET:()=>u,POST:()=>u,authOptions:()=>a});var t=s(19854),n=s.n(t),i=s(13581),o=s(12909);(0,o.lK)();let a={providers:[(0,i.A)({name:"credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;let r=(0,o.JE)(e.username);return r&&(0,o.Oj)(e.password,r.password)?((0,o.bx)(r.id),{id:r.id,name:r.username,email:r.username,role:r.role,permissions:r.permissions}):null}})],callbacks:{jwt:async({token:e,user:r,trigger:s,session:t})=>(r&&(e.role=r.role,e.permissions=r.permissions),"update"===s&&t&&(e.role=t.role,e.permissions=t.permissions),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.permissions=r.permissions),e)},pages:{signIn:"/login"},callbacks:{jwt:async({token:e,user:r,trigger:s,session:t})=>(r&&(e.role=r.role,e.permissions=r.permissions),"update"===s&&t&&(e.role=t.role,e.permissions=t.permissions),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.permissions=r.permissions),e),redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:e.startsWith(r)?e:`${r}/login`},session:{strategy:"jwt"},secret:process.env.NEXTAUTH_SECRET||"your-secret-key-here"},u=n()(a)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{JE:()=>d,Oj:()=>f,TK:()=>w,bx:()=>h,hG:()=>g,kg:()=>y,kl:()=>m,lK:()=>p,lo:()=>l});var t=s(85663),n=s(29021),i=s.n(n),o=s(33873);let a=s.n(o)().join(process.cwd(),"users.json"),u={id:"1",username:"admin",password:"$2b$10$t5.K7vyPesCG5IR5F5TkUuSSM/QNlar8Wr243/uQuG0yyrw1mJbBK",role:"admin",permissions:{viewUsers:!0,unlockUsers:!0,manageSettings:!0,manageUsers:!0},createdAt:new Date().toISOString()};function p(){i().existsSync(a)||i().writeFileSync(a,JSON.stringify([u],null,2))}function l(){try{i().existsSync(a)||p();let e=i().readFileSync(a,"utf8");return JSON.parse(e)}catch(e){return console.error("Error reading users file:",e),[u]}}function c(e){try{i().writeFileSync(a,JSON.stringify(e,null,2))}catch(e){throw console.error("Error saving users file:",e),Error("Failed to save users")}}function d(e){return l().find(r=>r.username===e)||null}function m(e){return l().find(r=>r.id===e)||null}function f(e,r){return t.Ay.compareSync(e,r)}function x(e){return t.Ay.hashSync(e,10)}function y(e){let r=l();if(r.find(r=>r.username===e.username))throw Error("Username already exists");let s={...e,id:Date.now().toString(),password:x(e.password),createdAt:new Date().toISOString()};return r.push(s),c(r),s}function w(e,r){let s=l(),t=s.findIndex(r=>r.id===e);if(-1===t)throw Error("User not found");return r.password&&(r.password=x(r.password)),s[t]={...s[t],...r},c(s),s[t]}function g(e){let r=l(),s=r.findIndex(r=>r.id===e);if(-1===s)return!1;if("admin"===r[s].username)throw Error("Cannot delete default admin user");return r.splice(s,1),c(r),!0}function h(e){let r=l(),s=r.findIndex(r=>r.id===e);-1!==s&&(r[s].lastLogin=new Date().toISOString(),c(r))}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53772:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>w,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{GET:()=>d,POST:()=>c});var n=s(96559),i=s(48088),o=s(37719),a=s(32190),u=s(19854),p=s(5792);let l=new Set;async function c(){try{let e=await (0,u.getServerSession)(p.authOptions);if(!e?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});if(!e.user.permissions?.manageUsers)return a.NextResponse.json({error:"Insufficient permissions"},{status:403});let r=Date.now().toString();return l.add(r),a.NextResponse.json({success:!0,invalidationTime:r})}catch(e){return console.error("Error invalidating sessions:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(){let e=Array.from(l),r=e.length>0?Math.max(...e.map(Number)):0;return a.NextResponse.json({latestInvalidation:r})}let m=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/invalidate-sessions/route",pathname:"/api/auth/invalidate-sessions",filename:"route",bundlePath:"app/api/auth/invalidate-sessions/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\auth\\invalidate-sessions\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:f,workUnitAsyncStorage:x,serverHooks:y}=m;function w(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:x})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580,854,427],()=>s(53772));module.exports=t})();