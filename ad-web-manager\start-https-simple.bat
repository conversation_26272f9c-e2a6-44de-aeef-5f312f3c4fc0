@echo off
title AD Web Manager HTTPS Server (Simple)
color 0A

echo.
echo ========================================
echo   AD Web Manager HTTPS Server Baslatiliyor
echo ========================================
echo.

REM Gerekli dizine git
cd /d "%~dp0"

echo [1/3] Dependencies kontrolu yapiliyor...
if not exist "node_modules" (
    echo Dependencies bulunamadi. Yukleniyor...
    call npm install
) else (
    echo Dependencies mevcut.
)
echo.

echo [2/3] LDAP ayarlari kontrolu yapiliyor...
if not exist "ldap-settings.json" (
    echo LDAP ayarlari olusturuluyor...
    echo {"server":"**********","port":"389","baseDN":"DC=egefrn,DC=bayraktar,DC=com","username":"<EMAIL>","password":"Ebt1991.,","useSSL":false} > ldap-settings.json
    echo LDAP ayarlari olusturuldu.
) else (
    echo LDAP ayarlari mevcut.
)
echo.

REM Environment dosyasi kontrolu
if not exist ".env.local" (
    echo Environment dosyasi olusturuluyor...
    echo NEXTAUTH_SECRET=your-secret-key-here-change-this-in-production> .env.local
    echo NEXTAUTH_URL=https://localhost:3000>> .env.local
) else (
    REM NEXTAUTH_URL'i HTTPS olarak guncelle
    powershell -Command "(Get-Content .env.local) -replace 'NEXTAUTH_URL=http://localhost:3000', 'NEXTAUTH_URL=https://localhost:3000' | Set-Content .env.local"
)

echo [3/3] HTTPS Server baslatiliyor...
echo.
echo ========================================
echo   Server https://localhost:3000 adresinde
echo   Kapatmak icin Ctrl+C basin
echo ========================================
echo.
echo UYARI: Self-signed certificate kullaniliyor.
echo Tarayicida "Guvenli degil" uyarisi cikacak.
echo "Gelismis" -> "localhost'a git (guvenli degil)" tiklayin.
echo.

REM HTTPS environment variables
set HTTPS=true

call npm run dev

echo.
echo Server kapatildi.
pause
