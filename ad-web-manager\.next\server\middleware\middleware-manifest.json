{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "VxsZ3hRKeHcjFiOsyWz2ZuNDEfY8h4a6QtwEExCtPGY=", "__NEXT_PREVIEW_MODE_ID": "2eea2de5d50ebeb1ced4b824c08f9805", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9acdd2179f3ff3356a814ced3aaef00c62daaa5c1054d2276c238f274f027a66", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "03621ab2ac7001443917b587b2196f45dc0d061ee093a0818c65d9f5cc2ab805"}}}, "instrumentation": null, "functions": {}}