(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[541],{2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>r});var t=a(5155);a(2115);var n=a(9434);function r(e){let{className:s,type:a,...r}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...r})}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>l});var t=a(5155);a(2115);var n=a(968),r=a(9434);function l(e){let{className:s,...a}=e;return(0,t.jsx)(n.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},5127:(e,s,a)=>{"use strict";a.d(s,{A0:()=>l,BF:()=>i,Hj:()=>d,XI:()=>r,nA:()=>o,nd:()=>c});var t=a(5155);a(2115);var n=a(9434);function r(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",s),...a})})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}},5365:(e,s,a)=>{"use strict";a.d(s,{Fc:()=>i,TN:()=>d});var t=a(5155);a(2115);var n=a(2085),r=a(9434);let l=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:a,...n}=e;return(0,t.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,r.cn)(l({variant:a}),s),...n})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"alert-description",className:(0,r.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...a})}},7262:(e,s,a)=>{"use strict";a.d(s,{S:()=>i});var t=a(5155);a(2115);var n=a(6981),r=a(5196),l=a(9434);function i(e){let{className:s,...a}=e;return(0,t.jsx)(n.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...a,children:(0,t.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(r.A,{className:"size-3.5"})})})}},9036:(e,s,a)=>{Promise.resolve().then(a.bind(a,9567))},9567:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var t=a(5155),n=a(2115),r=a(6874),l=a.n(r),i=a(2108),d=a(6695),c=a(285),o=a(2523),u=a(5057),m=a(7262),x=a(5365),p=a(5127),h=a(5452),g=a(4416),f=a(9434);function j(e){let{...s}=e;return(0,t.jsx)(h.bL,{"data-slot":"dialog",...s})}function v(e){let{...s}=e;return(0,t.jsx)(h.l9,{"data-slot":"dialog-trigger",...s})}function b(e){let{...s}=e;return(0,t.jsx)(h.ZL,{"data-slot":"dialog-portal",...s})}function y(e){let{className:s,...a}=e;return(0,t.jsx)(h.hJ,{"data-slot":"dialog-overlay",className:(0,f.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...a})}function N(e){let{className:s,children:a,showCloseButton:n=!0,...r}=e;return(0,t.jsxs)(b,{"data-slot":"dialog-portal",children:[(0,t.jsx)(y,{}),(0,t.jsxs)(h.UC,{"data-slot":"dialog-content",className:(0,f.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...r,children:[a,n&&(0,t.jsxs)(h.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(g.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function w(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,f.cn)("flex flex-col gap-2 text-center sm:text-left",s),...a})}function k(e){let{className:s,...a}=e;return(0,t.jsx)(h.hE,{"data-slot":"dialog-title",className:(0,f.cn)("text-lg leading-none font-semibold",s),...a})}function U(e){let{className:s,...a}=e;return(0,t.jsx)(h.VY,{"data-slot":"dialog-description",className:(0,f.cn)("text-muted-foreground text-sm",s),...a})}var C=a(6830),S=a(6474),z=a(5196),A=a(7863);function D(e){let{...s}=e;return(0,t.jsx)(C.bL,{"data-slot":"select",...s})}function K(e){let{...s}=e;return(0,t.jsx)(C.WT,{"data-slot":"select-value",...s})}function _(e){let{className:s,size:a="default",children:n,...r}=e;return(0,t.jsxs)(C.l9,{"data-slot":"select-trigger","data-size":a,className:(0,f.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...r,children:[n,(0,t.jsx)(C.In,{asChild:!0,children:(0,t.jsx)(S.A,{className:"size-4 opacity-50"})})]})}function Y(e){let{className:s,children:a,position:n="popper",...r}=e;return(0,t.jsx)(C.ZL,{children:(0,t.jsxs)(C.UC,{"data-slot":"select-content",className:(0,f.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...r,children:[(0,t.jsx)(F,{}),(0,t.jsx)(C.LM,{className:(0,f.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(O,{})]})})}function B(e){let{className:s,children:a,...n}=e;return(0,t.jsxs)(C.q7,{"data-slot":"select-item",className:(0,f.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...n,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(C.VF,{children:(0,t.jsx)(z.A,{className:"size-4"})})}),(0,t.jsx)(C.p4,{children:a})]})}function F(e){let{className:s,...a}=e;return(0,t.jsx)(C.PP,{"data-slot":"select-scroll-up-button",className:(0,f.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,t.jsx)(A.A,{className:"size-4"})})}function O(e){let{className:s,...a}=e;return(0,t.jsx)(C.wn,{"data-slot":"select-scroll-down-button",className:(0,f.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,t.jsx)(S.A,{className:"size-4"})})}var J=a(420),L=a(3978),T=a(8931),E=a(2318),M=a(1154),P=a(3717),Z=a(2525);function $(){let{data:e,update:s}=(0,i.useSession)(),a=(0,J.X)(),[r,h]=(0,n.useState)([]),[g,f]=(0,n.useState)(!1),[b,y]=(0,n.useState)(""),[C,S]=(0,n.useState)(""),[z,A]=(0,n.useState)(!1),[F,O]=(0,n.useState)(null),[$,G]=(0,n.useState)({username:"",password:"",role:"user",permissions:{viewUsers:!0,unlockUsers:!1,manageSettings:!1,manageUsers:!1}});if((0,n.useEffect)(()=>{(null==e?void 0:e.user.permissions.manageUsers)&&H()},[e]),!e)return null;if(!e.user.permissions.manageUsers)return(0,t.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center",children:(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"Erişim Reddedildi"}),(0,t.jsx)(d.BT,{children:"Bu sayfaya erişim yetkiniz bulunmamaktadır."})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)(c.$,{asChild:!0,children:(0,t.jsx)(l(),{href:"/",children:"Ana Sayfaya D\xf6n"})})})]})});let H=async()=>{f(!0);try{let e=await fetch("/api/users");if(e.ok){let s=await e.json();h(s.users)}else y("Kullanıcılar y\xfcklenirken hata oluştu!"),S("error")}catch(e){y("Bağlantı hatası!"),S("error")}finally{f(!1)}},R=async e=>{e.preventDefault(),f(!0),y("");try{let e=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify($)}),s=await e.json();e.ok?(y("Kullanıcı başarıyla oluşturuldu!"),S("success"),A(!1),G({username:"",password:"",role:"user",permissions:{viewUsers:!0,unlockUsers:!1,manageSettings:!1,manageUsers:!1}}),H()):(y(s.error||"Kullanıcı oluşturulurken hata oluştu!"),S("error"))}catch(e){y("Bağlantı hatası!"),S("error")}finally{f(!1)}},V=async e=>{if(confirm("Bu kullanıcıyı silmek istediğinizden emin misiniz?")){f(!0);try{let s=await fetch("/api/users/".concat(e),{method:"DELETE"}),a=await s.json();s.ok?(y("Kullanıcı başarıyla silindi!"),S("success"),H()):(y(a.error||"Kullanıcı silinirken hata oluştu!"),S("error"))}catch(e){y("Bağlantı hatası!"),S("error")}finally{f(!1)}}},q=e=>{try{return new Date(e).toLocaleString("tr-TR")}catch(s){return e}},I=e=>{O(e),G({username:e.username,password:"",role:e.role,permissions:{...e.permissions}}),A(!0)},W=async s=>{if(s.preventDefault(),F){f(!0),y("");try{let s=await fetch("/api/users/".concat(F.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({role:$.role,permissions:$.permissions,...$.password&&{password:$.password}})}),n=await s.json();if(s.ok){var a,t;y("Kullanıcı başarıyla g\xfcncellendi!"),S("success"),A(!1),O(null),G({username:"",password:"",role:"user",permissions:{viewUsers:!1,unlockUsers:!1,manageSettings:!1,manageUsers:!1}}),H();let s=(null==e||null==(a=e.user)?void 0:a.username)||(null==e||null==(t=e.user)?void 0:t.name),n=F.username===s;console.log("Debug - User update check:",{editingUsername:F.username,currentUsername:s,sessionUser:null==e?void 0:e.user,isCurrentUser:n}),n?(console.log("Current user permissions updated, forcing logout..."),y("Yetkiniz g\xfcncellendi. Yeniden giriş yapmanız gerekiyor..."),S("success"),setTimeout(()=>{console.log("Signing out..."),signOut({callbackUrl:"/login"})},2e3)):console.log("Different user updated, no logout needed")}else y(n.error||"Kullanıcı g\xfcncellenirken hata oluştu!"),S("error")}catch(e){y("Bağlantı hatası!"),S("error")}finally{f(!1)}}};return(0,t.jsxs)("div",{className:"min-h-screen bg-background",children:[a.isMobile?(0,t.jsx)(L.c,{currentPath:"/manage-users"}):(0,t.jsx)(T.u,{currentPath:"/manage-users"}),(0,t.jsx)("main",{className:"\n        ".concat(a.isMobile?"pb-safe px-4 py-4":"container mx-auto px-4 py-6","\n      "),children:(0,t.jsxs)("div",{className:"".concat(a.isMobile?"space-y-4":"space-y-6"),children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-foreground",children:"Kullanıcı Y\xf6netimi"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Sistem kullanıcılarını y\xf6netin"})]}),(0,t.jsxs)(j,{open:z,onOpenChange:A,children:[(0,t.jsx)(v,{asChild:!0,children:(0,t.jsxs)(c.$,{onClick:()=>{O(null),G({username:"",password:"",role:"user",permissions:{viewUsers:!0,unlockUsers:!1,manageSettings:!1,manageUsers:!1}})},children:[(0,t.jsx)(E.A,{className:"mr-2 h-4 w-4"}),"Yeni Kullanıcı"]})}),(0,t.jsxs)(N,{className:"max-w-md",children:[(0,t.jsxs)(w,{children:[(0,t.jsx)(k,{children:F?"Kullanıcı D\xfczenle":"Yeni Kullanıcı Oluştur"}),(0,t.jsx)(U,{children:F?"Kullanıcı bilgilerini ve yetkilerini g\xfcncelleyin.":"Yeni bir kullanıcı hesabı oluşturun ve yetkilerini belirleyin."})]}),(0,t.jsxs)("form",{onSubmit:F?W:R,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"username",children:"Kullanıcı Adı"}),(0,t.jsx)(o.p,{id:"username",value:$.username,onChange:e=>G({...$,username:e.target.value}),placeholder:"Kullanıcı adı",disabled:!!F,required:!F})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"password",children:F?"Yeni Şifre (Opsiyonel)":"Şifre"}),(0,t.jsx)(o.p,{id:"password",type:"password",value:$.password,onChange:e=>G({...$,password:e.target.value}),placeholder:F?"Boş bırakılırsa değişmez":"Şifre",required:!F})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"role",children:"Rol"}),(0,t.jsxs)(D,{value:$.role,onValueChange:e=>G({...$,role:e}),children:[(0,t.jsx)(_,{children:(0,t.jsx)(K,{})}),(0,t.jsxs)(Y,{children:[(0,t.jsx)(B,{value:"user",children:"User"}),(0,t.jsx)(B,{value:"admin",children:"Admin"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(u.J,{children:"Yetkiler"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.S,{id:"viewUsers",checked:$.permissions.viewUsers,onCheckedChange:e=>G({...$,permissions:{...$.permissions,viewUsers:e}})}),(0,t.jsx)(u.J,{htmlFor:"viewUsers",children:"Kullanıcıları G\xf6r\xfcnt\xfcle"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.S,{id:"unlockUsers",checked:$.permissions.unlockUsers,onCheckedChange:e=>G({...$,permissions:{...$.permissions,unlockUsers:e}})}),(0,t.jsx)(u.J,{htmlFor:"unlockUsers",children:"Kullanıcı Unlock"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.S,{id:"manageSettings",checked:$.permissions.manageSettings,onCheckedChange:e=>G({...$,permissions:{...$.permissions,manageSettings:e}})}),(0,t.jsx)(u.J,{htmlFor:"manageSettings",children:"Ayarları Y\xf6net"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.S,{id:"manageUsers",checked:$.permissions.manageUsers,onCheckedChange:e=>G({...$,permissions:{...$.permissions,manageUsers:e}})}),(0,t.jsx)(u.J,{htmlFor:"manageUsers",children:"Kullanıcıları Y\xf6net"})]})]})]}),(0,t.jsxs)(c.$,{type:"submit",className:"w-full",disabled:g,children:[g&&(0,t.jsx)(M.A,{className:"mr-2 h-4 w-4 animate-spin"}),g?F?"G\xfcncelleniyor...":"Oluşturuluyor...":F?"Kullanıcı G\xfcncelle":"Kullanıcı Oluştur"]})]})]})]})]}),b&&(0,t.jsx)(x.Fc,{className:"error"===C?"border-destructive":"border-green-500",children:(0,t.jsx)(x.TN,{className:"error"===C?"text-destructive":"text-green-700",children:b})}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"Kullanıcılar"}),(0,t.jsx)(d.BT,{children:"Sistem kullanıcılarının listesi ve y\xf6netimi"})]}),(0,t.jsx)(d.Wu,{children:g?(0,t.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,t.jsx)(M.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Y\xfckleniyor..."})]}):a.isMobile?(0,t.jsx)("div",{className:"space-y-3",children:r.map(e=>(0,t.jsxs)(d.Zp,{className:"p-4",children:[(0,t.jsx)("div",{className:"flex justify-between items-start mb-3",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg text-foreground",children:e.username}),(0,t.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("admin"===e.role?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:"admin"===e.role?"Admin":"Kullanıcı"})})]})}),(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Yetkiler:"}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-1 mt-1",children:[e.permissions.viewUsers&&(0,t.jsx)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded text-xs",children:"\uD83D\uDC41️ G\xf6r\xfcnt\xfcle"}),e.permissions.unlockUsers&&(0,t.jsx)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs",children:"\uD83D\uDD13 Unlock"}),e.permissions.manageSettings&&(0,t.jsx)("span",{className:"bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs",children:"⚙️ Ayarlar"}),e.permissions.manageUsers&&(0,t.jsx)("span",{className:"bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs",children:"\uD83D\uDC65 Kullanıcılar"})]})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Oluşturulma:"}),(0,t.jsx)("span",{className:"font-medium",children:q(e.createdAt)})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Son Giriş:"}),(0,t.jsx)("span",{className:"font-medium",children:e.lastLogin?q(e.lastLogin):"Hi\xe7 giriş yapmamış"})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(c.$,{className:"flex-1",variant:"outline",onClick:()=>I(e),children:[(0,t.jsx)(P.A,{className:"mr-2 h-4 w-4"}),"D\xfczenle"]}),"admin"!==e.username&&(0,t.jsx)(c.$,{variant:"destructive",onClick:()=>V(e.id),children:(0,t.jsx)(Z.A,{className:"h-4 w-4"})})]})]},e.id))}):(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(p.XI,{children:[(0,t.jsx)(p.A0,{children:(0,t.jsxs)(p.Hj,{children:[(0,t.jsx)(p.nd,{children:"Kullanıcı Adı"}),(0,t.jsx)(p.nd,{children:"Rol"}),(0,t.jsx)(p.nd,{children:"Yetkiler"}),(0,t.jsx)(p.nd,{children:"Oluşturulma"}),(0,t.jsx)(p.nd,{children:"Son Giriş"}),(0,t.jsx)(p.nd,{children:"İşlemler"})]})}),(0,t.jsx)(p.BF,{children:r.map(e=>(0,t.jsxs)(p.Hj,{children:[(0,t.jsx)(p.nA,{className:"font-medium",children:e.username}),(0,t.jsx)(p.nA,{children:(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("admin"===e.role?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:e.role})}),(0,t.jsx)(p.nA,{children:(0,t.jsxs)("div",{className:"text-xs space-y-1",children:[e.permissions.viewUsers&&(0,t.jsx)("div",{children:"\uD83D\uDC41️ View Users"}),e.permissions.unlockUsers&&(0,t.jsx)("div",{children:"\uD83D\uDD13 Unlock Users"}),e.permissions.manageSettings&&(0,t.jsx)("div",{children:"⚙️ Manage Settings"}),e.permissions.manageUsers&&(0,t.jsx)("div",{children:"\uD83D\uDC65 Manage Users"})]})}),(0,t.jsx)(p.nA,{children:q(e.createdAt)}),(0,t.jsx)(p.nA,{children:e.lastLogin?q(e.lastLogin):"Hi\xe7"}),(0,t.jsx)(p.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>I(e),children:(0,t.jsx)(P.A,{className:"h-4 w-4"})}),"admin"!==e.username&&(0,t.jsx)(c.$,{size:"sm",variant:"destructive",onClick:()=>V(e.id),children:(0,t.jsx)(Z.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]})]})}),!1]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[108,460,349,64,449,441,684,358],()=>s(9036)),_N_E=e.O()}]);