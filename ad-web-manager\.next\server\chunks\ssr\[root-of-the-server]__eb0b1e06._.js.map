{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/hooks/useDeviceDetection.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport interface DeviceInfo {\n  isMobile: boolean;\n  isTablet: boolean;\n  isDesktop: boolean;\n  deviceType: 'mobile' | 'tablet' | 'desktop';\n  screenWidth: number;\n  userAgent: string;\n}\n\nexport function useDeviceDetection(): DeviceInfo {\n  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({\n    isMobile: false,\n    isTablet: false,\n    isDesktop: true,\n    deviceType: 'desktop',\n    screenWidth: 1024,\n    userAgent: ''\n  });\n  const [isHydrated, setIsHydrated] = useState(false);\n\n  useEffect(() => {\n    setIsHydrated(true);\n    const detectDevice = () => {\n      const userAgent = navigator.userAgent || '';\n      const screenWidth = window.innerWidth;\n\n      // Mobile device detection via user agent\n      const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;\n      const tabletRegex = /iPad|Android(?=.*\\bMobile\\b)(?=.*\\bTablet\\b)|Android(?=.*\\bTablet\\b)/i;\n      \n      // Screen size based detection\n      const isMobileScreen = screenWidth < 768;\n      const isTabletScreen = screenWidth >= 768 && screenWidth < 1024;\n      const isDesktopScreen = screenWidth >= 1024;\n\n      // User agent based detection\n      const isMobileUA = mobileRegex.test(userAgent) && !tabletRegex.test(userAgent);\n      const isTabletUA = tabletRegex.test(userAgent);\n\n      // Combined detection (prioritize user agent for mobile devices)\n      const isMobile = isMobileUA || (isMobileScreen && !isTabletUA);\n      const isTablet = isTabletUA || (isTabletScreen && !isMobileUA);\n      const isDesktop = !isMobile && !isTablet;\n\n      let deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop';\n      if (isMobile) deviceType = 'mobile';\n      else if (isTablet) deviceType = 'tablet';\n\n      setDeviceInfo({\n        isMobile,\n        isTablet,\n        isDesktop,\n        deviceType,\n        screenWidth,\n        userAgent\n      });\n    };\n\n    // Initial detection\n    detectDevice();\n\n    // Listen for window resize\n    const handleResize = () => {\n      detectDevice();\n    };\n\n    window.addEventListener('resize', handleResize);\n    \n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n\n  // Return default values during SSR to prevent hydration mismatch\n  if (!isHydrated) {\n    return {\n      isMobile: false,\n      isTablet: false,\n      isDesktop: true,\n      deviceType: 'desktop',\n      screenWidth: 1024,\n      userAgent: ''\n    };\n  }\n\n  return deviceInfo;\n}\n\n// Server-side device detection helper\nexport function getServerDeviceInfo(userAgent: string): Omit<DeviceInfo, 'screenWidth'> {\n  const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;\n  const tabletRegex = /iPad|Android(?=.*\\bMobile\\b)(?=.*\\bTablet\\b)|Android(?=.*\\bTablet\\b)/i;\n  \n  const isMobileUA = mobileRegex.test(userAgent) && !tabletRegex.test(userAgent);\n  const isTabletUA = tabletRegex.test(userAgent);\n  \n  const isMobile = isMobileUA;\n  const isTablet = isTabletUA;\n  const isDesktop = !isMobile && !isTablet;\n\n  let deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop';\n  if (isMobile) deviceType = 'mobile';\n  else if (isTablet) deviceType = 'tablet';\n\n  return {\n    isMobile,\n    isTablet,\n    isDesktop,\n    deviceType,\n    userAgent\n  };\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAaO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,UAAU;QACV,UAAU;QACV,WAAW;QACX,YAAY;QACZ,aAAa;QACb,WAAW;IACb;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;QACd,MAAM,eAAe;YACnB,MAAM,YAAY,UAAU,SAAS,IAAI;YACzC,MAAM,cAAc,OAAO,UAAU;YAErC,yCAAyC;YACzC,MAAM,cAAc;YACpB,MAAM,cAAc;YAEpB,8BAA8B;YAC9B,MAAM,iBAAiB,cAAc;YACrC,MAAM,iBAAiB,eAAe,OAAO,cAAc;YAC3D,MAAM,kBAAkB,eAAe;YAEvC,6BAA6B;YAC7B,MAAM,aAAa,YAAY,IAAI,CAAC,cAAc,CAAC,YAAY,IAAI,CAAC;YACpE,MAAM,aAAa,YAAY,IAAI,CAAC;YAEpC,gEAAgE;YAChE,MAAM,WAAW,cAAe,kBAAkB,CAAC;YACnD,MAAM,WAAW,cAAe,kBAAkB,CAAC;YACnD,MAAM,YAAY,CAAC,YAAY,CAAC;YAEhC,IAAI,aAA8C;YAClD,IAAI,UAAU,aAAa;iBACtB,IAAI,UAAU,aAAa;YAEhC,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QAEA,oBAAoB;QACpB;QAEA,2BAA2B;QAC3B,MAAM,eAAe;YACnB;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,iEAAiE;IACjE,IAAI,CAAC,YAAY;QACf,OAAO;YACL,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;YACZ,aAAa;YACb,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,SAAiB;IACnD,MAAM,cAAc;IACpB,MAAM,cAAc;IAEpB,MAAM,aAAa,YAAY,IAAI,CAAC,cAAc,CAAC,YAAY,IAAI,CAAC;IACpE,MAAM,aAAa,YAAY,IAAI,CAAC;IAEpC,MAAM,WAAW;IACjB,MAAM,WAAW;IACjB,MAAM,YAAY,CAAC,YAAY,CAAC;IAEhC,IAAI,aAA8C;IAClD,IAAI,UAAU,aAAa;SACtB,IAAI,UAAU,aAAa;IAEhC,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,kKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,kKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,kKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,kKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,kKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,kKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/components/ui/mobile-nav.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useSession, signOut } from 'next-auth/react';\nimport { Button } from '@/components/ui/button';\nimport { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';\nimport { Menu, X, Users, Settings, LogOut, UserPlus, KeyRound, Home } from 'lucide-react';\n\ninterface MobileNavProps {\n  currentPath?: string;\n}\n\nexport function MobileNav({ currentPath = '/' }: MobileNavProps) {\n  const { data: session } = useSession();\n  const [isOpen, setIsOpen] = useState(false);\n\n  if (!session) {\n    return null;\n  }\n\n  const handleLogout = () => {\n    signOut({ callbackUrl: '/login' });\n    setIsOpen(false);\n  };\n\n  const navItems = [\n    {\n      href: '/',\n      label: 'Dashboard',\n      icon: Home,\n      show: true\n    },\n    {\n      href: '/users',\n      label: 'Locked Users',\n      icon: Users,\n      show: true\n    },\n    {\n      href: '/password-expiry',\n      label: 'Password Expiry',\n      icon: KeyRound,\n      show: true\n    },\n    {\n      href: '/manage-users',\n      label: 'Manage Users',\n      icon: UserPlus,\n      show: session.user.permissions.manageUsers\n    },\n    {\n      href: '/settings',\n      label: 'Settings',\n      icon: Settings,\n      show: session.user.permissions.manageSettings\n    }\n  ];\n\n  return (\n    <nav className=\"border-b bg-card\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Image\n              src=\"/Bayraktar Holding Logo.png\"\n              alt=\"Bayraktar Holding Logo\"\n              width={40}\n              height={40}\n              className=\"h-8 w-auto\"\n            />\n            <span className=\"text-lg font-bold text-primary hidden sm:block\">AD Web Manager</span>\n            <span className=\"text-sm font-bold text-primary sm:hidden\">AD Web</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-6\">\n            {navItems.filter(item => item.show).map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={`text-sm font-medium hover:text-primary transition-colors ${\n                  currentPath === item.href ? 'text-primary' : 'text-muted-foreground'\n                }`}\n              >\n                {item.label}\n              </Link>\n            ))}\n            <Button onClick={handleLogout} variant=\"outline\" size=\"sm\">\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              Çıkış\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <Sheet open={isOpen} onOpenChange={setIsOpen}>\n            <SheetTrigger asChild>\n              <Button variant=\"ghost\" size=\"sm\" className=\"md:hidden\">\n                <Menu className=\"h-5 w-5\" />\n                <span className=\"sr-only\">Menu</span>\n              </Button>\n            </SheetTrigger>\n            <SheetContent side=\"right\" className=\"w-[300px] sm:w-[400px]\">\n              <div className=\"flex flex-col h-full\">\n                {/* Header */}\n                <div className=\"flex items-center justify-between pb-4 border-b\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Image\n                      src=\"/Bayraktar Holding Logo.png\"\n                      alt=\"Bayraktar Holding Logo\"\n                      width={32}\n                      height={32}\n                      className=\"h-8 w-auto\"\n                    />\n                    <span className=\"text-lg font-bold text-primary\">AD Web Manager</span>\n                  </div>\n                </div>\n\n                {/* User Info */}\n                <div className=\"py-4 border-b\">\n                  <p className=\"text-sm text-muted-foreground\">Hoş geldiniz,</p>\n                  <p className=\"font-medium\">{session.user.name}</p>\n                  <p className=\"text-xs text-muted-foreground\">{session.user.email}</p>\n                </div>\n\n                {/* Navigation Items */}\n                <div className=\"flex-1 py-4\">\n                  <div className=\"space-y-2\">\n                    {navItems.filter(item => item.show).map((item) => {\n                      const Icon = item.icon;\n                      return (\n                        <Link\n                          key={item.href}\n                          href={item.href}\n                          onClick={() => setIsOpen(false)}\n                          className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                            currentPath === item.href\n                              ? 'bg-primary text-primary-foreground'\n                              : 'text-muted-foreground hover:text-primary hover:bg-accent'\n                          }`}\n                        >\n                          <Icon className=\"h-5 w-5\" />\n                          <span>{item.label}</span>\n                        </Link>\n                      );\n                    })}\n                  </div>\n                </div>\n\n                {/* Logout Button */}\n                <div className=\"pt-4 border-t\">\n                  <Button onClick={handleLogout} variant=\"outline\" className=\"w-full\">\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    Çıkış Yap\n                  </Button>\n                </div>\n              </div>\n            </SheetContent>\n          </Sheet>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAcO,SAAS,UAAU,EAAE,cAAc,GAAG,EAAkB;IAC7D,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAS;QAChC,UAAU;IACZ;IAEA,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,MAAM,mMAAA,CAAA,OAAI;YACV,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,8MAAA,CAAA,WAAQ;YACd,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,8MAAA,CAAA,WAAQ;YACd,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,WAAW;QAC5C;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,0MAAA,CAAA,WAAQ;YACd,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,cAAc;QAC/C;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,8OAAC;gCAAK,WAAU;0CAAiD;;;;;;0CACjE,8OAAC;gCAAK,WAAU;0CAA2C;;;;;;;;;;;;kCAI7D,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,qBACvC,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,yDAAyD,EACnE,gBAAgB,KAAK,IAAI,GAAG,iBAAiB,yBAC7C;8CAED,KAAK,KAAK;mCANN,KAAK,IAAI;;;;;0CASlB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,SAAQ;gCAAU,MAAK;;kDACpD,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMvC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,MAAM;wBAAQ,cAAc;;0CACjC,8OAAC,iIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;;sDAC1C,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,8OAAC,iIAAA,CAAA,eAAY;gCAAC,MAAK;gCAAQ,WAAU;0CACnC,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;;;;;;;sDAKrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;8DAC7C,8OAAC;oDAAE,WAAU;8DAAe,QAAQ,IAAI,CAAC,IAAI;;;;;;8DAC7C,8OAAC;oDAAE,WAAU;8DAAiC,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;sDAIlE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC;oDACvC,MAAM,OAAO,KAAK,IAAI;oDACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,SAAS,IAAM,UAAU;wDACzB,WAAW,CAAC,uFAAuF,EACjG,gBAAgB,KAAK,IAAI,GACrB,uCACA,4DACJ;;0EAEF,8OAAC;gEAAK,WAAU;;;;;;0EAChB,8OAAC;0EAAM,KAAK,KAAK;;;;;;;uDAVZ,KAAK,IAAI;;;;;gDAapB;;;;;;;;;;;sDAKJ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAc,SAAQ;gDAAU,WAAU;;kEACzD,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/components/ui/desktop-nav.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useSession, signOut } from 'next-auth/react';\nimport { Button } from '@/components/ui/button';\nimport { LogOut } from 'lucide-react';\n\ninterface DesktopNavProps {\n  currentPath?: string;\n}\n\nexport function DesktopNav({ currentPath = '/' }: DesktopNavProps) {\n  const { data: session } = useSession();\n\n  if (!session) {\n    return null;\n  }\n\n  const handleLogout = () => {\n    signOut({ callbackUrl: '/login' });\n  };\n\n  const navItems = [\n    {\n      href: '/',\n      label: 'Dashboard',\n      show: true\n    },\n    {\n      href: '/users',\n      label: 'Locked Users',\n      show: true\n    },\n    {\n      href: '/password-expiry',\n      label: 'Password Expiry',\n      show: true\n    },\n    {\n      href: '/manage-users',\n      label: 'Manage Users',\n      show: session.user.permissions.manageUsers\n    },\n    {\n      href: '/settings',\n      label: 'Settings',\n      show: session.user.permissions.manageSettings\n    }\n  ];\n\n  return (\n    <nav className=\"border-b bg-card\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 lg:h-20 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <Image\n              src=\"/Bayraktar Holding Logo.png\"\n              alt=\"Bayraktar Holding Logo\"\n              width={120}\n              height={120}\n              className=\"h-10 lg:h-12 w-auto\"\n            />\n            <span className=\"text-lg lg:text-xl font-bold text-primary\">AD Web Manager</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"flex items-center space-x-6\">\n            {navItems.filter(item => item.show).map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={`text-sm font-medium hover:text-primary transition-colors ${\n                  currentPath === item.href ? 'text-primary' : 'text-muted-foreground'\n                }`}\n              >\n                {item.label}\n              </Link>\n            ))}\n            \n            {/* User Info */}\n            <div className=\"flex items-center space-x-3 border-l pl-6\">\n              <div className=\"text-right\">\n                <div className=\"text-sm font-medium text-foreground\">\n                  {session.user.name}\n                </div>\n                <div className=\"text-xs text-muted-foreground\">\n                  {session.user.role}\n                </div>\n              </div>\n              <Button onClick={handleLogout} variant=\"outline\" size=\"sm\">\n                <LogOut className=\"mr-2 h-4 w-4\" />\n                Çıkış\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAaO,SAAS,WAAW,EAAE,cAAc,GAAG,EAAmB;IAC/D,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAS;IAClC;IAEA,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,WAAW;QAC5C;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,cAAc;QAC/C;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,8OAAC;gCAAK,WAAU;0CAA4C;;;;;;;;;;;;kCAI9D,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,qBACvC,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,yDAAyD,EACnE,gBAAgB,KAAK,IAAI,GAAG,iBAAiB,yBAC7C;8CAED,KAAK,KAAK;mCANN,KAAK,IAAI;;;;;0CAWlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI,CAAC,IAAI;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;kDAGtB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAc,SAAQ;wCAAU,MAAK;;0DACpD,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 1000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useSession } from 'next-auth/react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { useDeviceDetection } from '@/hooks/useDeviceDetection';\nimport { MobileNav } from '@/components/ui/mobile-nav';\nimport { DesktopNav } from '@/components/ui/desktop-nav';\nimport { Users, UserPlus, KeyRound, BarChart3, Settings } from 'lucide-react';\n\nexport default function Home() {\n  const { data: session } = useSession();\n  const device = useDeviceDetection();\n\n  if (!session) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Adaptive Navigation */}\n      {device.isMobile ? (\n        <MobileNav currentPath=\"/\" />\n      ) : (\n        <DesktopNav currentPath=\"/\" />\n      )}\n\n      {/* Main Content */}\n      <main className={`\n        ${device.isMobile ? 'pb-safe px-4 py-4' : 'container mx-auto px-4 py-6'}\n      `}>\n        <div className={`${device.isMobile ? 'space-y-4' : 'space-y-6'}`}>\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground mb-2 sm:mb-4\">\n              Hoş Geldiniz, {session.user.name}\n            </h1>\n            <p className=\"text-base sm:text-lg lg:text-xl text-muted-foreground\">\n              Active Directory Web Yönetim Paneli\n            </p>\n          </div>\n\n          <div className={`\n            grid\n            ${device.isMobile ? 'grid-cols-1 gap-4' : device.deviceType === 'tablet' ? 'grid-cols-2 gap-4' : 'grid-cols-3 gap-6'}\n          `}>\n            {session.user.permissions.unlockUsers && (\n              <Card className=\"hover:shadow-lg transition-shadow\">\n                <CardHeader className=\"text-center pb-4\">\n                  <div className=\"mx-auto mb-3 flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-lg bg-primary/10\">\n                    <Users className=\"h-5 w-5 sm:h-6 sm:w-6 text-primary\" />\n                  </div>\n                  <CardTitle className=\"text-lg sm:text-xl\">Locked Users</CardTitle>\n                  <CardDescription className=\"text-sm\">\n                    Kilitlenen kullanıcıları görüntüle ve unlock yap\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"text-center pt-0\">\n                  <Button asChild size=\"sm\" className=\"w-full sm:w-auto\">\n                    <Link href=\"/users\">\n                      Kullanıcıları Görüntüle\n                    </Link>\n                  </Button>\n                </CardContent>\n              </Card>\n            )}\n\n            {session.user.permissions.unlockUsers && (\n              <Card className=\"hover:shadow-lg transition-shadow\">\n                <CardHeader className=\"text-center\">\n                  <div className=\"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100\">\n                    <KeyRound className=\"h-6 w-6 text-orange-600\" />\n                  </div>\n                  <CardTitle>Password Expiry</CardTitle>\n                  <CardDescription>\n                    Şifre süresi dolan kullanıcıları yönet\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"text-center\">\n                  <Button variant=\"outline\" className=\"border-orange-200 text-orange-700 hover:bg-orange-50\" asChild>\n                    <Link href=\"/password-expiry\">\n                      Şifre Yönetimi\n                    </Link>\n                  </Button>\n                </CardContent>\n              </Card>\n            )}\n\n            {session.user.permissions.manageSettings && (\n              <Card className=\"hover:shadow-lg transition-shadow\">\n                <CardHeader className=\"text-center\">\n                  <div className=\"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-secondary/10\">\n                    <Settings className=\"h-6 w-6 text-secondary-foreground\" />\n                  </div>\n                  <CardTitle>LDAP Settings</CardTitle>\n                  <CardDescription>\n                    LDAP bağlantı ayarlarını yapılandır\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"text-center\">\n                  <Button variant=\"secondary\" asChild>\n                    <Link href=\"/settings\">\n                      Ayarları Düzenle\n                    </Link>\n                  </Button>\n                </CardContent>\n              </Card>\n            )}\n\n            {session.user.permissions.manageUsers && (\n              <Card className=\"hover:shadow-lg transition-shadow\">\n                <CardHeader className=\"text-center\">\n                  <div className=\"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-green-100\">\n                    <UserPlus className=\"h-6 w-6 text-green-600\" />\n                  </div>\n                  <CardTitle>User Management</CardTitle>\n                  <CardDescription>\n                    Sistem kullanıcılarını yönet\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"text-center\">\n                  <Button variant=\"outline\" className=\"border-green-200 text-green-700 hover:bg-green-50\" asChild>\n                    <Link href=\"/manage-users\">\n                      Kullanıcı Yönetimi\n                    </Link>\n                  </Button>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n\n          <div className=\"bg-muted/50 rounded-lg p-6\">\n            <h2 className=\"text-2xl font-semibold text-foreground mb-4\">Sistem Durumu</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-primary mb-2\">✓</div>\n                <div className=\"text-sm text-muted-foreground\">LDAP Bağlantısı</div>\n                <div className=\"text-lg font-medium text-foreground\">Aktif</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-600 mb-2\">🔐</div>\n                <div className=\"text-sm text-muted-foreground\">Güvenlik</div>\n                <div className=\"text-lg font-medium text-foreground\">Korumalı</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-blue-600 mb-2\">⚡</div>\n                <div className=\"text-sm text-muted-foreground\">Sistem</div>\n                <div className=\"text-lg font-medium text-foreground\">Çalışıyor</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Debug info (only in development) */}\n      {process.env.NODE_ENV === 'development' && (\n        <div className=\"fixed bottom-2 right-2 bg-black/80 text-white text-xs p-2 rounded z-50\">\n          <div>Device: {device.deviceType}</div>\n          <div>Width: {device.screenWidth}px</div>\n          <div>Mobile: {device.isMobile ? 'Yes' : 'No'}</div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD;IAEhC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,OAAO,QAAQ,iBACd,8OAAC,yIAAA,CAAA,YAAS;gBAAC,aAAY;;;;;qCAEvB,8OAAC,0IAAA,CAAA,aAAU;gBAAC,aAAY;;;;;;0BAI1B,8OAAC;gBAAK,WAAW,CAAC;QAChB,EAAE,OAAO,QAAQ,GAAG,sBAAsB,8BAA8B;MAC1E,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAW,GAAG,OAAO,QAAQ,GAAG,cAAc,aAAa;;sCAC9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAA0E;wCACvE,QAAQ,IAAI,CAAC,IAAI;;;;;;;8CAElC,8OAAC;oCAAE,WAAU;8CAAwD;;;;;;;;;;;;sCAKvE,8OAAC;4BAAI,WAAW,CAAC;;YAEf,EAAE,OAAO,QAAQ,GAAG,sBAAsB,OAAO,UAAU,KAAK,WAAW,sBAAsB,oBAAoB;UACvH,CAAC;;gCACE,QAAQ,IAAI,CAAC,WAAW,CAAC,WAAW,kBACnC,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAqB;;;;;;8DAC1C,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DAAU;;;;;;;;;;;;sDAIvC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,MAAK;gDAAK,WAAU;0DAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAS;;;;;;;;;;;;;;;;;;;;;;gCAQ3B,QAAQ,IAAI,CAAC,WAAW,CAAC,WAAW,kBACnC,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;gDAAuD,OAAO;0DAChG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAmB;;;;;;;;;;;;;;;;;;;;;;gCAQrC,QAAQ,IAAI,CAAC,WAAW,CAAC,cAAc,kBACtC,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAY,OAAO;0DACjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAY;;;;;;;;;;;;;;;;;;;;;;gCAQ9B,QAAQ,IAAI,CAAC,WAAW,CAAC,WAAW,kBACnC,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;gDAAoD,OAAO;0DAC7F,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAC5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAuC;;;;;;8DACtD,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAsC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAyC;;;;;;8DACxD,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAsC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9D,oDAAyB,+BACxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAI;4BAAS,OAAO,UAAU;;;;;;;kCAC/B,8OAAC;;4BAAI;4BAAQ,OAAO,WAAW;4BAAC;;;;;;;kCAChC,8OAAC;;4BAAI;4BAAS,OAAO,QAAQ,GAAG,QAAQ;;;;;;;;;;;;;;;;;;;AAKlD", "debugId": null}}]}