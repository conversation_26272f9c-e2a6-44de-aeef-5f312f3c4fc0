{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "g6FTlHivHh8sRLtyrr0Pgac+15pL9J1g8zHgpTLDxSE=", "__NEXT_PREVIEW_MODE_ID": "8d76aedf08abe45b322fc34a7457fca3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "247fa2a446667060032daba65413b5fd4869c960f009e573980a075dec02aba2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0fbca20cefcf28690aa541cb76d2e7b9597559d86ce00bff13a80acbf054db9d"}}}, "instrumentation": null, "functions": {}}