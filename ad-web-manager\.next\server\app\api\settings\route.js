(()=>{var e={};e.id=177,e.ids=[177],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11786:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>j,routeModule:()=>w,serverHooks:()=>m,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>v});var t={};s.r(t),s.d(t,{GET:()=>x,POST:()=>g});var n=s(96559),a=s(48088),o=s(37719),i=s(32190),u=s(29021),p=s.n(u),c=s(33873);let d=s.n(c)().join(process.cwd(),"ldap-settings.json"),l={server:"",port:"389",baseDN:"",username:"",password:"",useSSL:!1};async function x(e){try{let r=new URL(e.url),s="true"===r.searchParams.get("includePassword");if(!p().existsSync(d))return i.NextResponse.json(l);{let e=p().readFileSync(d,"utf8"),r=JSON.parse(e);return i.NextResponse.json({...r,password:s?r.password:r.password?"••••••••":""})}}catch(e){return console.error("Error loading settings:",e),i.NextResponse.json({error:"Ayarlar y\xfcklenirken hata oluştu"},{status:500})}}async function g(e){try{let r=await e.json();if(!r.server||!r.baseDN||!r.username)return i.NextResponse.json({error:"Server, Base DN ve Username alanları zorunludur"},{status:400});if("••••••••"===r.password&&p().existsSync(d)){let e=p().readFileSync(d,"utf8");r.password=JSON.parse(e).password}return p().writeFileSync(d,JSON.stringify(r,null,2)),i.NextResponse.json({success:!0})}catch(e){return console.error("Error saving settings:",e),i.NextResponse.json({error:"Ayarlar kaydedilirken hata oluştu"},{status:500})}}let w=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/settings/route",pathname:"/api/settings",filename:"route",bundlePath:"app/api/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\settings\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:y,workUnitAsyncStorage:v,serverHooks:m}=w;function j(){return(0,o.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:v})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580],()=>s(11786));module.exports=t})();