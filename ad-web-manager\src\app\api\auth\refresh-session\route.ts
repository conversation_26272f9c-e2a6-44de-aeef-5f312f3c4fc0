import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getUserById } from '@/lib/auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function POST() {
  try {
    console.log('🔄 Refresh session API called');
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      console.log('❌ No session or user ID found');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('👤 Current session user ID:', session.user.id);

    // Get updated user data from database
    const updatedUser = getUserById(session.user.id);

    if (!updatedUser) {
      console.log('❌ User not found in database');
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    console.log('✅ Updated user data:', {
      id: updatedUser.id,
      username: updatedUser.username,
      role: updatedUser.role,
      permissions: updatedUser.permissions
    });

    // Return updated user data for session refresh
    return NextResponse.json({
      user: {
        id: updatedUser.id,
        name: updatedUser.username,
        username: updatedUser.username,
        role: updatedUser.role,
        permissions: updatedUser.permissions,
      }
    });
  } catch (error) {
    console.error('❌ Error refreshing session:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
