'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, Settings, LogOut, UserPlus, KeyRound } from 'lucide-react';

export default function Home() {
  const { data: session } = useSession();

  if (!session) {
    return null;
  }

  const handleLogout = () => {
    signOut({ callbackUrl: '/login' });
  };

  return (
    <div className="min-h-screen bg-background">
      <nav className="border-b bg-card">
        <div className="container mx-auto px-4">
          <div className="flex h-24 items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <Image 
                src="/bayraktar_holding_logo.jpeg" 
                alt="Bayraktar Holding Logo" 
                width={80} 
                height={80}
                className="rounded-md"
              />
              <span className="text-xl font-bold text-primary">AD Web Manager</span>
            </Link>
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-sm font-medium text-foreground hover:text-primary">
                Dashboard
              </Link>
              <Link href="/users" className="text-sm font-medium text-foreground hover:text-primary">
                Locked Users
              </Link>
              <Link href="/password-expiry" className="text-sm font-medium text-foreground hover:text-primary">
                Password Expiry
              </Link>
              {session.user.permissions.manageSettings && (
                <Link href="/settings" className="text-sm font-medium text-foreground hover:text-primary">
                  Settings
                </Link>
              )}
              {session.user.permissions.manageUsers && (
                <Link href="/manage-users" className="text-sm font-medium text-foreground hover:text-primary">
                  Manage Users
                </Link>
              )}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  {session.user.name} ({session.user.role})
                </span>
                <Button variant="ghost" size="sm" onClick={handleLogout}>
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              Hoş Geldiniz, {session.user.name}
            </h1>
            <p className="text-xl text-muted-foreground">
              Active Directory Web Yönetim Paneli
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {session.user.permissions.unlockUsers && (
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                    <Users className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle>Locked Users</CardTitle>
                  <CardDescription>
                    Kilitlenen kullanıcıları görüntüle ve unlock yap
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button asChild>
                    <Link href="/users">
                      Kullanıcıları Görüntüle
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}

            {session.user.permissions.unlockUsers && (
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100">
                    <KeyRound className="h-6 w-6 text-orange-600" />
                  </div>
                  <CardTitle>Password Expiry</CardTitle>
                  <CardDescription>
                    Şifre süresi dolan kullanıcıları yönet
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="outline" className="border-orange-200 text-orange-700 hover:bg-orange-50" asChild>
                    <Link href="/password-expiry">
                      Şifre Yönetimi
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}

            {session.user.permissions.manageSettings && (
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-secondary/10">
                    <Settings className="h-6 w-6 text-secondary-foreground" />
                  </div>
                  <CardTitle>LDAP Settings</CardTitle>
                  <CardDescription>
                    LDAP bağlantı ayarlarını yapılandır
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="secondary" asChild>
                    <Link href="/settings">
                      Ayarları Düzenle
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}

            {session.user.permissions.manageUsers && (
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                    <UserPlus className="h-6 w-6 text-green-600" />
                  </div>
                  <CardTitle>User Management</CardTitle>
                  <CardDescription>
                    Sistem kullanıcılarını yönet
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="outline" className="border-green-200 text-green-700 hover:bg-green-50" asChild>
                    <Link href="/manage-users">
                      Kullanıcı Yönetimi
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          <div className="bg-muted/50 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-foreground mb-4">Sistem Durumu</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">✓</div>
                <div className="text-sm text-muted-foreground">LDAP Bağlantısı</div>
                <div className="text-lg font-medium text-foreground">Aktif</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">🔐</div>
                <div className="text-sm text-muted-foreground">Güvenlik</div>
                <div className="text-lg font-medium text-foreground">Korumalı</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">⚡</div>
                <div className="text-sm text-muted-foreground">Sistem</div>
                <div className="text-lg font-medium text-foreground">Çalışıyor</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
