'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Menu, X, Users, Settings, LogOut, UserPlus, KeyRound, Home } from 'lucide-react';

interface MobileNavProps {
  currentPath?: string;
}

export function MobileNav({ currentPath = '/' }: MobileNavProps) {
  const { data: session } = useSession();
  const [isOpen, setIsOpen] = useState(false);

  if (!session) {
    return null;
  }

  const handleLogout = () => {
    signOut({ callbackUrl: '/login' });
    setIsOpen(false);
  };

  const navItems = [
    {
      href: '/',
      label: 'Dashboard',
      icon: Home,
      show: true
    },
    {
      href: '/users',
      label: 'Locked Users',
      icon: Users,
      show: true
    },
    {
      href: '/password-expiry',
      label: 'Password Expiry',
      icon: KeyRound,
      show: true
    },
    {
      href: '/manage-users',
      label: 'Manage Users',
      icon: UserPlus,
      show: session.user.permissions.manageUsers
    },
    {
      href: '/settings',
      label: 'Settings',
      icon: Settings,
      show: session.user.permissions.manageSettings
    }
  ];

  return (
    <nav className="border-b bg-card">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="/Bayraktar Holding Logo.png"
              alt="Bayraktar Holding Logo"
              width={40}
              height={40}
              className="h-8 w-auto"
              priority
              quality={100}
            />
            <span className="text-base font-bold text-primary truncate">AD Web Manager</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            {navItems.filter(item => item.show).map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`text-sm font-medium hover:text-primary transition-colors ${
                  currentPath === item.href ? 'text-primary' : 'text-muted-foreground'
                }`}
              >
                {item.label}
              </Link>
            ))}
            <Button onClick={handleLogout} variant="outline" size="sm">
              <LogOut className="mr-2 h-4 w-4" />
              Çıkış
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex items-center justify-between pb-4 border-b">
                  <div className="flex items-center space-x-2">
                    <Image
                      src="/Bayraktar Holding Logo.png"
                      alt="Bayraktar Holding Logo"
                      width={32}
                      height={32}
                      className="h-8 w-auto"
                      priority
                      quality={100}
                    />
                    <span className="text-lg font-bold text-primary">AD Web Manager</span>
                  </div>
                </div>

                {/* User Info */}
                <div className="py-4 border-b">
                  <p className="text-sm text-muted-foreground">Hoş geldiniz,</p>
                  <p className="font-medium">{session.user.name}</p>
                  <p className="text-xs text-muted-foreground">{session.user.email}</p>
                </div>

                {/* Navigation Items */}
                <div className="flex-1 py-4">
                  <div className="space-y-2">
                    {navItems.filter(item => item.show).map((item) => {
                      const Icon = item.icon;
                      return (
                        <Link
                          key={item.href}
                          href={item.href}
                          onClick={() => setIsOpen(false)}
                          className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                            currentPath === item.href
                              ? 'bg-primary text-primary-foreground'
                              : 'text-muted-foreground hover:text-primary hover:bg-accent'
                          }`}
                        >
                          <Icon className="h-5 w-5" />
                          <span>{item.label}</span>
                        </Link>
                      );
                    })}
                  </div>
                </div>

                {/* Logout Button */}
                <div className="pt-4 border-t">
                  <Button onClick={handleLogout} variant="outline" className="w-full">
                    <LogOut className="mr-2 h-4 w-4" />
                    Çıkış Yap
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
}
