@echo off
title AD Web Manager - Domain Server (ad.bayraktar.com)
color 0A

echo.
echo ========================================
echo   AD Web Manager - Domain Server
echo   Domain: ad.bayraktar.com
echo   Port: 80 (Standard HTTP)
echo ========================================
echo.

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Bu script Administrator yet<PERSON>i ile calistirilmalidir!
    echo.
    echo Cozum:
    echo 1. Command Prompt'u "Run as Administrator" ile acin
    echo 2. Bu script'i tekrar calistirin
    echo.
    pause
    exit /b 1
)

echo [INFO] Administrator yetkisi dogrulandi ✓
echo.

REM Set production environment
set NODE_ENV=production
echo [INFO] Production mode aktif ✓

REM Check if .next folder exists (build required)
if not exist ".next" (
    echo [INFO] Production build bulunamadi, build yapiliyor...
    echo.
    npm run build
    if %ERRORLEVEL% neq 0 (
        echo [ERROR] Build basarisiz! Hatalari kontrol edin.
        pause
        exit /b 1
    )
    echo [INFO] Build tamamlandi ✓
    echo.
) else (
    echo [INFO] Production build mevcut ✓
)

REM Kill any existing process on port 80
echo [INFO] Port 80 kontrol ediliyor...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :80') do (
    taskkill /f /pid %%a >nul 2>&1
)
echo [INFO] Port 80 temizlendi ✓
echo.

echo ========================================
echo   Server Baslatiliyor...
echo ========================================
echo.
echo [INFO] Erisim adresleri:
echo   - Lokal:    http://localhost
echo   - Network:  http://************
echo   - Domain:   http://ad.bayraktar.com
echo.
echo [INFO] Server'i durdurmak icin Ctrl+C basin
echo.
echo ========================================

REM Start the production server on port 80
npm run start:production

echo.
echo [INFO] Server durduruldu.
pause
