{"/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/_not-found/page": "/_not-found", "/api/auth/invalidate-sessions/route": "/api/auth/invalidate-sessions", "/api/auth/refresh-session/route": "/api/auth/refresh-session", "/api/debug/session/route": "/api/debug/session", "/api/extend-password/route": "/api/extend-password", "/api/locked-users/route": "/api/locked-users", "/api/password-expiry/route": "/api/password-expiry", "/api/settings/route": "/api/settings", "/api/search-user/route": "/api/search-user", "/api/test-connection/route": "/api/test-connection", "/api/test-ldap/route": "/api/test-ldap", "/api/unlock-user-ps/route": "/api/unlock-user-ps", "/api/users/[id]/route": "/api/users/[id]", "/api/unlock-user/route": "/api/unlock-user", "/api/users/route": "/api/users", "/favicon.ico/route": "/favicon.ico", "/page": "/", "/settings/page": "/settings", "/login/page": "/login", "/users/page": "/users", "/manage-users/page": "/manage-users", "/password-expiry/page": "/password-expiry"}