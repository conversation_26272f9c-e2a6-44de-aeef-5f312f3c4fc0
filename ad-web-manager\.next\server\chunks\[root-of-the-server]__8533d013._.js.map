{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/app/api/test-connection/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport ldap from 'ldapjs';\n\ninterface LdapSettings {\n  server: string;\n  port: string;\n  baseDN: string;\n  username: string;\n  password: string;\n  useSSL: boolean;\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const settings: LdapSettings = await request.json();\n    const startTime = Date.now();\n\n    // Validate required fields\n    if (!settings.server || !settings.baseDN || !settings.username || !settings.password) {\n      return NextResponse.json(\n        { error: 'Tüm alanlar doldurulmalıdır' },\n        { status: 400 }\n      );\n    }\n\n    const protocol = settings.useSSL ? 'ldaps' : 'ldap';\n    const url = `${protocol}://${settings.server}:${settings.port}`;\n\n    console.log('LDAP Test Connection:', {\n      url,\n      username: settings.username,\n      baseDN: settings.baseDN,\n      useSSL: settings.useSSL\n    });\n\n    const client = ldap.createClient({\n      url: url,\n      timeout: 10000,\n      connectTimeout: 10000,\n      tlsOptions: {\n        rejectUnauthorized: false // For self-signed certificates\n      }\n    });\n\n    return new Promise((resolve) => {\n      let isResolved = false;\n\n      const resolveOnce = (response: NextResponse) => {\n        if (!isResolved) {\n          isResolved = true;\n          client.destroy();\n          resolve(response);\n        }\n      };\n\n      // Set timeout\n      const timeout = setTimeout(() => {\n        resolveOnce(NextResponse.json(\n          { error: 'Bağlantı zaman aşımına uğradı' },\n          { status: 408 }\n        ));\n      }, 15000);\n\n      client.on('error', (err) => {\n        clearTimeout(timeout);\n        console.error('LDAP connection error:', err);\n        resolveOnce(NextResponse.json(\n          { error: `Bağlantı hatası: ${err.message}` },\n          { status: 500 }\n        ));\n      });\n\n      client.bind(settings.username, settings.password, (err) => {\n        clearTimeout(timeout);\n        \n        if (err) {\n          console.error('LDAP bind error:', err);\n          resolveOnce(NextResponse.json(\n            { error: `Kimlik doğrulama hatası: ${err.message}` },\n            { status: 401 }\n          ));\n        } else {\n          // Test a simple search to verify the connection works\n          const searchOptions = {\n            scope: 'base' as const,\n            filter: '(objectClass=*)',\n            attributes: ['dn']\n          };\n\n          client.search(settings.baseDN, searchOptions, (searchErr, searchRes) => {\n            if (searchErr) {\n              console.error('LDAP search error:', searchErr);\n              resolveOnce(NextResponse.json(\n                { error: `Base DN hatası: ${searchErr.message}` },\n                { status: 400 }\n              ));\n            } else {\n              let hasResults = false;\n              \n              searchRes.on('searchEntry', () => {\n                hasResults = true;\n              });\n\n              searchRes.on('error', (searchResErr) => {\n                console.error('LDAP search result error:', searchResErr);\n                resolveOnce(NextResponse.json(\n                  { error: `Arama hatası: ${searchResErr.message}` },\n                  { status: 500 }\n                ));\n              });\n\n              searchRes.on('end', () => {\n                const endTime = Date.now();\n                const latency = endTime - startTime;\n\n                resolveOnce(NextResponse.json({\n                  success: true,\n                  message: 'LDAP bağlantısı başarılı!',\n                  latency: latency,\n                  serverInfo: `${settings.server}:${settings.port}`\n                }));\n              });\n            }\n          });\n        }\n      });\n    });\n\n  } catch (error) {\n    console.error('Test connection error:', error);\n    return NextResponse.json(\n      { error: 'Bağlantı testi sırasında hata oluştu' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAWO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAyB,MAAM,QAAQ,IAAI;QACjD,MAAM,YAAY,KAAK,GAAG;QAE1B,2BAA2B;QAC3B,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,QAAQ,EAAE;YACpF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,SAAS,MAAM,GAAG,UAAU;QAC7C,MAAM,MAAM,GAAG,SAAS,GAAG,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,IAAI,EAAE;QAE/D,QAAQ,GAAG,CAAC,yBAAyB;YACnC;YACA,UAAU,SAAS,QAAQ;YAC3B,QAAQ,SAAS,MAAM;YACvB,QAAQ,SAAS,MAAM;QACzB;QAEA,MAAM,SAAS,qGAAA,CAAA,UAAI,CAAC,YAAY,CAAC;YAC/B,KAAK;YACL,SAAS;YACT,gBAAgB;YAChB,YAAY;gBACV,oBAAoB,MAAM,+BAA+B;YAC3D;QACF;QAEA,OAAO,IAAI,QAAQ,CAAC;YAClB,IAAI,aAAa;YAEjB,MAAM,cAAc,CAAC;gBACnB,IAAI,CAAC,YAAY;oBACf,aAAa;oBACb,OAAO,OAAO;oBACd,QAAQ;gBACV;YACF;YAEA,cAAc;YACd,MAAM,UAAU,WAAW;gBACzB,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;oBAAE,OAAO;gBAAgC,GACzC;oBAAE,QAAQ;gBAAI;YAElB,GAAG;YAEH,OAAO,EAAE,CAAC,SAAS,CAAC;gBAClB,aAAa;gBACb,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;oBAAE,OAAO,CAAC,iBAAiB,EAAE,IAAI,OAAO,EAAE;gBAAC,GAC3C;oBAAE,QAAQ;gBAAI;YAElB;YAEA,OAAO,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,QAAQ,EAAE,CAAC;gBACjD,aAAa;gBAEb,IAAI,KAAK;oBACP,QAAQ,KAAK,CAAC,oBAAoB;oBAClC,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;wBAAE,OAAO,CAAC,yBAAyB,EAAE,IAAI,OAAO,EAAE;oBAAC,GACnD;wBAAE,QAAQ;oBAAI;gBAElB,OAAO;oBACL,sDAAsD;oBACtD,MAAM,gBAAgB;wBACpB,OAAO;wBACP,QAAQ;wBACR,YAAY;4BAAC;yBAAK;oBACpB;oBAEA,OAAO,MAAM,CAAC,SAAS,MAAM,EAAE,eAAe,CAAC,WAAW;wBACxD,IAAI,WAAW;4BACb,QAAQ,KAAK,CAAC,sBAAsB;4BACpC,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;gCAAE,OAAO,CAAC,gBAAgB,EAAE,UAAU,OAAO,EAAE;4BAAC,GAChD;gCAAE,QAAQ;4BAAI;wBAElB,OAAO;4BACL,IAAI,aAAa;4BAEjB,UAAU,EAAE,CAAC,eAAe;gCAC1B,aAAa;4BACf;4BAEA,UAAU,EAAE,CAAC,SAAS,CAAC;gCACrB,QAAQ,KAAK,CAAC,6BAA6B;gCAC3C,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;oCAAE,OAAO,CAAC,cAAc,EAAE,aAAa,OAAO,EAAE;gCAAC,GACjD;oCAAE,QAAQ;gCAAI;4BAElB;4BAEA,UAAU,EAAE,CAAC,OAAO;gCAClB,MAAM,UAAU,KAAK,GAAG;gCACxB,MAAM,UAAU,UAAU;gCAE1B,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oCAC5B,SAAS;oCACT,SAAS;oCACT,SAAS;oCACT,YAAY,GAAG,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,IAAI,EAAE;gCACnD;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuC,GAChD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}