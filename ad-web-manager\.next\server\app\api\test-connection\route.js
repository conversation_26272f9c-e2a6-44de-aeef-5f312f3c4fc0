(()=>{var e={};e.id=255,e.ids=[255],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73010:e=>{"use strict";e.exports=require("ldapjs")},78335:()=>{},96487:()=>{},99206:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{POST:()=>p});var o=s(96559),a=s(48088),n=s(37719),u=s(32190),i=s(73010),c=s.n(i);async function p(e){try{let r=await e.json(),s=Date.now();if(!r.server||!r.baseDN||!r.username||!r.password)return u.NextResponse.json({error:"T\xfcm alanlar doldurulmalıdır"},{status:400});let t=r.useSSL?"ldaps":"ldap",o=`${t}://${r.server}:${r.port}`;console.log("LDAP Test Connection:",{url:o,username:r.username,baseDN:r.baseDN,useSSL:r.useSSL});let a=c().createClient({url:o,timeout:1e4,connectTimeout:1e4,tlsOptions:{rejectUnauthorized:!1}});return new Promise(e=>{let t=!1,o=r=>{t||(t=!0,a.destroy(),e(r))},n=setTimeout(()=>{o(u.NextResponse.json({error:"Bağlantı zaman aşımına uğradı"},{status:408}))},15e3);a.on("error",e=>{clearTimeout(n),console.error("LDAP connection error:",e),o(u.NextResponse.json({error:`Bağlantı hatası: ${e.message}`},{status:500}))}),a.bind(r.username,r.password,e=>{clearTimeout(n),e?(console.error("LDAP bind error:",e),o(u.NextResponse.json({error:`Kimlik doğrulama hatası: ${e.message}`},{status:401}))):a.search(r.baseDN,{scope:"base",filter:"(objectClass=*)",attributes:["dn"]},(e,t)=>{e?(console.error("LDAP search error:",e),o(u.NextResponse.json({error:`Base DN hatası: ${e.message}`},{status:400}))):(t.on("searchEntry",()=>{}),t.on("error",e=>{console.error("LDAP search result error:",e),o(u.NextResponse.json({error:`Arama hatası: ${e.message}`},{status:500}))}),t.on("end",()=>{let e=Date.now();o(u.NextResponse.json({success:!0,message:"LDAP bağlantısı başarılı!",latency:e-s,serverInfo:`${r.server}:${r.port}`}))}))})})})}catch(e){return console.error("Test connection error:",e),u.NextResponse.json({error:"Bağlantı testi sırasında hata oluştu"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/test-connection/route",pathname:"/api/test-connection",filename:"route",bundlePath:"app/api/test-connection/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\test-connection\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:x}=l;function h(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580],()=>s(99206));module.exports=t})();