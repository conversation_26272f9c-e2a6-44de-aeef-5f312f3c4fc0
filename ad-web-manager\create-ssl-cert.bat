@echo off
title SSL Sertifika Olusturucu
color 0B

echo.
echo ========================================
echo    SSL Sertifikasi Olusturuluyor
echo ========================================
echo.

REM OpenSSL kontrolu
where openssl >nul 2>&1
if errorlevel 1 (
    echo HATA: OpenSSL bulunamadi!
    echo.
    echo OpenSSL yuklemek icin:
    echo 1. https://slproweb.com/products/Win32OpenSSL.html adresine gidin
    echo 2. "Win64 OpenSSL v3.x.x" indirin ve yukleyin
    echo 3. PATH'e ekleyin: C:\Program Files\OpenSSL-Win64\bin
    echo.
    pause
    exit /b 1
)

echo [1/3] SSL sertifikasi olusturuluyor...

REM SSL dizini olustur
if not exist "ssl" mkdir ssl

REM Private key olustur
openssl genrsa -out ssl/localhost.key 2048

REM Certificate signing request olustur
openssl req -new -key ssl/localhost.key -out ssl/localhost.csr -subj "/C=TR/ST=Istanbul/L=Istanbul/O=Bayraktar Holding/OU=IT/CN=localhost"

REM Self-signed certificate olustur
openssl x509 -req -in ssl/localhost.csr -signkey ssl/localhost.key -out ssl/localhost.crt -days 365 -extensions v3_req -extfile ssl.conf

echo.
echo [2/3] SSL konfigurasyonu olusturuluyor...

REM SSL config dosyasi olustur
echo [req]> ssl.conf
echo distinguished_name = req_distinguished_name>> ssl.conf
echo req_extensions = v3_req>> ssl.conf
echo.>> ssl.conf
echo [req_distinguished_name]>> ssl.conf
echo.>> ssl.conf
echo [v3_req]>> ssl.conf
echo keyUsage = keyEncipherment, dataEncipherment>> ssl.conf
echo extendedKeyUsage = serverAuth>> ssl.conf
echo subjectAltName = @alt_names>> ssl.conf
echo.>> ssl.conf
echo [alt_names]>> ssl.conf
echo DNS.1 = localhost>> ssl.conf
echo DNS.2 = 127.0.0.1>> ssl.conf
echo IP.1 = 127.0.0.1>> ssl.conf
echo IP.2 = ::1>> ssl.conf

REM Yeniden certificate olustur
openssl x509 -req -in ssl/localhost.csr -signkey ssl/localhost.key -out ssl/localhost.crt -days 365 -extensions v3_req -extfile ssl.conf

echo.
echo [3/3] HTTPS server script'i olusturuluyor...

echo.
echo ========================================
echo   SSL Sertifikasi Basariyla Olusturuldu
echo ========================================
echo.
echo Dosyalar:
echo - ssl/localhost.key (Private Key)
echo - ssl/localhost.crt (Certificate)
echo - ssl/localhost.csr (Certificate Request)
echo.
echo HTTPS server baslatmak icin:
echo start-https-server.bat calistirin
echo.
pause
