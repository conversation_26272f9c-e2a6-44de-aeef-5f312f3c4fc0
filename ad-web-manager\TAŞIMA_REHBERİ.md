# AD Web Manager - <PERSON><PERSON> Rehberi

## 📋 <PERSON><PERSON>k Kontrol Listesi

### ✅ Sistem Gereksinimleri
- [ ] Windows Server 2016+ veya Windows 10+
- [ ] Node.js 18.0+ kurulu
- [ ] npm 9.0+ kurulu
- [ ] Minimum 2GB RAM
- [ ] Minimum 1GB disk alanı
- [ ] Administrator yetkisi

### ✅ Network Gereksinimleri
- [ ] Port 80 erişimi (HTTP)
- [ ] Port 3000 erişimi (Development)
- [ ] LDAP Server erişimi (**********:389)
- [ ] Internet bağlantısı (npm için)

## 🚀 Kurulum Adımları

### 1. Proje Do<PERSON>alarını Kopyalama
```bash
# Tüm proje klasörünü yeni sunucuya kopyalayın:
ad-web-manager/
├── src/
├── public/
├── package.json
├── .env.production
├── start-domain.bat
├── start-development.bat
└── install-new-server.bat
```

### 2. Otomati<PERSON>
```bash
# Administrator o<PERSON><PERSON> Command Prompt açın
cd PROJE_KLA<PERSON>ÖRÜ
install-new-server.bat
```

### 3. <PERSON> (Alternatif)
```bash
# 1. Bağımlılıkları yükleyin:
npm install

# 2. Production build yapın:
npm run build

# 3. Environment dosyasını düzenleyin:
# .env.production → NEXTAUTH_URL ve DOMAIN_NAME
```

## ⚙️ Konfigürasyon

### Environment Ayarları (.env.production)
```bash
NEXTAUTH_SECRET=bayraktar-ad-web-manager-2024-production-key
NEXTAUTH_URL=http://YENİ_DOMAIN_ADI
DOMAIN_NAME=YENİ_DOMAIN_ADI
PORT=80
```

### DNS Ayarları
```bash
A Record: YENİ_DOMAIN → SUNUCU_IP
CNAME: www.YENİ_DOMAIN → YENİ_DOMAIN
```

### Firewall Ayarları
```bash
# Windows Firewall:
- Port 80: Inbound Rule → Allow
- Port 3000: Inbound Rule → Allow (Development için)

# Network Firewall:
- Port 80 → Yeni sunucu IP
- Port 389 → LDAP erişimi
```

## 🚀 Server Başlatma

### Domain Server (Port 80)
```bash
# Administrator olarak:
start-domain.bat

# Veya manuel:
npm run start:production
```

### Development Server (Port 3000)
```bash
start-development.bat

# Veya manuel:
npm run dev
```

## 🔍 Test Listesi

### ✅ Kurulum Testi
- [ ] Node.js versiyonu: `node --version`
- [ ] npm versiyonu: `npm --version`
- [ ] Bağımlılıklar yüklendi: `node_modules` klasörü var
- [ ] Build tamamlandı: `.next` klasörü var

### ✅ Server Testi
- [ ] Port 80 çalışıyor: `http://localhost`
- [ ] Login sayfası açılıyor: `http://localhost/login`
- [ ] Logo görünüyor
- [ ] LDAP bağlantısı çalışıyor

### ✅ Domain Testi (DNS sonrası)
- [ ] Domain erişimi: `http://YENİ_DOMAIN`
- [ ] Login çalışıyor
- [ ] Tüm özellikler aktif

## 🆘 Sorun Giderme

### Node.js Bulunamıyor
```bash
# Çözüm:
1. https://nodejs.org → LTS versiyonu indirin
2. Kurulum sırasında "Add to PATH" seçin
3. Command Prompt'u yeniden açın
```

### Port 80 Erişim Hatası
```bash
# Çözüm:
1. Administrator olarak çalıştırın
2. Windows Firewall'da Port 80'i açın
3. Başka servis Port 80 kullanıyor mu kontrol edin
```

### LDAP Bağlantı Hatası
```bash
# Çözüm:
1. Network bağlantısını kontrol edin
2. LDAP Server (**********:389) erişimini test edin
3. Firewall'da Port 389'u açın
```

### Build Hatası
```bash
# Çözüm:
1. node_modules klasörünü silin
2. npm install tekrar çalıştırın
3. npm run build tekrar deneyin
```

## 📞 Destek

Sorun yaşadığınızda:
1. Bu rehberi kontrol edin
2. Log dosyalarını inceleyin
3. IT ekibiyle iletişime geçin

## 📝 Notlar

- Kurulum yaklaşık 10-15 dakika sürer
- İlk build 2-3 dakika sürebilir
- LDAP bağlantısı network'e bağlıdır
- Domain değişikliği sonrası logout/login gerekebilir
