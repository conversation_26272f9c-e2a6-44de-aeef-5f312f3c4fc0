(()=>{var e={};e.id=263,e.ids=[263],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5792:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GET:()=>a,POST:()=>a,authOptions:()=>u});var t=s(19854),n=s.n(t),i=s(13581),o=s(12909);(0,o.lK)();let u={providers:[(0,i.A)({name:"credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;let r=(0,o.JE)(e.username);return r&&(0,o.Oj)(e.password,r.password)?((0,o.bx)(r.id),{id:r.id,name:r.username,email:r.username,role:r.role,permissions:r.permissions}):null}})],callbacks:{jwt:async({token:e,user:r,trigger:s,session:t})=>(r&&(e.role=r.role,e.permissions=r.permissions),"update"===s&&t&&(e.role=t.role,e.permissions=t.permissions),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.permissions=r.permissions),e)},pages:{signIn:"/login"},callbacks:{jwt:async({token:e,user:r,trigger:s,session:t})=>(r&&(e.role=r.role,e.permissions=r.permissions),"update"===s&&t&&(e.role=t.role,e.permissions=t.permissions),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.permissions=r.permissions),e),redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:e.startsWith(r)?e:`${r}/login`},session:{strategy:"jwt"},secret:process.env.NEXTAUTH_SECRET||"your-secret-key-here"},a=n()(u)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{JE:()=>c,Oj:()=>f,TK:()=>y,bx:()=>w,hG:()=>g,kg:()=>h,kl:()=>m,lK:()=>l,lo:()=>p});var t=s(85663),n=s(29021),i=s.n(n),o=s(33873);let u=s.n(o)().join(process.cwd(),"users.json"),a={id:"1",username:"admin",password:"$2b$10$t5.K7vyPesCG5IR5F5TkUuSSM/QNlar8Wr243/uQuG0yyrw1mJbBK",role:"admin",permissions:{viewUsers:!0,unlockUsers:!0,manageSettings:!0,manageUsers:!0},createdAt:new Date().toISOString()};function l(){i().existsSync(u)||i().writeFileSync(u,JSON.stringify([a],null,2))}function p(){try{i().existsSync(u)||l();let e=i().readFileSync(u,"utf8");return JSON.parse(e)}catch(e){return console.error("Error reading users file:",e),[a]}}function d(e){try{i().writeFileSync(u,JSON.stringify(e,null,2))}catch(e){throw console.error("Error saving users file:",e),Error("Failed to save users")}}function c(e){return p().find(r=>r.username===e)||null}function m(e){return p().find(r=>r.id===e)||null}function f(e,r){return t.Ay.compareSync(e,r)}function x(e){return t.Ay.hashSync(e,10)}function h(e){let r=p();if(r.find(r=>r.username===e.username))throw Error("Username already exists");let s={...e,id:Date.now().toString(),password:x(e.password),createdAt:new Date().toISOString()};return r.push(s),d(r),s}function y(e,r){let s=p(),t=s.findIndex(r=>r.id===e);if(-1===t)throw Error("User not found");return r.password&&(r.password=x(r.password)),s[t]={...s[t],...r},d(s),s[t]}function g(e){let r=p(),s=r.findIndex(r=>r.id===e);if(-1===s)return!1;if("admin"===r[s].username)throw Error("Cannot delete default admin user");return r.splice(s,1),d(r),!0}function w(e){let r=p(),s=r.findIndex(r=>r.id===e);-1!==s&&(r[s].lastLogin=new Date().toISOString(),d(r))}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38719:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var t={};s.r(t),s.d(t,{POST:()=>d});var n=s(96559),i=s(48088),o=s(37719),u=s(32190),a=s(19854),l=s(12909),p=s(5792);async function d(){try{console.log("\uD83D\uDD04 Refresh session API called");let e=await (0,a.getServerSession)(p.authOptions);if(!e?.user?.id)return console.log("❌ No session or user ID found"),u.NextResponse.json({error:"Unauthorized"},{status:401});console.log("\uD83D\uDC64 Current session user ID:",e.user.id);let r=(0,l.kl)(e.user.id);if(!r)return console.log("❌ User not found in database"),u.NextResponse.json({error:"User not found"},{status:404});return console.log("✅ Updated user data:",{id:r.id,username:r.username,role:r.role,permissions:r.permissions}),u.NextResponse.json({user:{id:r.id,name:r.username,username:r.username,role:r.role,permissions:r.permissions}})}catch(e){return console.error("❌ Error refreshing session:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/refresh-session/route",pathname:"/api/auth/refresh-session",filename:"route",bundlePath:"app/api/auth/refresh-session/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\auth\\refresh-session\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:x}=c;function h(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580,854,427],()=>s(38719));module.exports=t})();