'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Save, TestTube, CheckCircle, XCircle, LogOut, Wifi, WifiOff, Clock } from 'lucide-react';

interface LdapSettings {
  server: string;
  port: string;
  baseDN: string;
  username: string;
  password: string;
  useSSL: boolean;
}

export default function Settings() {
  const { data: session, update: updateSession } = useSession();

  if (!session) {
    return null;
  }

  const [settings, setSettings] = useState<LdapSettings>({
    server: '',
    port: '389',
    baseDN: '',
    username: '',
    password: '',
    useSSL: false
  });

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'testing' | 'unknown'>('unknown');
  const [connectionLatency, setConnectionLatency] = useState<number | null>(null);
  const [lastConnectionTest, setLastConnectionTest] = useState<Date | null>(null);
  const [serverInfo, setServerInfo] = useState<string>('');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      console.log('Loading settings...');
      // Gerçek şifreyi almak için includePassword=true parametresi ekle
      const response = await fetch('/api/settings?includePassword=true');
      if (response.ok) {
        const data = await response.json();
        console.log('Settings loaded:', data);
        setSettings(data);

        // Settings yüklendikten hemen sonra bağlantı testini yap
        if (data.server && data.username && data.password) {
          console.log('Testing connection with loaded settings...');
          setTimeout(() => testConnectionWithData(data), 500);
        }
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const testConnectionWithData = async (settingsData: any) => {
    // Eğer gerekli ayarlar yoksa test yapma
    if (!settingsData.server || !settingsData.username || !settingsData.password) {
      setConnectionStatus('unknown');
      return;
    }

    try {
      console.log('Testing connection with data:', settingsData);
      const response = await fetch('/api/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsData),
      });

      const result = await response.json();
      console.log('Connection test result:', result);

      if (response.ok && result.success) {
        setConnectionStatus('connected');
        setServerInfo(result.serverInfo || '');
        setConnectionLatency(result.latency || null);
      } else {
        setConnectionStatus('disconnected');
        console.log('Connection test failed:', result.error);
      }
    } catch (error) {
      setConnectionStatus('disconnected');
      console.error('Connection test error:', error);
    }
  };

  const testConnectionStatus = async () => {
    return testConnectionWithData(settings);
  };

  const saveSettings = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage('Ayarlar başarıyla kaydedildi!');
        setMessageType('success');
        await updateSession();
      } else {
        setMessage(result.error || 'Ayarlar kaydedilirken hata oluştu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Bağlantı hatası!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const testConnection = async () => {
    setIsLoading(true);
    setMessage('');
    setConnectionStatus('testing');

    const startTime = Date.now();

    try {
      const response = await fetch('/api/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      const endTime = Date.now();
      const latency = endTime - startTime;
      const result = await response.json();

      if (response.ok && result.success) {
        setMessage('LDAP bağlantısı başarılı!');
        setMessageType('success');
        setConnectionStatus('connected');
        setConnectionLatency(latency);
        setServerInfo(result.serverInfo || '');
      } else {
        setMessage(result.error || 'LDAP bağlantısı başarısız!');
        setMessageType('error');
        setConnectionStatus('disconnected');
        setConnectionLatency(null);
      }
    } catch (error) {
      setMessage('Bağlantı testi sırasında hata oluştu!');
      setMessageType('error');
      setConnectionStatus('disconnected');
      setConnectionLatency(null);
    } finally {
      setIsLoading(false);
      setLastConnectionTest(new Date());
    }
  };

  const handleLogout = () => {
    signOut({ callbackUrl: '/login' });
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Wifi className="h-5 w-5 text-green-600" />;
      case 'disconnected':
        return <WifiOff className="h-5 w-5 text-red-600" />;
      case 'testing':
        return <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Bağlı';
      case 'disconnected':
        return 'Bağlantısız';
      case 'testing':
        return 'Test Ediliyor...';
      default:
        return 'Bilinmiyor';
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-600';
      case 'disconnected':
        return 'text-red-600';
      case 'testing':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <nav className="border-b bg-card">
        <div className="container mx-auto px-4">
          <div className="flex h-24 items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/Bayraktar Holding Logo.png"
                alt="Bayraktar Holding Logo"
                width={160}
                height={160}
                className="rounded-md"
              />
              <span className="text-xl font-bold text-primary">AD Web Manager</span>
            </Link>
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-sm font-medium text-foreground hover:text-primary">
                Dashboard
              </Link>
              <Link href="/users" className="text-sm font-medium text-foreground hover:text-primary">
                Locked Users
              </Link>
              <Link href="/password-expiry" className="text-sm font-medium text-foreground hover:text-primary">
                Password Expiry
              </Link>
              {session.user.permissions.manageSettings && (
                <Link href="/settings" className="text-sm font-medium text-primary">
                  Settings
                </Link>
              )}
              {session.user.permissions.manageUsers && (
                <Link href="/manage-users" className="text-sm font-medium text-foreground hover:text-primary">
                  Manage Users
                </Link>
              )}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  {session.user.name} ({session.user.role})
                </span>
                <Button variant="ghost" size="sm" onClick={handleLogout}>
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              LDAP Ayarları
            </h1>
            <p className="text-xl text-muted-foreground">
              Active Directory bağlantı ayarlarını yapılandırın
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {getConnectionStatusIcon()}
                <span>LDAP Bağlantı Durumu</span>
              </CardTitle>
              <CardDescription>
                Mevcut LDAP bağlantısının durumu ve performans bilgileri
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold mb-2">
                    <span className={getConnectionStatusColor()}>
                      {getConnectionStatusText()}
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">Durum</div>
                </div>
                
                {connectionLatency !== null && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-2">
                      {connectionLatency}ms
                    </div>
                    <div className="text-sm text-muted-foreground">Gecikme</div>
                  </div>
                )}
                
                {lastConnectionTest && (
                  <div className="text-center">
                    <div className="text-lg font-medium text-gray-600 mb-2">
                      {lastConnectionTest.toLocaleTimeString('tr-TR')}
                    </div>
                    <div className="text-sm text-muted-foreground">Son Test</div>
                  </div>
                )}
              </div>
              
              {serverInfo && (
                <div className="mt-4 p-3 bg-muted rounded-lg">
                  <div className="text-sm text-muted-foreground">Sunucu Bilgisi:</div>
                  <div className="text-sm font-mono">{serverInfo}</div>
                </div>
              )}
            </CardContent>
          </Card>

          {message && (
            <Alert className={messageType === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              {messageType === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className={messageType === 'success' ? 'text-green-800' : 'text-red-800'}>
                {message}
              </AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle>LDAP Ayarları</CardTitle>
              <CardDescription>
                Active Directory sunucu bağlantı bilgilerini girin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="server">LDAP Sunucu</Label>
                  <Input
                    id="server"
                    placeholder="ldap.example.com"
                    value={settings.server}
                    onChange={(e) => setSettings({ ...settings, server: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="port">Port</Label>
                  <Input
                    id="port"
                    placeholder="389"
                    value={settings.port}
                    onChange={(e) => setSettings({ ...settings, port: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="baseDN">Base DN</Label>
                <Input
                  id="baseDN"
                  placeholder="DC=example,DC=com"
                  value={settings.baseDN}
                  onChange={(e) => setSettings({ ...settings, baseDN: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="username">Kullanıcı Adı</Label>
                <Input
                  id="username"
                  placeholder="<EMAIL>"
                  value={settings.username}
                  onChange={(e) => setSettings({ ...settings, username: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Şifre</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={settings.password}
                  onChange={(e) => setSettings({ ...settings, password: e.target.value })}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="useSSL"
                  checked={settings.useSSL}
                  onCheckedChange={(checked) => setSettings({ ...settings, useSSL: checked as boolean })}
                />
                <Label htmlFor="useSSL">SSL/TLS Kullan</Label>
              </div>

              <div className="flex space-x-4">
                <Button onClick={saveSettings} disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Kaydediliyor...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Kaydet
                    </>
                  )}
                </Button>
                
                <Button variant="outline" onClick={testConnection} disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Test Ediliyor...
                    </>
                  ) : (
                    <>
                      <TestTube className="mr-2 h-4 w-4" />
                      Bağlantıyı Test Et
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
