(()=>{var e={};e.id=890,e.ids=[890],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6211:(e,s,a)=>{"use strict";a.d(s,{A0:()=>l,BF:()=>i,Hj:()=>d,XI:()=>n,nA:()=>o,nd:()=>c});var r=a(60687);a(43210);var t=a(4780);function n({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,t.cn)("w-full caption-bottom text-sm",e),...s})})}function l({className:e,...s}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,t.cn)("[&_tr]:border-b",e),...s})}function i({className:e,...s}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,t.cn)("[&_tr:last-child]:border-0",e),...s})}function d({className:e,...s}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,t.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...s})}function c({className:e,...s}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,t.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s})}function o({className:e,...s}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,t.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24486:(e,s,a)=>{"use strict";a.d(s,{E$:()=>x,bL:()=>o});var r=a(60687),t=a(43210),n=a(5336),l=a(35071),i=a(11860),d=a(4780);function c({id:e,title:s,description:a,variant:c,duration:o=8e3,onClose:x}){let[m,u]=(0,t.useState)(!1),[p,h]=(0,t.useState)(!1);return(0,r.jsx)("div",{className:(0,d.cn)("fixed bottom-4 right-4 z-50 w-96 max-w-sm p-4 rounded-lg shadow-lg border transition-all duration-300 transform",{"bg-green-50 border-green-200 text-green-800":"success"===c,"bg-red-50 border-red-200 text-red-800":"error"===c,"translate-x-full opacity-0":!m,"translate-x-0 opacity-100":m&&!p,"translate-x-full opacity-0":p}),children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:"success"===c?(0,r.jsx)(n.A,{className:"h-5 w-5 text-green-600"}):(0,r.jsx)(l.A,{className:"h-5 w-5 text-red-600"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-semibold",children:s}),(0,r.jsx)("p",{className:"text-sm mt-1 whitespace-pre-line",children:a})]}),(0,r.jsx)("button",{onClick:()=>{h(!0),setTimeout(()=>{x(e)},300)},className:"flex-shrink-0 ml-2 p-1 rounded-md hover:bg-black/5 transition-colors",children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})]})})}function o({notifications:e,onRemove:s}){return(0,r.jsx)("div",{className:"fixed bottom-0 right-0 z-50 p-4 space-y-2",children:e.map(e=>(0,r.jsx)(c,{...e,onClose:s},e.id))})}function x(){let[e,s]=(0,t.useState)([]);return{notifications:e,addNotification:e=>{let a=Math.random().toString(36).substr(2,9);s(s=>[...s,{...e,id:a}])},removeNotification:e=>{s(s=>s.filter(s=>s.id!==e))}}}},27352:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\\\PC\\\\Masa\xfcst\xfc\\\\AD_Web\\\\ad-web-manager\\\\src\\\\app\\\\password-expiry\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\password-expiry\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},41862:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},43649:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},48730:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},54300:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var r=a(60687),t=a(43210),n=a(14163),l=t.forwardRef((e,s)=>(0,r.jsx)(n.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var i=a(4780);function d({className:e,...s}){return(0,r.jsx)(l,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},61237:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=a(65239),t=a(48088),n=a(88170),l=a.n(n),i=a(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(s,d);let c={children:["",{children:["password-expiry",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,27352)),"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\password-expiry\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\password-expiry\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/password-expiry/page",pathname:"/password-expiry",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78122:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},89667:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var r=a(60687);a(43210);var t=a(4780);function n({className:e,type:s,...a}){return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},91153:(e,s,a)=>{Promise.resolve().then(a.bind(a,95315))},91821:(e,s,a)=>{"use strict";a.d(s,{Fc:()=>i,TN:()=>d});var r=a(60687);a(43210);var t=a(24224),n=a(4780);let l=(0,t.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i({className:e,variant:s,...a}){return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(l({variant:s}),e),...a})}function d({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...s})}},91825:(e,s,a)=>{Promise.resolve().then(a.bind(a,27352))},95315:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var r=a(60687),t=a(43210),n=a(82136),l=a(44493),i=a(29523),d=a(91821),c=a(6211),o=a(24486),x=a(24236),m=a(35879),u=a(9263),p=a(89667),h=a(54300),j=a(43649),f=a(78122),b=a(48730),g=a(53094),y=a(41862),v=a(62688);let N=(0,v.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var w=a(35071);let k=(0,v.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),A=(0,v.A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);function S(){let{data:e}=(0,n.useSession)(),s=(0,x.X)(),{notifications:a,addNotification:v,removeNotification:S}=(0,o.E$)();if(!e)return null;let[M,D]=(0,t.useState)([]),[T,R]=(0,t.useState)({totalExpiredUsers:0,expiredToday:0,expiredThisWeek:0,expiredThisMonth:0}),[E,B]=(0,t.useState)(!1),[$,P]=(0,t.useState)(null),[U,z]=(0,t.useState)(null),[C,_]=(0,t.useState)(""),[H,I]=(0,t.useState)(null),[L,G]=(0,t.useState)(!1),[K,W]=(0,t.useState)(""),Z=async()=>{B(!0);try{let e=await fetch("/api/password-expiry"),s=await e.json();e.ok?(D(s.users||[]),R(s.stats||{totalExpiredUsers:0,expiredToday:0,expiredThisWeek:0,expiredThisMonth:0}),z(new Date)):v({title:"❌ Y\xfckleme Hatası",description:s.error||"Şifre s\xfcresi dolan kullanıcılar y\xfcklenirken hata oluştu!",variant:"error",duration:8e3})}catch(e){v({title:"❌ Bağlantı Hatası",description:"Sunucuya bağlanırken hata oluştu!",variant:"error",duration:8e3})}finally{B(!1)}},F=async()=>{if(!C.trim())return void W("Kullanıcı adı gerekli");G(!0),W(""),I(null);try{let e=await fetch(`/api/search-user?username=${encodeURIComponent(C.trim())}`),s=await e.json();e.ok?(I(s.user),W("")):(W(s.error||"Kullanıcı bulunamadı"),I(null))}catch(e){W("Arama sırasında hata oluştu"),I(null)}finally{G(!1)}},Y=async e=>{P(e);try{let s=await fetch("/api/extend-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e})}),a=await s.json();if(s.ok){let s=a.extendedAt?new Date(a.extendedAt):new Date,r=a.newExpiryDate?new Date(a.newExpiryDate):null;a.daysExtended;let t=`✅ ${e} kullanıcısının şifre s\xfcresi başarıyla uzatıldı!`;a.unlocked&&(t+=`
🔓 Kullanıcı hesabı unlock edildi!`),r&&(t+=`
📅 Yeni şifre son kullanma tarihi: ${r.toLocaleDateString("tr-TR")}`),t+=`
⏰ İşlem zamanı: ${s.toLocaleString("tr-TR")}`,v({title:"✅ İşlem Başarılı",description:`${e} kullanıcısının şifre s\xfcresi başarıyla uzatıldı${a.unlocked?" ve hesap unlock edildi":""}.`,variant:"success",duration:8e3}),H&&H.username===e&&(I(null),_("")),setTimeout(()=>{Z()},1e3)}else{let e=a.error||"Şifre s\xfcresi uzatılırken hata oluştu!";v({title:"❌ İşlem Başarısız",description:e,variant:"error",duration:8e3})}}catch(e){console.error("Frontend extend password error:",e),v({title:"❌ Bağlantı Hatası",description:`Bağlantı hatası: ${e instanceof Error?e.message:"Bilinmeyen hata"}`,variant:"error",duration:8e3})}finally{P(null)}},q=e=>{try{return new Date(e).toLocaleString("tr-TR")}catch{return e}},O=e=>e<=7?"text-red-600":e<=30?"text-orange-600":"text-gray-600";return e.user.permissions.unlockUsers?(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[s.isMobile?(0,r.jsx)(m.c,{currentPath:"/password-expiry"}):(0,r.jsx)(u.u,{currentPath:"/password-expiry"}),(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:`${s.isMobile?"space-y-4":"flex items-center justify-between"}`,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:`${s.isMobile?"text-2xl":"text-3xl"} font-bold text-foreground`,children:"Password Expiry Management"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Şifre s\xfcresi dolan kullanıcıları g\xf6r\xfcnt\xfcleyin ve şifre s\xfcrelerini uzatın"})]}),(0,r.jsxs)("div",{className:`${s.isMobile?"flex flex-col space-y-2":"flex items-center space-x-4"}`,children:[U&&(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Son g\xfcncelleme: ",q(U.toISOString())]}),(0,r.jsxs)(i.$,{onClick:Z,disabled:E,className:s.isMobile?"w-full":"",children:[(0,r.jsx)(f.A,{className:`mr-2 h-4 w-4 ${E?"animate-spin":""}`}),"Yenile"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)(l.Zp,{className:"border-red-200",children:[(0,r.jsx)(l.aR,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,r.jsx)(j.A,{className:"h-6 w-6 text-red-600"}),(0,r.jsx)(l.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Toplam S\xfcresi Dolan"})]})}),(0,r.jsx)(l.Wu,{className:"text-center",children:(0,r.jsx)("div",{className:"text-4xl font-bold text-red-600",children:T.totalExpiredUsers})})]}),(0,r.jsxs)(l.Zp,{className:"border-orange-200",children:[(0,r.jsx)(l.aR,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,r.jsx)(b.A,{className:"h-6 w-6 text-orange-600"}),(0,r.jsx)(l.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Bug\xfcn Dolan"})]})}),(0,r.jsx)(l.Wu,{className:"text-center",children:(0,r.jsx)("div",{className:"text-4xl font-bold text-orange-600",children:T.expiredToday})})]}),(0,r.jsxs)(l.Zp,{className:"border-yellow-200",children:[(0,r.jsx)(l.aR,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,r.jsx)(b.A,{className:"h-6 w-6 text-yellow-600"}),(0,r.jsx)(l.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Bu Hafta Dolan"})]})}),(0,r.jsx)(l.Wu,{className:"text-center",children:(0,r.jsx)("div",{className:"text-4xl font-bold text-yellow-600",children:T.expiredThisWeek})})]}),(0,r.jsxs)(l.Zp,{className:"border-blue-200",children:[(0,r.jsx)(l.aR,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,r.jsx)(g.A,{className:"h-6 w-6 text-blue-600"}),(0,r.jsx)(l.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Bu Ay Dolan"})]})}),(0,r.jsx)(l.Wu,{className:"text-center",children:(0,r.jsx)("div",{className:"text-4xl font-bold text-blue-600",children:T.expiredThisMonth})})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{className:"text-xl",children:"Kullanıcı Arama"}),(0,r.jsx)(l.BT,{children:"Herhangi bir kullanıcının şifre s\xfcresini uzatmak i\xe7in kullanıcı adını arayın"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(h.J,{htmlFor:"search-username",children:"Kullanıcı Adı"}),(0,r.jsx)(p.p,{id:"search-username",placeholder:"Kullanıcı adını girin...",value:C,onChange:e=>_(e.target.value),onKeyPress:e=>"Enter"===e.key&&F()})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)(i.$,{onClick:F,disabled:L,children:L?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Aranıyor..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(N,{className:"mr-2 h-4 w-4"}),"Ara"]})})})]}),K&&(0,r.jsxs)(d.Fc,{className:"border-red-200 bg-red-50",children:[(0,r.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,r.jsx)(d.TN,{className:"text-red-800",children:K})]}),H&&(0,r.jsx)(l.Zp,{className:"border-blue-200 bg-blue-50",children:(0,r.jsx)(l.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:`${s.isMobile?"space-y-4":"flex items-center justify-between"}`,children:[(0,r.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(k,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("span",{className:"font-medium text-blue-900",children:H.displayName}),(0,r.jsxs)("span",{className:"text-sm text-blue-700",children:["(",H.username,")"]})]}),(0,r.jsxs)("div",{className:`grid ${s.isMobile?"grid-cols-1 gap-2":"grid-cols-1 md:grid-cols-3 gap-4"} text-sm`,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-blue-700",children:"Durum: "}),(0,r.jsx)("span",{className:`font-medium ${H.accountDisabled?"text-red-600":"text-green-600"}`,children:H.accountDisabled?"Devre Dışı":"Aktif"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-blue-700",children:"Şifre: "}),(0,r.jsx)("span",{className:`font-medium ${H.passwordNeverExpires?"text-green-600":"text-blue-600"}`,children:H.passwordNeverExpires?"Hi\xe7 Dolmaz":"S\xfcreli"})]}),void 0!==H.daysUntilExpiry&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-blue-700",children:"Kalan S\xfcre: "}),(0,r.jsx)("span",{className:`font-medium ${H.daysUntilExpiry<0?"text-red-600":H.daysUntilExpiry<=7?"text-orange-600":"text-green-600"}`,children:H.daysUntilExpiry<0?`${Math.abs(H.daysUntilExpiry)} g\xfcn \xf6nce doldu`:`${H.daysUntilExpiry} g\xfcn`})]})]}),H.lastLogon&&(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("span",{className:"text-blue-700",children:"Son Giriş: "}),(0,r.jsx)("span",{className:"text-blue-600",children:q(H.lastLogon)})]})]}),(0,r.jsx)("div",{className:s.isMobile?"w-full":"",children:!H.accountDisabled&&(0,r.jsx)(i.$,{onClick:()=>Y(H.username),disabled:$===H.username,className:`bg-blue-600 hover:bg-blue-700 ${s.isMobile?"w-full":""}`,children:$===H.username?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uzatılıyor & Unlock..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"S\xfcre Uzat & Unlock"]})})})]})})})]})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{className:"text-xl",children:"Şifre S\xfcresi Dolan Kullanıcılar"}),(0,r.jsx)(l.BT,{children:"Şifre s\xfcresi dolan kullanıcıları g\xf6r\xfcnt\xfcleyin ve şifre s\xfcrelerini uzatın"})]}),(0,r.jsx)(l.Wu,{children:E?(0,r.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,r.jsx)(y.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,r.jsx)("span",{className:"ml-2",children:"Y\xfckleniyor..."})]}):0===M.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(A,{className:"mx-auto h-12 w-12 text-green-500 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Şifre S\xfcresi Dolan Kullanıcı Yok"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"T\xfcm kullanıcıların şifre s\xfcreleri ge\xe7erli."})]}):s.isMobile?(0,r.jsx)("div",{className:"space-y-3",children:M.map(e=>(0,r.jsxs)(l.Zp,{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg text-foreground",children:e.username}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.displayName})]}),(0,r.jsxs)("div",{className:`px-2 py-1 rounded-full text-xs font-medium ${O(e.daysSinceExpiry)} bg-opacity-20`,children:[e.daysSinceExpiry," g\xfcn ge\xe7ti"]})]}),(0,r.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Şifre Bitiş:"}),(0,r.jsx)("span",{className:"font-medium text-red-600",children:q(e.passwordExpires)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Son Giriş:"}),(0,r.jsx)("span",{className:"font-medium",children:e.lastLogon?q(e.lastLogon):"Hi\xe7 giriş yapmamış"})]})]}),(0,r.jsx)(i.$,{className:"w-full bg-blue-600 hover:bg-blue-700",onClick:()=>Y(e.username),disabled:$===e.username,children:$===e.username?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uzatılıyor & Unlock..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"S\xfcre Uzat & Unlock"]})})]},e.username))}):(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{children:(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nd,{children:"Kullanıcı Adı"}),(0,r.jsx)(c.nd,{children:"Tam Ad"}),(0,r.jsx)(c.nd,{children:"Şifre Bitiş Tarihi"}),(0,r.jsx)(c.nd,{children:"Ge\xe7en G\xfcn"}),(0,r.jsx)(c.nd,{children:"Son Giriş"}),(0,r.jsx)(c.nd,{children:"İşlemler"})]})}),(0,r.jsx)(c.BF,{children:M.map(e=>(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nA,{className:"font-medium",children:e.username}),(0,r.jsx)(c.nA,{children:e.displayName}),(0,r.jsx)(c.nA,{children:q(e.passwordExpires)}),(0,r.jsx)(c.nA,{children:(0,r.jsxs)("span",{className:`font-medium ${O(e.daysSinceExpiry)}`,children:[e.daysSinceExpiry," g\xfcn"]})}),(0,r.jsx)(c.nA,{children:e.lastLogon?q(e.lastLogon):"Hi\xe7"}),(0,r.jsx)(c.nA,{children:(0,r.jsx)(i.$,{size:"sm",onClick:()=>Y(e.username),disabled:$===e.username,className:"bg-blue-600 hover:bg-blue-700",children:$===e.username?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uzatılıyor & Unlock..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"S\xfcre Uzat & Unlock"]})})})]},e.username))})]})})})]})]})}),(0,r.jsx)(o.bL,{notifications:a,onRemove:S})]}):(0,r.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center",children:(0,r.jsxs)(l.Zp,{className:"w-full max-w-md",children:[(0,r.jsxs)(l.aR,{className:"text-center",children:[(0,r.jsx)(j.A,{className:"mx-auto h-12 w-12 text-orange-500 mb-4"}),(0,r.jsx)(l.ZB,{children:"Erişim Reddedildi"}),(0,r.jsx)(l.BT,{children:"Bu sayfaya erişim yetkiniz bulunmamaktadır."})]}),(0,r.jsx)(l.Wu,{className:"text-center",children:(0,r.jsx)(i.$,{onClick:()=>window.history.back(),children:"Geri D\xf6n"})})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[243,310,895,443,121],()=>a(61237));module.exports=r})();