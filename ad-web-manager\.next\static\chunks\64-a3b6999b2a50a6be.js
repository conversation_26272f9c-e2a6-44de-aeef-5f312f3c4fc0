"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[64],{968:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(2115),o=n(3655),l=n(5155),i=r.forwardRef((e,t)=>(0,l.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var a=i},1154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1275:(e,t,n)=>{n.d(t,{X:()=>l});var r=n(2115),o=n(2712);function l(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},2525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},3717:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(2115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},6474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6830:(e,t,n)=>{n.d(t,{UC:()=>nm,In:()=>np,q7:()=>ng,VF:()=>nw,p4:()=>ny,ZL:()=>nh,bL:()=>nc,wn:()=>nb,PP:()=>nx,l9:()=>nd,WT:()=>nf,LM:()=>nv});var r,o=n(2115),l=n(7650);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(5185);function s(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function u(e,t){var n=s(e,t,"get");return n.get?n.get.call(e):n.value}function c(e,t,n){var r=s(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var d=n(6081),f=n(6101),p=n(9708),h=n(5155),m=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=g(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function g(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap;var y=o.createContext(void 0),w=n(9178),x=n(2293),b=n(7900),S=n(1285);let C=["top","right","bottom","left"],R=Math.min,k=Math.max,A=Math.round,T=Math.floor,E=e=>({x:e,y:e}),j={left:"right",right:"left",bottom:"top",top:"bottom"},P={start:"end",end:"start"};function L(e,t){return"function"==typeof e?e(t):e}function M(e){return e.split("-")[0]}function N(e){return e.split("-")[1]}function D(e){return"x"===e?"y":"x"}function I(e){return"y"===e?"height":"width"}let O=new Set(["top","bottom"]);function H(e){return O.has(M(e))?"y":"x"}function B(e){return e.replace(/start|end/g,e=>P[e])}let F=["left","right"],V=["right","left"],W=["top","bottom"],_=["bottom","top"];function z(e){return e.replace(/left|right|bottom|top/g,e=>j[e])}function G(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function K(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function q(e,t,n){let r,{reference:o,floating:l}=e,i=H(t),a=D(H(t)),s=I(a),u=M(t),c="y"===i,d=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,p=o[s]/2-l[s]/2;switch(u){case"top":r={x:d,y:o.y-l.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-l.width,y:f};break;default:r={x:o.x,y:o.y}}switch(N(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let X=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=q(u,r,s),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[l]:{...p[l],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=q(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function U(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=L(t,e),h=G(p),m=a[f?"floating"===d?"reference":"floating":d],v=K(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(m)))||n?m:m.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),g="floating"===d?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),w=await (null==l.isElement?void 0:l.isElement(y))&&await (null==l.getScale?void 0:l.getScale(y))||{x:1,y:1},x=K(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function Y(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Z(e){return C.some(t=>e[t]>=0)}let $=new Set(["left","top"]);async function J(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=M(n),a=N(n),s="y"===H(n),u=$.has(i)?-1:1,c=l&&s?-1:1,d=L(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*c,y:f*u}:{x:f*u,y:p*c}}function Q(){return"undefined"!=typeof window}function ee(e){return er(e)?(e.nodeName||"").toLowerCase():"#document"}function et(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function en(e){var t;return null==(t=(er(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function er(e){return!!Q()&&(e instanceof Node||e instanceof et(e).Node)}function eo(e){return!!Q()&&(e instanceof Element||e instanceof et(e).Element)}function el(e){return!!Q()&&(e instanceof HTMLElement||e instanceof et(e).HTMLElement)}function ei(e){return!!Q()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof et(e).ShadowRoot)}let ea=new Set(["inline","contents"]);function es(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ew(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!ea.has(o)}let eu=new Set(["table","td","th"]),ec=[":popover-open",":modal"];function ed(e){return ec.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let ef=["transform","translate","scale","rotate","perspective"],ep=["transform","translate","scale","rotate","perspective","filter"],eh=["paint","layout","strict","content"];function em(e){let t=ev(),n=eo(e)?ew(e):e;return ef.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||ep.some(e=>(n.willChange||"").includes(e))||eh.some(e=>(n.contain||"").includes(e))}function ev(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eg=new Set(["html","body","#document"]);function ey(e){return eg.has(ee(e))}function ew(e){return et(e).getComputedStyle(e)}function ex(e){return eo(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eb(e){if("html"===ee(e))return e;let t=e.assignedSlot||e.parentNode||ei(e)&&e.host||en(e);return ei(t)?t.host:t}function eS(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eb(t);return ey(n)?t.ownerDocument?t.ownerDocument.body:t.body:el(n)&&es(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=et(o);if(l){let e=eC(i);return t.concat(i,i.visualViewport||[],es(o)?o:[],e&&n?eS(e):[])}return t.concat(o,eS(o,[],n))}function eC(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eR(e){let t=ew(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=el(e),l=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=A(n)!==l||A(r)!==i;return a&&(n=l,r=i),{width:n,height:r,$:a}}function ek(e){return eo(e)?e:e.contextElement}function eA(e){let t=ek(e);if(!el(t))return E(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=eR(t),i=(l?A(n.width):n.width)/r,a=(l?A(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),a&&Number.isFinite(a)||(a=1),{x:i,y:a}}let eT=E(0);function eE(e){let t=et(e);return ev()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eT}function ej(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=ek(e),a=E(1);t&&(r?eo(r)&&(a=eA(r)):a=eA(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===et(i))&&o)?eE(i):E(0),u=(l.left+s.x)/a.x,c=(l.top+s.y)/a.y,d=l.width/a.x,f=l.height/a.y;if(i){let e=et(i),t=r&&eo(r)?et(r):r,n=e,o=eC(n);for(;o&&r&&t!==n;){let e=eA(o),t=o.getBoundingClientRect(),r=ew(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=l,c+=i,o=eC(n=et(o))}}return K({width:d,height:f,x:u,y:c})}function eP(e,t){let n=ex(e).scrollLeft;return t?t.left+n:ej(en(e)).left+n}function eL(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eP(e,r)),y:r.top+t.scrollTop}}let eM=new Set(["absolute","fixed"]);function eN(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=et(e),r=en(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,s=0;if(o){l=o.width,i=o.height;let e=ev();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:i,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=en(e),n=ex(e),r=e.ownerDocument.body,o=k(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=k(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),i=-n.scrollLeft+eP(e),a=-n.scrollTop;return"rtl"===ew(r).direction&&(i+=k(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:i,y:a}}(en(e));else if(eo(t))r=function(e,t){let n=ej(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=el(e)?eA(e):E(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=eE(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return K(r)}function eD(e){return"static"===ew(e).position}function eI(e,t){if(!el(e)||"fixed"===ew(e).position)return null;if(t)return t(e);let n=e.offsetParent;return en(e)===n&&(n=n.ownerDocument.body),n}function eO(e,t){var n;let r=et(e);if(ed(e))return r;if(!el(e)){let t=eb(e);for(;t&&!ey(t);){if(eo(t)&&!eD(t))return t;t=eb(t)}return r}let o=eI(e,t);for(;o&&(n=o,eu.has(ee(n)))&&eD(o);)o=eI(o,t);return o&&ey(o)&&eD(o)&&!em(o)?r:o||function(e){let t=eb(e);for(;el(t)&&!ey(t);){if(em(t))return t;if(ed(t))break;t=eb(t)}return null}(e)||r}let eH=async function(e){let t=this.getOffsetParent||eO,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=el(t),o=en(t),l="fixed"===n,i=ej(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=E(0);if(r||!r&&!l)if(("body"!==ee(t)||es(o))&&(a=ex(t)),r){let e=ej(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eP(o));l&&!r&&o&&(s.x=eP(o));let u=!o||r||l?E(0):eL(o,a);return{x:i.left+a.scrollLeft-s.x-u.x,y:i.top+a.scrollTop-s.y-u.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eB={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=en(r),a=!!t&&ed(t.floating);if(r===i||a&&l)return n;let s={scrollLeft:0,scrollTop:0},u=E(1),c=E(0),d=el(r);if((d||!d&&!l)&&(("body"!==ee(r)||es(i))&&(s=ex(r)),el(r))){let e=ej(r);u=eA(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!i||d||l?E(0):eL(i,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-s.scrollTop*u.y+c.y+f.y}},getDocumentElement:en,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?ed(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eS(e,[],!1).filter(e=>eo(e)&&"body"!==ee(e)),o=null,l="fixed"===ew(e).position,i=l?eb(e):e;for(;eo(i)&&!ey(i);){let t=ew(i),n=em(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&eM.has(o.position)||es(i)&&!n&&function e(t,n){let r=eb(t);return!(r===n||!eo(r)||ey(r))&&("fixed"===ew(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=eb(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],i=l[0],a=l.reduce((e,n)=>{let r=eN(t,n,o);return e.top=k(r.top,e.top),e.right=R(r.right,e.right),e.bottom=R(r.bottom,e.bottom),e.left=k(r.left,e.left),e},eN(t,i,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eO,getElementRects:eH,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eR(e);return{width:t,height:n}},getScale:eA,isElement:eo,isRTL:function(e){return"rtl"===ew(e).direction}};function eF(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eV=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:i,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=L(e,t)||{};if(null==u)return{};let d=G(c),f={x:n,y:r},p=D(H(o)),h=I(p),m=await i.getDimensions(u),v="y"===p,g=v?"clientHeight":"clientWidth",y=l.reference[h]+l.reference[p]-f[p]-l.floating[h],w=f[p]-l.reference[p],x=await (null==i.getOffsetParent?void 0:i.getOffsetParent(u)),b=x?x[g]:0;b&&await (null==i.isElement?void 0:i.isElement(x))||(b=a.floating[g]||l.floating[h]);let S=b/2-m[h]/2-1,C=R(d[v?"top":"left"],S),A=R(d[v?"bottom":"right"],S),T=b-m[h]-A,E=b/2-m[h]/2+(y/2-w/2),j=k(C,R(E,T)),P=!s.arrow&&null!=N(o)&&E!==j&&l.reference[h]/2-(E<C?C:A)-m[h]/2<0,M=P?E<C?E-C:E-T:0;return{[p]:f[p]+M,data:{[p]:j,centerOffset:E-j-M,...P&&{alignmentOffset:M}},reset:P}}}),eW=(e,t,n)=>{let r=new Map,o={platform:eB,...n},l={...o.platform,_c:r};return X(e,t,{...o,platform:l})};var e_="undefined"!=typeof document?o.useLayoutEffect:function(){};function ez(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ez(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ez(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eG(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eK(e,t){let n=eG(e);return Math.round(t*n)/n}function eq(e){let t=o.useRef(e);return e_(()=>{t.current=e}),t}let eX=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eV({element:n.current,padding:r}).fn(t):{}:n?eV({element:n,padding:r}).fn(t):{}}}),eU=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,s=await J(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:i}}}}}(e),options:[e,t]}),eY=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:i=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=L(e,t),u={x:n,y:r},c=await U(t,s),d=H(M(o)),f=D(d),p=u[f],h=u[d];if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+c[e],r=p-c[t];p=k(n,R(p,r))}if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+c[e],r=h-c[t];h=k(n,R(h,r))}let m=a.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:l,[d]:i}}}}}}(e),options:[e,t]}),eZ=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=L(e,t),c={x:n,y:r},d=H(o),f=D(d),p=c[f],h=c[d],m=L(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===f?"height":"width",t=l.reference[f]-l.floating[e]+v.mainAxis,n=l.reference[f]+l.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var g,y;let e="y"===f?"width":"height",t=$.has(M(o)),n=l.reference[d]-l.floating[e]+(t&&(null==(g=i.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=l.reference[d]+l.reference[e]+(t?0:(null==(y=i.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),e$=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=L(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let x=M(a),b=H(c),S=M(c)===c,C=await (null==d.isRTL?void 0:d.isRTL(f.floating)),R=m||(S||!y?[z(c)]:function(e){let t=z(e);return[B(e),t,B(t)]}(c)),k="none"!==g;!m&&k&&R.push(...function(e,t,n,r){let o=N(e),l=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?V:F;return t?F:V;case"left":case"right":return t?W:_;default:return[]}}(M(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(B)))),l}(c,y,g,C));let A=[c,...R],T=await U(t,w),E=[],j=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&E.push(T[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=N(e),o=D(H(e)),l=I(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=z(i)),[i,z(i)]}(a,u,C);E.push(T[e[0]],T[e[1]])}if(j=[...j,{placement:a,overflows:E}],!E.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=A[e];if(t&&("alignment"!==h||b===H(t)||j.every(e=>e.overflows[0]>0&&H(e.placement)===b)))return{data:{index:e,overflows:j},reset:{placement:t}};let n=null==(l=j.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(v){case"bestFit":{let e=null==(i=j.filter(e=>{if(k){let t=H(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eJ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:i,rects:a,platform:s,elements:u}=t,{apply:c=()=>{},...d}=L(e,t),f=await U(t,d),p=M(i),h=N(i),m="y"===H(i),{width:v,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,l=h===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(l=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=v-f.left-f.right,x=R(g-f[o],y),b=R(v-f[l],w),S=!t.middlewareData.shift,C=x,A=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(A=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(C=y),S&&!h){let e=k(f.left,0),t=k(f.right,0),n=k(f.top,0),r=k(f.bottom,0);m?A=v-2*(0!==e||0!==t?e+t:k(f.left,f.right)):C=g-2*(0!==n||0!==r?n+r:k(f.top,f.bottom))}await c({...t,availableWidth:A,availableHeight:C});let T=await s.getDimensions(u.floating);return v!==T.width||g!==T.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eQ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=L(e,t);switch(r){case"referenceHidden":{let e=Y(await U(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:Z(e)}}}case"escaped":{let e=Y(await U(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:Z(e)}}}default:return{}}}}}(e),options:[e,t]}),e0=(e,t)=>({...eX(e),options:[e,t]});var e1=n(3655),e2=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,h.jsx)(e1.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,h.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e2.displayName="Arrow";var e5=n(9033),e6=n(2712),e9=n(1275),e3="Popper",[e8,e4]=(0,d.A)(e3),[e7,te]=e8(e3),tt=e=>{let{__scopePopper:t,children:n}=e,[r,l]=o.useState(null);return(0,h.jsx)(e7,{scope:t,anchor:r,onAnchorChange:l,children:n})};tt.displayName=e3;var tn="PopperAnchor",tr=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...l}=e,i=te(tn,n),a=o.useRef(null),s=(0,f.s)(t,a);return o.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,h.jsx)(e1.sG.div,{...l,ref:s})});tr.displayName=tn;var to="PopperContent",[tl,ti]=e8(to),ta=o.forwardRef((e,t)=>{var n,r,i,a,s,u,c,d;let{__scopePopper:p,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:S=0,sticky:C="partial",hideWhenDetached:A=!1,updatePositionStrategy:E="optimized",onPlaced:j,...P}=e,L=te(to,p),[M,N]=o.useState(null),D=(0,f.s)(t,e=>N(e)),[I,O]=o.useState(null),H=(0,e9.X)(I),B=null!=(c=null==H?void 0:H.width)?c:0,F=null!=(d=null==H?void 0:H.height)?d:0,V="number"==typeof S?S:{top:0,right:0,bottom:0,left:0,...S},W=Array.isArray(b)?b:[b],_=W.length>0,z={padding:V,boundary:W.filter(td),altBoundary:_},{refs:G,floatingStyles:K,placement:q,isPositioned:X,middlewareData:U}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:i,elements:{reference:a,floating:s}={},transform:u=!0,whileElementsMounted:c,open:d}=e,[f,p]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=o.useState(r);ez(h,r)||m(r);let[v,g]=o.useState(null),[y,w]=o.useState(null),x=o.useCallback(e=>{e!==R.current&&(R.current=e,g(e))},[]),b=o.useCallback(e=>{e!==k.current&&(k.current=e,w(e))},[]),S=a||v,C=s||y,R=o.useRef(null),k=o.useRef(null),A=o.useRef(f),T=null!=c,E=eq(c),j=eq(i),P=eq(d),L=o.useCallback(()=>{if(!R.current||!k.current)return;let e={placement:t,strategy:n,middleware:h};j.current&&(e.platform=j.current),eW(R.current,k.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};M.current&&!ez(A.current,t)&&(A.current=t,l.flushSync(()=>{p(t)}))})},[h,t,n,j,P]);e_(()=>{!1===d&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let M=o.useRef(!1);e_(()=>(M.current=!0,()=>{M.current=!1}),[]),e_(()=>{if(S&&(R.current=S),C&&(k.current=C),S&&C){if(E.current)return E.current(S,C,L);L()}},[S,C,L,E,T]);let N=o.useMemo(()=>({reference:R,floating:k,setReference:x,setFloating:b}),[x,b]),D=o.useMemo(()=>({reference:S,floating:C}),[S,C]),I=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=eK(D.floating,f.x),r=eK(D.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eG(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,f.x,f.y]);return o.useMemo(()=>({...f,update:L,refs:N,elements:D,floatingStyles:I}),[f,L,N,D,I])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=ek(e),d=l||i?[...c?eS(c):[],...eS(t)]:[];d.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});let f=c&&s?function(e,t){let n,r=null,o=en(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function i(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),l();let u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=u;if(a||t(),!f||!p)return;let h=T(d),m=T(o.clientWidth-(c+f)),v={rootMargin:-h+"px "+-m+"px "+-T(o.clientHeight-(d+p))+"px "+-T(c)+"px",threshold:k(0,R(1,s))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==s){if(!g)return i();r?i(!1,r):n=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==r||eF(u,e.getBoundingClientRect())||i(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),l}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!u&&h.observe(c),h.observe(t));let m=u?ej(e):null;return u&&function t(){let r=ej(e);m&&!eF(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{l&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===E})},elements:{reference:L.anchor},middleware:[eU({mainAxis:v+F,alignmentAxis:y}),x&&eY({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?eZ():void 0,...z}),x&&e$({...z}),eJ({...z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),I&&e0({element:I,padding:w}),tf({arrowWidth:B,arrowHeight:F}),A&&eQ({strategy:"referenceHidden",...z})]}),[Y,Z]=tp(q),$=(0,e5.c)(j);(0,e6.N)(()=>{X&&(null==$||$())},[X,$]);let J=null==(n=U.arrow)?void 0:n.x,Q=null==(r=U.arrow)?void 0:r.y,ee=(null==(i=U.arrow)?void 0:i.centerOffset)!==0,[et,er]=o.useState();return(0,e6.N)(()=>{M&&er(window.getComputedStyle(M).zIndex)},[M]),(0,h.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:X?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:et,"--radix-popper-transform-origin":[null==(a=U.transformOrigin)?void 0:a.x,null==(s=U.transformOrigin)?void 0:s.y].join(" "),...(null==(u=U.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,h.jsx)(tl,{scope:p,placedSide:Y,onArrowChange:O,arrowX:J,arrowY:Q,shouldHideArrow:ee,children:(0,h.jsx)(e1.sG.div,{"data-side":Y,"data-align":Z,...P,ref:D,style:{...P.style,animation:X?void 0:"none"}})})})});ta.displayName=to;var ts="PopperArrow",tu={top:"bottom",right:"left",bottom:"top",left:"right"},tc=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ti(ts,n),l=tu[o.placedSide];return(0,h.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,h.jsx)(e2,{...r,ref:t,style:{...r.style,display:"block"}})})});function td(e){return null!==e}tc.displayName=ts;var tf=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=tp(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(l=null==(r=u.arrow)?void 0:r.x)?l:0)+d/2,g=(null!=(i=null==(o=u.arrow)?void 0:o.y)?i:0)+f/2,y="",w="";return"bottom"===p?(y=c?m:"".concat(v,"px"),w="".concat(-f,"px")):"top"===p?(y=c?m:"".concat(v,"px"),w="".concat(s.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=c?m:"".concat(g,"px")):"left"===p&&(y="".concat(s.floating.width+f,"px"),w=c?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function tp(e){let[t,n="center"]=e.split("-");return[t,n]}var th=n(4378),tm=n(5845),tv=n(5503),tg=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});o.forwardRef((e,t)=>(0,h.jsx)(e1.sG.span,{...e,ref:t,style:{...tg,...e.style}})).displayName="VisuallyHidden";var ty=n(8168),tw=n(3795),tx=[" ","Enter","ArrowUp","ArrowDown"],tb=[" ","Enter"],tS="Select",[tC,tR,tk]=function(e){let t=e+"CollectionProvider",[n,r]=(0,d.A)(t),[l,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=o.useRef(null),i=o.useRef(new Map).current;return(0,h.jsx)(l,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let s=e+"CollectionSlot",u=(0,p.TL)(s),c=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(s,n),l=(0,f.s)(t,o.collectionRef);return(0,h.jsx)(u,{ref:l,children:r})});c.displayName=s;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,p.TL)(m),y=o.forwardRef((e,t)=>{let{scope:n,children:r,...l}=e,a=o.useRef(null),s=(0,f.s)(t,a),u=i(m,n);return o.useEffect(()=>(u.itemMap.set(a,{ref:a,...l}),()=>void u.itemMap.delete(a))),(0,h.jsx)(g,{...{[v]:""},ref:s,children:r})});return y.displayName=m,[{Provider:a,Slot:c,ItemSlot:y},function(t){let n=i(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(tS),[tA,tT]=(0,d.A)(tS,[tk,e4]),tE=e4(),[tj,tP]=tA(tS),[tL,tM]=tA(tS),tN=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:c,name:d,autoComplete:f,disabled:p,required:m,form:v}=e,g=tE(t),[w,x]=o.useState(null),[b,C]=o.useState(null),[R,k]=o.useState(!1),A=function(e){let t=o.useContext(y);return e||t||"ltr"}(c),[T,E]=(0,tm.i)({prop:r,defaultProp:null!=l&&l,onChange:i,caller:tS}),[j,P]=(0,tm.i)({prop:a,defaultProp:s,onChange:u,caller:tS}),L=o.useRef(null),M=!w||v||!!w.closest("form"),[N,D]=o.useState(new Set),I=Array.from(N).map(e=>e.props.value).join(";");return(0,h.jsx)(tt,{...g,children:(0,h.jsxs)(tj,{required:m,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:C,valueNodeHasChildren:R,onValueNodeHasChildrenChange:k,contentId:(0,S.B)(),value:j,onValueChange:P,open:T,onOpenChange:E,dir:A,triggerPointerDownPosRef:L,disabled:p,children:[(0,h.jsx)(tC.Provider,{scope:t,children:(0,h.jsx)(tL,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),M?(0,h.jsxs)(ni,{"aria-hidden":!0,required:m,tabIndex:-1,name:d,autoComplete:f,value:j,onChange:e=>P(e.target.value),disabled:p,form:v,children:[void 0===j?(0,h.jsx)("option",{value:""}):null,Array.from(N)]},I):null]})})};tN.displayName=tS;var tD="SelectTrigger",tI=o.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...l}=e,i=tE(n),s=tP(tD,n),u=s.disabled||r,c=(0,f.s)(t,s.onTriggerChange),d=tR(n),p=o.useRef("touch"),[m,v,g]=ns(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=nu(t,e,n);void 0!==r&&s.onValueChange(r.value)}),y=e=>{u||(s.onOpenChange(!0),g()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,h.jsx)(tr,{asChild:!0,...i,children:(0,h.jsx)(e1.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":na(s.value)?"":void 0,...l,ref:c,onClick:(0,a.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&y(e)}),onPointerDown:(0,a.m)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(l.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&tx.includes(e.key)&&(y(),e.preventDefault())})})})});tI.displayName=tD;var tO="SelectValue",tH=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,s=tP(tO,n),{onValueNodeHasChildrenChange:u}=s,c=void 0!==l,d=(0,f.s)(t,s.onValueNodeChange);return(0,e6.N)(()=>{u(c)},[u,c]),(0,h.jsx)(e1.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:na(s.value)?(0,h.jsx)(h.Fragment,{children:i}):l})});tH.displayName=tO;var tB=o.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,h.jsx)(e1.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tB.displayName="SelectIcon";var tF=e=>(0,h.jsx)(th.Z,{asChild:!0,...e});tF.displayName="SelectPortal";var tV="SelectContent",tW=o.forwardRef((e,t)=>{let n=tP(tV,e.__scopeSelect),[r,i]=o.useState();return((0,e6.N)(()=>{i(new DocumentFragment)},[]),n.open)?(0,h.jsx)(tK,{...e,ref:t}):r?l.createPortal((0,h.jsx)(t_,{scope:e.__scopeSelect,children:(0,h.jsx)(tC.Slot,{scope:e.__scopeSelect,children:(0,h.jsx)("div",{children:e.children})})}),r):null});tW.displayName=tV;var[t_,tz]=tA(tV),tG=(0,p.TL)("SelectContent.RemoveScroll"),tK=o.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:s,side:u,sideOffset:c,align:d,alignOffset:p,arrowPadding:m,collisionBoundary:v,collisionPadding:g,sticky:y,hideWhenDetached:S,avoidCollisions:C,...R}=e,k=tP(tV,n),[A,T]=o.useState(null),[E,j]=o.useState(null),P=(0,f.s)(t,e=>T(e)),[L,M]=o.useState(null),[N,D]=o.useState(null),I=tR(n),[O,H]=o.useState(!1),B=o.useRef(!1);o.useEffect(()=>{if(A)return(0,ty.Eq)(A)},[A]),(0,x.Oh)();let F=o.useCallback(e=>{let[t,...n]=I().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&E&&(E.scrollTop=0),n===r&&E&&(E.scrollTop=E.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[I,E]),V=o.useCallback(()=>F([L,A]),[F,L,A]);o.useEffect(()=>{O&&V()},[O,V]);let{onOpenChange:W,triggerPointerDownPosRef:_}=k;o.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=_.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(r=_.current)?void 0:r.y)?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():A.contains(n.target)||W(!1),document.removeEventListener("pointermove",t),_.current=null};return null!==_.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[A,W,_]),o.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[z,G]=ns(e=>{let t=I().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=nu(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=o.useCallback((e,t,n)=>{let r=!B.current&&!n;(void 0!==k.value&&k.value===t||r)&&(M(e),r&&(B.current=!0))},[k.value]),q=o.useCallback(()=>null==A?void 0:A.focus(),[A]),X=o.useCallback((e,t,n)=>{let r=!B.current&&!n;(void 0!==k.value&&k.value===t||r)&&D(e)},[k.value]),U="popper"===r?tX:tq,Y=U===tX?{side:u,sideOffset:c,align:d,alignOffset:p,arrowPadding:m,collisionBoundary:v,collisionPadding:g,sticky:y,hideWhenDetached:S,avoidCollisions:C}:{};return(0,h.jsx)(t_,{scope:n,content:A,viewport:E,onViewportChange:j,itemRefCallback:K,selectedItem:L,onItemLeave:q,itemTextRefCallback:X,focusSelectedItem:V,selectedItemText:N,position:r,isPositioned:O,searchRef:z,children:(0,h.jsx)(tw.A,{as:tG,allowPinchZoom:!0,children:(0,h.jsx)(b.n,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(l,e=>{var t;null==(t=k.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,h.jsx)(w.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,h.jsx)(U,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...R,...Y,onPlaced:()=>H(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,a.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});tK.displayName="SelectContentImpl";var tq=o.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...l}=e,a=tP(tV,n),s=tz(tV,n),[u,c]=o.useState(null),[d,p]=o.useState(null),m=(0,f.s)(t,e=>p(e)),v=tR(n),g=o.useRef(!1),y=o.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:b,focusSelectedItem:S}=s,C=o.useCallback(()=>{if(a.trigger&&a.valueNode&&u&&d&&w&&x&&b){let e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==a.dir){let r=o.left-t.left,l=n.left-r,a=e.left-l,s=e.width+a,c=Math.max(s,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=s+"px",u.style.left=d+"px"}else{let r=t.right-o.right,l=window.innerWidth-n.right-r,a=window.innerWidth-e.right-l,s=e.width+a,c=Math.max(s,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=s+"px",u.style.right=d+"px"}let l=v(),s=window.innerHeight-20,c=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),m=parseInt(f.borderBottomWidth,10),y=p+h+c+parseInt(f.paddingBottom,10)+m,S=Math.min(5*x.offsetHeight,y),C=window.getComputedStyle(w),R=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),A=e.top+e.height/2-10,T=x.offsetHeight/2,E=p+h+(x.offsetTop+T);if(E<=A){let e=l.length>0&&x===l[l.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-A,T+(e?k:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+m);u.style.height=E+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;u.style.top="0px";let t=Math.max(A,p+w.offsetTop+(e?R:0)+T);u.style.height=t+(y-E)+"px",w.scrollTop=E-A+w.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=S+"px",u.style.maxHeight=s+"px",null==r||r(),requestAnimationFrame(()=>g.current=!0)}},[v,a.trigger,a.valueNode,u,d,w,x,b,a.dir,r]);(0,e6.N)(()=>C(),[C]);let[R,k]=o.useState();(0,e6.N)(()=>{d&&k(window.getComputedStyle(d).zIndex)},[d]);let A=o.useCallback(e=>{e&&!0===y.current&&(C(),null==S||S(),y.current=!1)},[C,S]);return(0,h.jsx)(tU,{scope:n,contentWrapper:u,shouldExpandOnScrollRef:g,onScrollButtonChange:A,children:(0,h.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,h.jsx)(e1.sG.div,{...l,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tq.displayName="SelectItemAlignedPosition";var tX=o.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=tE(n);return(0,h.jsx)(ta,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tX.displayName="SelectPopperPosition";var[tU,tY]=tA(tV,{}),tZ="SelectViewport",t$=o.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...l}=e,i=tz(tZ,n),s=tY(tZ,n),u=(0,f.s)(t,i.onViewportChange),c=o.useRef(0);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,h.jsx)(tC.Slot,{scope:n,children:(0,h.jsx)(e1.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,a.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if((null==r?void 0:r.current)&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});t$.displayName=tZ;var tJ="SelectGroup",[tQ,t0]=tA(tJ);o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,S.B)();return(0,h.jsx)(tQ,{scope:n,id:o,children:(0,h.jsx)(e1.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=tJ;var t1="SelectLabel";o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=t0(t1,n);return(0,h.jsx)(e1.sG.div,{id:o.id,...r,ref:t})}).displayName=t1;var t2="SelectItem",[t5,t6]=tA(t2),t9=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:l=!1,textValue:i,...s}=e,u=tP(t2,n),c=tz(t2,n),d=u.value===r,[p,m]=o.useState(null!=i?i:""),[v,g]=o.useState(!1),y=(0,f.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,r,l)}),w=(0,S.B)(),x=o.useRef("touch"),b=()=>{l||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,h.jsx)(t5,{scope:n,value:r,disabled:l,textId:w,isSelected:d,onItemTextChange:o.useCallback(e=>{m(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,h.jsx)(tC.ItemSlot,{scope:n,value:r,disabled:l,textValue:p,children:(0,h.jsx)(e1.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":d&&v,"data-state":d?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...s,ref:y,onFocus:(0,a.m)(s.onFocus,()=>g(!0)),onBlur:(0,a.m)(s.onBlur,()=>g(!1)),onClick:(0,a.m)(s.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:(0,a.m)(s.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:(0,a.m)(s.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,a.m)(s.onPointerMove,e=>{if(x.current=e.pointerType,l){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(s.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(tb.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});t9.displayName=t2;var t3="SelectItemText",t8=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:i,...a}=e,s=tP(t3,n),u=tz(t3,n),c=t6(t3,n),d=tM(t3,n),[p,m]=o.useState(null),v=(0,f.s)(t,e=>m(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),g=null==p?void 0:p.textContent,y=o.useMemo(()=>(0,h.jsx)("option",{value:c.value,disabled:c.disabled,children:g},c.value),[c.disabled,c.value,g]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=d;return(0,e6.N)(()=>(w(y),()=>x(y)),[w,x,y]),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(e1.sG.span,{id:c.textId,...a,ref:v}),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?l.createPortal(a.children,s.valueNode):null]})});t8.displayName=t3;var t4="SelectItemIndicator",t7=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t6(t4,n).isSelected?(0,h.jsx)(e1.sG.span,{"aria-hidden":!0,...r,ref:t}):null});t7.displayName=t4;var ne="SelectScrollUpButton",nt=o.forwardRef((e,t)=>{let n=tz(ne,e.__scopeSelect),r=tY(ne,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,f.s)(t,r.onScrollButtonChange);return(0,e6.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,h.jsx)(no,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});nt.displayName=ne;var nn="SelectScrollDownButton",nr=o.forwardRef((e,t)=>{let n=tz(nn,e.__scopeSelect),r=tY(nn,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,f.s)(t,r.onScrollButtonChange);return(0,e6.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,h.jsx)(no,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});nr.displayName=nn;var no=o.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...l}=e,i=tz("SelectScrollButton",n),s=o.useRef(null),u=tR(n),c=o.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return o.useEffect(()=>()=>c(),[c]),(0,e6.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,h.jsx)(e1.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,a.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(r,50))}),onPointerMove:(0,a.m)(l.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(r,50))}),onPointerLeave:(0,a.m)(l.onPointerLeave,()=>{c()})})});o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,h.jsx)(e1.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var nl="SelectArrow";o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tE(n),l=tP(nl,n),i=tz(nl,n);return l.open&&"popper"===i.position?(0,h.jsx)(tc,{...o,...r,ref:t}):null}).displayName=nl;var ni=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...l}=e,i=o.useRef(null),a=(0,f.s)(t,i),s=(0,tv.Z)(r);return o.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[s,r]),(0,h.jsx)(e1.sG.select,{...l,style:{...tg,...l.style},ref:a,defaultValue:r})});function na(e){return""===e||void 0===e}function ns(e){let t=(0,e5.c)(e),n=o.useRef(""),r=o.useRef(0),l=o.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=o.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,l,i]}function nu(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==n?s:void 0}ni.displayName="SelectBubbleInput";var nc=tN,nd=tI,nf=tH,np=tB,nh=tF,nm=tW,nv=t$,ng=t9,ny=t8,nw=t7,nx=nt,nb=nr},6981:(e,t,n)=>{n.d(t,{C1:()=>C,bL:()=>b});var r=n(2115),o=n(6101),l=n(6081),i=n(5185),a=n(5845),s=n(5503),u=n(1275),c=n(8905),d=n(3655),f=n(5155),p="Checkbox",[h,m]=(0,l.A)(p),[v,g]=h(p);function y(e){let{__scopeCheckbox:t,checked:n,children:o,defaultChecked:l,disabled:i,form:s,name:u,onCheckedChange:c,required:d,value:h="on",internal_do_not_use_render:m}=e,[g,y]=(0,a.i)({prop:n,defaultProp:null!=l&&l,onChange:c,caller:p}),[w,x]=r.useState(null),[b,S]=r.useState(null),C=r.useRef(!1),R=!w||!!s||!!w.closest("form"),k={checked:g,disabled:i,setChecked:y,control:w,setControl:x,name:u,form:s,value:h,hasConsumerStoppedPropagationRef:C,required:d,defaultChecked:!A(l)&&l,isFormControl:R,bubbleInput:b,setBubbleInput:S};return(0,f.jsx)(v,{scope:t,...k,children:"function"==typeof m?m(k):o})}var w="CheckboxTrigger",x=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,onKeyDown:l,onClick:a,...s}=e,{control:u,value:c,disabled:p,checked:h,required:m,setControl:v,setChecked:y,hasConsumerStoppedPropagationRef:x,isFormControl:b,bubbleInput:S}=g(w,n),C=(0,o.s)(t,v),R=r.useRef(h);return r.useEffect(()=>{let e=null==u?void 0:u.form;if(e){let t=()=>y(R.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,y]),(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":A(h)?"mixed":h,"aria-required":m,"data-state":T(h),"data-disabled":p?"":void 0,disabled:p,value:c,...s,ref:C,onKeyDown:(0,i.m)(l,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(a,e=>{y(e=>!!A(e)||!e),S&&b&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});x.displayName=w;var b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:o,defaultChecked:l,required:i,disabled:a,value:s,onCheckedChange:u,form:c,...d}=e;return(0,f.jsx)(y,{__scopeCheckbox:n,checked:o,defaultChecked:l,disabled:a,required:i,onCheckedChange:u,name:r,form:c,value:s,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(x,{...d,ref:t,__scopeCheckbox:n}),r&&(0,f.jsx)(k,{__scopeCheckbox:n})]})}})});b.displayName=p;var S="CheckboxIndicator",C=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,l=g(S,n);return(0,f.jsx)(c.C,{present:r||A(l.checked)||!0===l.checked,children:(0,f.jsx)(d.sG.span,{"data-state":T(l.checked),"data-disabled":l.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=S;var R="CheckboxBubbleInput",k=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,...l}=e,{control:i,hasConsumerStoppedPropagationRef:a,checked:c,defaultChecked:p,required:h,disabled:m,name:v,value:y,form:w,bubbleInput:x,setBubbleInput:b}=g(R,n),S=(0,o.s)(t,b),C=(0,s.Z)(c),k=(0,u.X)(i);r.useEffect(()=>{if(!x)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(C!==c&&e){let n=new Event("click",{bubbles:t});x.indeterminate=A(c),e.call(x,!A(c)&&c),x.dispatchEvent(n)}},[x,C,c,a]);let T=r.useRef(!A(c)&&c);return(0,f.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:T.current,required:h,disabled:m,name:v,value:y,form:w,...l,tabIndex:-1,ref:S,style:{...l.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(e){return"indeterminate"===e}function T(e){return A(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=R},7863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}}]);