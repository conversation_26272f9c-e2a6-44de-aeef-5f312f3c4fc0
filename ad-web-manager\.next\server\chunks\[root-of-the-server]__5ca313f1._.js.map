{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/app/api/locked-users/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport ldap from 'ldapjs';\nimport fs from 'fs';\nimport path from 'path';\n\nconst SETTINGS_FILE = path.join(process.cwd(), 'ldap-settings.json');\n\ninterface LdapSettings {\n  server: string;\n  port: string;\n  baseDN: string;\n  username: string;\n  password: string;\n  useSSL: boolean;\n}\n\ninterface LockedUser {\n  username: string;\n  displayName: string;\n  lockoutTime: string;\n  passwordExpires?: string;\n  accountExpires?: string;\n  lastLogon?: string;\n}\n\nfunction loadSettings(): LdapSettings | null {\n  try {\n    if (fs.existsSync(SETTINGS_FILE)) {\n      const data = fs.readFileSync(SETTINGS_FILE, 'utf8');\n      return JSON.parse(data);\n    }\n    return null;\n  } catch (error) {\n    console.error('Error loading settings:', error);\n    return null;\n  }\n}\n\nexport async function GET() {\n  try {\n    const settings = loadSettings();\n    \n    if (!settings) {\n      return NextResponse.json(\n        { error: 'LDAP ayarları bulunamadı. Lütfen önce ayarları yapılandırın.' },\n        { status: 400 }\n      );\n    }\n\n    if (!settings.server || !settings.baseDN || !settings.username || !settings.password) {\n      return NextResponse.json(\n        { error: 'LDAP ayarları eksik. Lütfen ayarları kontrol edin.' },\n        { status: 400 }\n      );\n    }\n\n    const protocol = settings.useSSL ? 'ldaps' : 'ldap';\n    const url = `${protocol}://${settings.server}:${settings.port}`;\n    \n    const client = ldap.createClient({\n      url: url,\n      timeout: 30000,\n      connectTimeout: 10000,\n      tlsOptions: {\n        rejectUnauthorized: false\n      }\n    });\n\n    return new Promise((resolve) => {\n      let isResolved = false;\n\n      const resolveOnce = (response: NextResponse) => {\n        if (!isResolved) {\n          isResolved = true;\n          client.destroy();\n          resolve(response);\n        }\n      };\n\n      const timeout = setTimeout(() => {\n        resolveOnce(NextResponse.json(\n          { error: 'LDAP bağlantısı zaman aşımına uğradı' },\n          { status: 408 }\n        ));\n      }, 35000);\n\n      client.on('error', (err) => {\n        clearTimeout(timeout);\n        console.error('LDAP connection error:', err);\n        resolveOnce(NextResponse.json(\n          { error: `LDAP bağlantı hatası: ${err.message}` },\n          { status: 500 }\n        ));\n      });\n\n      client.bind(settings.username, settings.password, (err) => {\n        if (err) {\n          clearTimeout(timeout);\n          console.error('LDAP bind error:', err);\n          resolveOnce(NextResponse.json(\n            { error: `LDAP kimlik doğrulama hatası: ${err.message}` },\n            { status: 401 }\n          ));\n          return;\n        }\n\n        // Search for locked users and users with expired passwords\n        const searchFilter = '(&(objectClass=user)(|(lockoutTime>=1)(userAccountControl:1.2.840.113556.1.4.803:=8388608)))';\n        const searchOptions = {\n          scope: 'sub' as const,\n          filter: searchFilter,\n          attributes: [\n            'sAMAccountName',\n            'displayName',\n            'lockoutTime',\n            'pwdLastSet',\n            'accountExpires',\n            'lastLogon',\n            'userAccountControl',\n            'msDS-UserPasswordExpiryTimeComputed'\n          ]\n        };\n\n        const lockedUsers: LockedUser[] = [];\n\n        client.search(settings.baseDN, searchOptions, (searchErr, searchRes) => {\n          if (searchErr) {\n            clearTimeout(timeout);\n            console.error('LDAP search error:', searchErr);\n            resolveOnce(NextResponse.json(\n              { error: `LDAP arama hatası: ${searchErr.message}` },\n              { status: 500 }\n            ));\n            return;\n          }\n\n          searchRes.on('searchEntry', (entry) => {\n            try {\n              const attributes = entry.pojo.attributes;\n              const getAttributeValue = (name: string) => {\n                const attr = attributes.find((a: any) => a.type === name);\n                return attr && attr.values && attr.values.length > 0 ? attr.values[0] : '';\n              };\n\n              const lockoutTime = getAttributeValue('lockoutTime');\n              const pwdLastSet = getAttributeValue('pwdLastSet');\n              const accountExpires = getAttributeValue('accountExpires');\n              const lastLogon = getAttributeValue('lastLogon');\n              const passwordExpiryComputed = getAttributeValue('msDS-UserPasswordExpiryTimeComputed');\n\n              // Convert Windows FILETIME to JavaScript Date\n              const convertFileTime = (fileTime: string) => {\n                if (!fileTime || fileTime === '0' || fileTime === '9223372036854775807') return '';\n                try {\n                  const windowsEpoch = new Date('1601-01-01T00:00:00Z').getTime();\n                  const jsDate = new Date(windowsEpoch + (parseInt(fileTime) / 10000));\n                  return jsDate.toISOString();\n                } catch {\n                  return '';\n                }\n              };\n\n              // Get password expiration date\n              let passwordExpires = '';\n              if (passwordExpiryComputed && passwordExpiryComputed !== '0') {\n                // Use the computed password expiry time from AD\n                passwordExpires = convertFileTime(passwordExpiryComputed);\n              } else if (pwdLastSet && pwdLastSet !== '0') {\n                // Fallback: calculate based on pwdLastSet + 90 days\n                try {\n                  const pwdDate = convertFileTime(pwdLastSet);\n                  if (pwdDate) {\n                    const expireDate = new Date(pwdDate);\n                    expireDate.setDate(expireDate.getDate() + 90); // 90 days default\n                    passwordExpires = expireDate.toISOString();\n                  }\n                } catch {\n                  passwordExpires = '';\n                }\n              }\n\n              const user: LockedUser = {\n                username: getAttributeValue('sAMAccountName'),\n                displayName: getAttributeValue('displayName'),\n                lockoutTime: convertFileTime(lockoutTime),\n                passwordExpires,\n                accountExpires: convertFileTime(accountExpires),\n                lastLogon: convertFileTime(lastLogon)\n              };\n\n              if (user.username) {\n                lockedUsers.push(user);\n              }\n            } catch (entryErr) {\n              console.error('Error processing search entry:', entryErr);\n            }\n          });\n\n          searchRes.on('error', (searchResErr) => {\n            clearTimeout(timeout);\n            console.error('LDAP search result error:', searchResErr);\n            resolveOnce(NextResponse.json(\n              { error: `LDAP arama sonuç hatası: ${searchResErr.message}` },\n              { status: 500 }\n            ));\n          });\n\n          searchRes.on('end', () => {\n            clearTimeout(timeout);\n\n            // Calculate statistics\n            const now = new Date();\n            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n\n            const stats = {\n              totalLocked: lockedUsers.filter(u => u.lockoutTime).length,\n              lockedToday: lockedUsers.filter(u => {\n                if (!u.lockoutTime) return false;\n                const lockDate = new Date(u.lockoutTime);\n                return lockDate >= today;\n              }).length,\n              passwordExpired: lockedUsers.filter(u => {\n                if (!u.passwordExpires) return false;\n                return new Date(u.passwordExpires) <= now;\n              }).length,\n              lastUpdated: now.toISOString()\n            };\n\n            resolveOnce(NextResponse.json({\n              success: true,\n              users: lockedUsers,\n              count: lockedUsers.length,\n              stats\n            }));\n          });\n        });\n      });\n    });\n\n  } catch (error) {\n    console.error('Get locked users error:', error);\n    return NextResponse.json(\n      { error: 'Kilitlenen kullanıcılar alınırken hata oluştu' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAoB/C,SAAS;IACP,IAAI;QACF,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,gBAAgB;YAChC,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,eAAe;YAC5C,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW;QAEjB,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+D,GACxE;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,QAAQ,EAAE;YACpF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqD,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,SAAS,MAAM,GAAG,UAAU;QAC7C,MAAM,MAAM,GAAG,SAAS,GAAG,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,IAAI,EAAE;QAE/D,MAAM,SAAS,wIAAA,CAAA,UAAI,CAAC,YAAY,CAAC;YAC/B,KAAK;YACL,SAAS;YACT,gBAAgB;YAChB,YAAY;gBACV,oBAAoB;YACtB;QACF;QAEA,OAAO,IAAI,QAAQ,CAAC;YAClB,IAAI,aAAa;YAEjB,MAAM,cAAc,CAAC;gBACnB,IAAI,CAAC,YAAY;oBACf,aAAa;oBACb,OAAO,OAAO;oBACd,QAAQ;gBACV;YACF;YAEA,MAAM,UAAU,WAAW;gBACzB,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;oBAAE,OAAO;gBAAuC,GAChD;oBAAE,QAAQ;gBAAI;YAElB,GAAG;YAEH,OAAO,EAAE,CAAC,SAAS,CAAC;gBAClB,aAAa;gBACb,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;oBAAE,OAAO,CAAC,sBAAsB,EAAE,IAAI,OAAO,EAAE;gBAAC,GAChD;oBAAE,QAAQ;gBAAI;YAElB;YAEA,OAAO,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,QAAQ,EAAE,CAAC;gBACjD,IAAI,KAAK;oBACP,aAAa;oBACb,QAAQ,KAAK,CAAC,oBAAoB;oBAClC,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;wBAAE,OAAO,CAAC,8BAA8B,EAAE,IAAI,OAAO,EAAE;oBAAC,GACxD;wBAAE,QAAQ;oBAAI;oBAEhB;gBACF;gBAEA,2DAA2D;gBAC3D,MAAM,eAAe;gBACrB,MAAM,gBAAgB;oBACpB,OAAO;oBACP,QAAQ;oBACR,YAAY;wBACV;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;gBAEA,MAAM,cAA4B,EAAE;gBAEpC,OAAO,MAAM,CAAC,SAAS,MAAM,EAAE,eAAe,CAAC,WAAW;oBACxD,IAAI,WAAW;wBACb,aAAa;wBACb,QAAQ,KAAK,CAAC,sBAAsB;wBACpC,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;4BAAE,OAAO,CAAC,mBAAmB,EAAE,UAAU,OAAO,EAAE;wBAAC,GACnD;4BAAE,QAAQ;wBAAI;wBAEhB;oBACF;oBAEA,UAAU,EAAE,CAAC,eAAe,CAAC;wBAC3B,IAAI;4BACF,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU;4BACxC,MAAM,oBAAoB,CAAC;gCACzB,MAAM,OAAO,WAAW,IAAI,CAAC,CAAC,IAAW,EAAE,IAAI,KAAK;gCACpD,OAAO,QAAQ,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,IAAI,KAAK,MAAM,CAAC,EAAE,GAAG;4BAC1E;4BAEA,MAAM,cAAc,kBAAkB;4BACtC,MAAM,aAAa,kBAAkB;4BACrC,MAAM,iBAAiB,kBAAkB;4BACzC,MAAM,YAAY,kBAAkB;4BACpC,MAAM,yBAAyB,kBAAkB;4BAEjD,8CAA8C;4BAC9C,MAAM,kBAAkB,CAAC;gCACvB,IAAI,CAAC,YAAY,aAAa,OAAO,aAAa,uBAAuB,OAAO;gCAChF,IAAI;oCACF,MAAM,eAAe,IAAI,KAAK,wBAAwB,OAAO;oCAC7D,MAAM,SAAS,IAAI,KAAK,eAAgB,SAAS,YAAY;oCAC7D,OAAO,OAAO,WAAW;gCAC3B,EAAE,OAAM;oCACN,OAAO;gCACT;4BACF;4BAEA,+BAA+B;4BAC/B,IAAI,kBAAkB;4BACtB,IAAI,0BAA0B,2BAA2B,KAAK;gCAC5D,gDAAgD;gCAChD,kBAAkB,gBAAgB;4BACpC,OAAO,IAAI,cAAc,eAAe,KAAK;gCAC3C,oDAAoD;gCACpD,IAAI;oCACF,MAAM,UAAU,gBAAgB;oCAChC,IAAI,SAAS;wCACX,MAAM,aAAa,IAAI,KAAK;wCAC5B,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK,KAAK,kBAAkB;wCACjE,kBAAkB,WAAW,WAAW;oCAC1C;gCACF,EAAE,OAAM;oCACN,kBAAkB;gCACpB;4BACF;4BAEA,MAAM,OAAmB;gCACvB,UAAU,kBAAkB;gCAC5B,aAAa,kBAAkB;gCAC/B,aAAa,gBAAgB;gCAC7B;gCACA,gBAAgB,gBAAgB;gCAChC,WAAW,gBAAgB;4BAC7B;4BAEA,IAAI,KAAK,QAAQ,EAAE;gCACjB,YAAY,IAAI,CAAC;4BACnB;wBACF,EAAE,OAAO,UAAU;4BACjB,QAAQ,KAAK,CAAC,kCAAkC;wBAClD;oBACF;oBAEA,UAAU,EAAE,CAAC,SAAS,CAAC;wBACrB,aAAa;wBACb,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;4BAAE,OAAO,CAAC,yBAAyB,EAAE,aAAa,OAAO,EAAE;wBAAC,GAC5D;4BAAE,QAAQ;wBAAI;oBAElB;oBAEA,UAAU,EAAE,CAAC,OAAO;wBAClB,aAAa;wBAEb,uBAAuB;wBACvB,MAAM,MAAM,IAAI;wBAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;wBAErE,MAAM,QAAQ;4BACZ,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;4BAC1D,aAAa,YAAY,MAAM,CAAC,CAAA;gCAC9B,IAAI,CAAC,EAAE,WAAW,EAAE,OAAO;gCAC3B,MAAM,WAAW,IAAI,KAAK,EAAE,WAAW;gCACvC,OAAO,YAAY;4BACrB,GAAG,MAAM;4BACT,iBAAiB,YAAY,MAAM,CAAC,CAAA;gCAClC,IAAI,CAAC,EAAE,eAAe,EAAE,OAAO;gCAC/B,OAAO,IAAI,KAAK,EAAE,eAAe,KAAK;4BACxC,GAAG,MAAM;4BACT,aAAa,IAAI,WAAW;wBAC9B;wBAEA,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;4BAC5B,SAAS;4BACT,OAAO;4BACP,OAAO,YAAY,MAAM;4BACzB;wBACF;oBACF;gBACF;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgD,GACzD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}