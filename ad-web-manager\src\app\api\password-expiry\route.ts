import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import ldap from 'ldapjs';
import fs from 'fs';
import path from 'path';

interface LdapSettings {
  server: string;
  port: string;
  baseDN: string;
  username: string;
  password: string;
  useSSL: boolean;
}

interface ExpiredUser {
  username: string;
  displayName: string;
  passwordExpires: string;
  daysSinceExpiry: number;
  lastLogon?: string;
  department?: string;
}

interface DashboardStats {
  totalExpiredUsers: number;
  expiredToday: number;
  expiredThisWeek: number;
  expiredThisMonth: number;
}

function getLdapSettings(): LdapSettings {
  try {
    const settingsPath = path.join(process.cwd(), 'ldap-settings.json');
    const settingsData = fs.readFileSync(settingsPath, 'utf8');
    return JSON.parse(settingsData);
  } catch (error) {
    console.error('Error reading LDAP settings:', error);
    throw new Error('LDAP settings not found');
  }
}

function convertWindowsTimeToDate(windowsTime: string): Date | null {
  try {
    const windowsTimeNum = parseInt(windowsTime);
    if (windowsTimeNum === 0 || isNaN(windowsTimeNum)) return null;
    
    // Windows FILETIME epoch starts at January 1, 1601
    // JavaScript Date epoch starts at January 1, 1970
    // Difference is 11644473600 seconds
    const unixTime = (windowsTimeNum / 10000000) - 11644473600;
    return new Date(unixTime * 1000);
  } catch {
    return null;
  }
}

function calculatePasswordExpiry(pwdLastSet: string, maxPasswordAge: number): Date | null {
  const lastSetDate = convertWindowsTimeToDate(pwdLastSet);
  if (!lastSetDate) return null;
  
  // maxPasswordAge is in 100-nanosecond intervals (negative value)
  const maxAgeDays = Math.abs(maxPasswordAge) / (10000000 * 60 * 60 * 24);
  
  const expiryDate = new Date(lastSetDate);
  expiryDate.setDate(expiryDate.getDate() + maxAgeDays);
  
  return expiryDate;
}

async function getExpiredUsers(): Promise<{ users: ExpiredUser[], stats: DashboardStats }> {
  const settings = getLdapSettings();
  
  return new Promise((resolve, reject) => {
    const ldapUrl = `${settings.useSSL ? 'ldaps' : 'ldap'}://${settings.server}:${settings.port}`;
    console.log('Connecting to LDAP for password expiry check:', ldapUrl);
    
    const client = ldap.createClient({
      url: ldapUrl,
      timeout: 10000,
      connectTimeout: 10000,
    });

    client.bind(settings.username, settings.password, (bindErr) => {
      if (bindErr) {
        console.error('LDAP bind error:', bindErr);
        client.destroy();
        return reject(new Error('LDAP authentication failed'));
      }

      console.log('LDAP bind successful, searching for users with expired passwords...');

      // First, get domain password policy
      const domainSearchOptions = {
        scope: 'base' as const,
        filter: '(objectClass=domain)',
        attributes: ['maxPwdAge']
      };

      client.search(settings.baseDN, domainSearchOptions, (domainErr, domainRes) => {
        if (domainErr) {
          console.error('Domain search error:', domainErr);
          client.destroy();
          return reject(new Error('Failed to get domain password policy'));
        }

        let maxPasswordAge = 0;

        domainRes.on('searchEntry', (entry) => {
          const maxPwdAge = entry.pojo.attributes.find((attr: any) => attr.type === 'maxPwdAge');
          if (maxPwdAge && maxPwdAge.values && maxPwdAge.values[0]) {
            maxPasswordAge = parseInt(maxPwdAge.values[0]);
          }
        });

        domainRes.on('end', () => {
          if (maxPasswordAge === 0) {
            console.log('Password expiry is disabled in domain policy');
            client.destroy();
            return resolve({ 
              users: [], 
              stats: { totalExpiredUsers: 0, expiredToday: 0, expiredThisWeek: 0, expiredThisMonth: 0 }
            });
          }

          // Now search for users
          // Filter explanation:
          // - objectClass=user: Only user objects
          // - objectCategory=person: Only person objects (excludes computer accounts)
          // - !(userAccountControl:1.2.840.113556.1.4.803:=2): Exclude disabled accounts
          const userSearchOptions = {
            scope: 'sub' as const,
            filter: '(&(objectClass=user)(objectCategory=person)(!(userAccountControl:1.2.840.113556.1.4.803:=2)))',
            attributes: [
              'sAMAccountName',
              'displayName',
              'pwdLastSet',
              'lastLogon',
              'department',
              'userAccountControl'
            ]
          };

          client.search(settings.baseDN, userSearchOptions, (userErr, userRes) => {
            if (userErr) {
              console.error('User search error:', userErr);
              client.destroy();
              return reject(new Error('Failed to search users'));
            }

            const expiredUsers: ExpiredUser[] = [];
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const weekAgo = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
            const monthAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

            userRes.on('searchEntry', (entry) => {
              try {
                const attributes = entry.pojo.attributes;
                const username = attributes.find((attr: any) => attr.type === 'sAMAccountName')?.values[0];
                const displayName = attributes.find((attr: any) => attr.type === 'displayName')?.values[0] || username;
                const pwdLastSet = attributes.find((attr: any) => attr.type === 'pwdLastSet')?.values[0];
                const lastLogon = attributes.find((attr: any) => attr.type === 'lastLogon')?.values[0];
                const department = attributes.find((attr: any) => attr.type === 'department')?.values[0];
                const userAccountControl = attributes.find((attr: any) => attr.type === 'userAccountControl')?.values[0];

                if (!username || !pwdLastSet || pwdLastSet === '0') return;

                // Check if password never expires (DONT_EXPIRE_PASSWORD flag = 0x10000 = 65536)
                if (userAccountControl) {
                  const uacValue = parseInt(userAccountControl);
                  const DONT_EXPIRE_PASSWORD = 0x10000; // 65536

                  if (uacValue & DONT_EXPIRE_PASSWORD) {
                    console.log(`✅ Skipping user ${username} - password never expires (UAC: ${uacValue})`);
                    return; // Skip users with password never expires
                  } else {
                    console.log(`🔍 Checking user ${username} - password can expire (UAC: ${uacValue})`);
                  }
                } else {
                  console.log(`⚠️ User ${username} - no userAccountControl found`);
                }

                const passwordExpiry = calculatePasswordExpiry(pwdLastSet, maxPasswordAge);
                if (!passwordExpiry) return;

                // Check if password has expired
                if (passwordExpiry < now) {
                  const daysSinceExpiry = Math.floor((now.getTime() - passwordExpiry.getTime()) / (1000 * 60 * 60 * 24));

                  console.log(`Found expired user: ${username}, expired ${daysSinceExpiry} days ago`);

                  expiredUsers.push({
                    username,
                    displayName,
                    passwordExpires: passwordExpiry.toISOString(),
                    daysSinceExpiry,
                    lastLogon: lastLogon && lastLogon !== '0' ? convertWindowsTimeToDate(lastLogon)?.toISOString() : undefined,
                    department
                  });
                }
              } catch (error) {
                console.error('Error processing user entry:', error);
              }
            });

            userRes.on('end', () => {
              client.destroy();
              
              // Calculate stats
              const stats: DashboardStats = {
                totalExpiredUsers: expiredUsers.length,
                expiredToday: expiredUsers.filter(user => {
                  const expiry = new Date(user.passwordExpires);
                  return expiry >= today && expiry < new Date(today.getTime() + 24 * 60 * 60 * 1000);
                }).length,
                expiredThisWeek: expiredUsers.filter(user => {
                  const expiry = new Date(user.passwordExpires);
                  return expiry >= weekAgo;
                }).length,
                expiredThisMonth: expiredUsers.filter(user => {
                  const expiry = new Date(user.passwordExpires);
                  return expiry >= monthAgo;
                }).length
              };

              // Sort by days since expiry (most recent first)
              expiredUsers.sort((a, b) => a.daysSinceExpiry - b.daysSinceExpiry);

              console.log(`Found ${expiredUsers.length} users with expired passwords`);
              resolve({ users: expiredUsers, stats });
            });

            userRes.on('error', (error) => {
              console.error('User search result error:', error);
              client.destroy();
              reject(new Error('Error during user search'));
            });
          });
        });

        domainRes.on('error', (error) => {
          console.error('Domain search result error:', error);
          client.destroy();
          reject(new Error('Error during domain search'));
        });
      });
    });

    client.on('error', (error) => {
      console.error('LDAP client error:', error);
      reject(new Error('LDAP connection failed'));
    });
  });
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user.permissions.unlockUsers) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await getExpiredUsers();
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Password expiry API error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to get expired users' },
      { status: 500 }
    );
  }
}
