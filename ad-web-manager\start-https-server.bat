@echo off
title AD Web Manager HTTPS Server
color 0A

echo.
echo ========================================
echo   AD Web Manager HTTPS Server Baslatiliyor
echo ========================================
echo.

REM Gerekli dizine git
cd /d "%~dp0"

echo [1/4] SSL sertifikasi kontrolu yapiliyor...

if not exist "ssl\localhost.crt" (
    echo SSL sertifikasi bulunamadi!
    echo SSL sertifikasi olusturuluyor...
    call create-ssl-cert.bat
    if errorlevel 1 (
        echo HATA: SSL sertifikasi olusturulamadi!
        pause
        exit /b 1
    )
) else (
    echo SSL sertifikasi mevcut.
)
echo.

echo [2/4] Dependencies kontrolu yapiliyor...
if not exist "node_modules" (
    echo Dependencies bulunamadi. Yukleniyor...
    call npm install
) else (
    echo Dependencies mevcut.
)
echo.

echo [3/4] LDAP ayarlari kontrolu yapiliyor...
if not exist "ldap-settings.json" (
    echo LDAP ayarlari olusturuluyor...
    echo {"server":"**********","port":"389","baseDN":"DC=egefrn,DC=bayraktar,DC=com","username":"<EMAIL>","password":"Ebt1991.,","useSSL":false} > ldap-settings.json
    echo LDAP ayarlari olusturuldu.
) else (
    echo LDAP ayarlari mevcut.
)
echo.

REM Environment dosyasi kontrolu
if not exist ".env.local" (
    echo Environment dosyasi olusturuluyor...
    echo NEXTAUTH_SECRET=your-secret-key-here-change-this-in-production> .env.local
    echo NEXTAUTH_URL=https://localhost:3000>> .env.local
) else (
    REM NEXTAUTH_URL'i HTTPS olarak guncelle
    powershell -Command "(Get-Content .env.local) -replace 'NEXTAUTH_URL=http://localhost:3000', 'NEXTAUTH_URL=https://localhost:3000' | Set-Content .env.local"
)

echo [4/4] HTTPS Server baslatiliyor...
echo.
echo ========================================
echo   Server https://localhost:3000 adresinde
echo   Kapatmak icin Ctrl+C basin
echo ========================================
echo.
echo UYARI: Tarayicida "Guvenli degil" uyarisi cikabilir.
echo "Gelismis" -> "localhost'a git (guvenli degil)" tiklayin.
echo.

REM HTTPS development server'i baslat
set HTTPS=true
set SSL_CRT_FILE=ssl/localhost.crt
set SSL_KEY_FILE=ssl/localhost.key

call npm run dev

echo.
echo Server kapatildi.
pause
