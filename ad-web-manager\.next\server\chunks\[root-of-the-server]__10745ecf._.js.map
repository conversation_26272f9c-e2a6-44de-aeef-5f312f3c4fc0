{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/app/api/unlock-user/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport ldap from 'ldapjs';\nimport fs from 'fs';\nimport path from 'path';\n\nconst SETTINGS_FILE = path.join(process.cwd(), 'ldap-settings.json');\n\ninterface LdapSettings {\n  server: string;\n  port: string;\n  baseDN: string;\n  username: string;\n  password: string;\n  useSSL: boolean;\n}\n\nfunction loadSettings(): LdapSettings | null {\n  try {\n    if (fs.existsSync(SETTINGS_FILE)) {\n      const data = fs.readFileSync(SETTINGS_FILE, 'utf8');\n      return JSON.parse(data);\n    }\n    return null;\n  } catch (error) {\n    console.error('Error loading settings:', error);\n    return null;\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { username } = await request.json();\n    \n    if (!username) {\n      return NextResponse.json(\n        { error: 'Kullanıcı adı gereklidir' },\n        { status: 400 }\n      );\n    }\n\n    const settings = loadSettings();\n    \n    if (!settings) {\n      return NextResponse.json(\n        { error: 'LDAP ayarları bulunamadı. Lütfen önce ayarları yapılandırın.' },\n        { status: 400 }\n      );\n    }\n\n    if (!settings.server || !settings.baseDN || !settings.username || !settings.password) {\n      return NextResponse.json(\n        { error: 'LDAP ayarları eksik. Lütfen ayarları kontrol edin.' },\n        { status: 400 }\n      );\n    }\n\n    const protocol = settings.useSSL ? 'ldaps' : 'ldap';\n    const url = `${protocol}://${settings.server}:${settings.port}`;\n    \n    console.log(`Attempting LDAP connection to: ${url}`);\n    console.log(`Username for unlock: ${username}`);\n\n    const client = ldap.createClient({\n      url: url,\n      timeout: 60000,\n      connectTimeout: 20000,\n      tlsOptions: {\n        rejectUnauthorized: false\n      }\n    });\n\n    return new Promise((resolve) => {\n      let isResolved = false;\n\n      const resolveOnce = (response: NextResponse) => {\n        if (!isResolved) {\n          isResolved = true;\n          client.destroy();\n          resolve(response);\n        }\n      };\n\n      const timeout = setTimeout(() => {\n        console.error(`LDAP timeout for user: ${username}`);\n        resolveOnce(NextResponse.json(\n          { error: 'LDAP işlemi zaman aşımına uğradı (60s)' },\n          { status: 408 }\n        ));\n      }, 65000);\n\n      client.on('error', (err) => {\n        clearTimeout(timeout);\n        console.error('LDAP connection error:', err);\n        resolveOnce(NextResponse.json(\n          { error: `LDAP bağlantı hatası: ${err.message}` },\n          { status: 500 }\n        ));\n      });\n\n      console.log(`Attempting LDAP bind with user: ${settings.username}`);\n\n      client.bind(settings.username, settings.password, (err) => {\n        if (err) {\n          clearTimeout(timeout);\n          console.error('LDAP bind error:', err);\n          resolveOnce(NextResponse.json(\n            { error: `LDAP kimlik doğrulama hatası: ${err.message}` },\n            { status: 401 }\n          ));\n          return;\n        }\n\n        console.log('LDAP bind successful, searching for user...');\n\n        // First, find the user's DN\n        const searchFilter = `(&(objectClass=user)(sAMAccountName=${username}))`;\n        const searchOptions = {\n          scope: 'sub' as const,\n          filter: searchFilter,\n          attributes: ['distinguishedName', 'lockoutTime', 'userAccountControl']\n        };\n\n        client.search(settings.baseDN, searchOptions, (searchErr, searchRes) => {\n          if (searchErr) {\n            clearTimeout(timeout);\n            console.error('LDAP search error:', searchErr);\n            resolveOnce(NextResponse.json(\n              { error: `Kullanıcı arama hatası: ${searchErr.message}` },\n              { status: 500 }\n            ));\n            return;\n          }\n\n          let userDN = '';\n          let foundUser = false;\n\n          searchRes.on('searchEntry', (entry) => {\n            foundUser = true;\n            userDN = entry.pojo.objectName;\n\n            // Also try to get DN from attributes if objectName is not available\n            if (!userDN) {\n              const attributes = entry.pojo.attributes;\n              const dnAttr = attributes.find((a: any) => a.type === 'distinguishedName');\n              if (dnAttr && dnAttr.values && dnAttr.values.length > 0) {\n                userDN = dnAttr.values[0];\n              }\n            }\n          });\n\n          searchRes.on('error', (searchResErr) => {\n            clearTimeout(timeout);\n            console.error('LDAP search result error:', searchResErr);\n            resolveOnce(NextResponse.json(\n              { error: `Kullanıcı arama sonuç hatası: ${searchResErr.message}` },\n              { status: 500 }\n            ));\n          });\n\n          searchRes.on('end', () => {\n            if (!foundUser || !userDN) {\n              clearTimeout(timeout);\n              console.error(`User not found or DN not available. Found: ${foundUser}, DN: ${userDN}, Username: ${username}`);\n              resolveOnce(NextResponse.json(\n                { error: `Kullanıcı bulunamadı veya DN alınamadı: ${username}` },\n                { status: 404 }\n              ));\n              return;\n            }\n\n            console.log(`Found user ${username} with DN: ${userDN}`);\n\n            // Try multiple unlock methods\n            const unlockMethods = [\n              // Method 1: Set lockoutTime to 0\n              {\n                operation: 'replace',\n                modification: {\n                  type: 'lockoutTime',\n                  values: ['0']\n                }\n              },\n              // Method 2: Remove lockoutTime attribute\n              {\n                operation: 'delete',\n                modification: {\n                  type: 'lockoutTime'\n                }\n              }\n            ];\n\n            let methodIndex = 0;\n\n            const tryUnlock = () => {\n              if (methodIndex >= unlockMethods.length) {\n                clearTimeout(timeout);\n                resolveOnce(NextResponse.json(\n                  { error: `Tüm unlock yöntemleri başarısız oldu` },\n                  { status: 500 }\n                ));\n                return;\n              }\n\n              const change = new ldap.Change(unlockMethods[methodIndex]);\n\n              client.modify(userDN, change, (modifyErr) => {\n                if (modifyErr) {\n                  console.error(`LDAP modify error (method ${methodIndex + 1}):`, modifyErr);\n                  methodIndex++;\n                  tryUnlock(); // Try next method\n                } else {\n                  clearTimeout(timeout);\n                  resolveOnce(NextResponse.json({\n                    success: true,\n                    message: `${username} kullanıcısı başarıyla unlock edildi (method ${methodIndex + 1})`\n                  }));\n                }\n              });\n            };\n\n            tryUnlock();\n          });\n        });\n      });\n    });\n\n  } catch (error) {\n    console.error('Unlock user error:', error);\n    return NextResponse.json(\n      { error: 'Unlock işlemi sırasında hata oluştu' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAW/C,SAAS;IACP,IAAI;QACF,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,gBAAgB;YAChC,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,eAAe;YAC5C,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEvC,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW;QAEjB,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+D,GACxE;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,QAAQ,EAAE;YACpF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqD,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,SAAS,MAAM,GAAG,UAAU;QAC7C,MAAM,MAAM,GAAG,SAAS,GAAG,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,IAAI,EAAE;QAE/D,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,KAAK;QACnD,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,UAAU;QAE9C,MAAM,SAAS,qGAAA,CAAA,UAAI,CAAC,YAAY,CAAC;YAC/B,KAAK;YACL,SAAS;YACT,gBAAgB;YAChB,YAAY;gBACV,oBAAoB;YACtB;QACF;QAEA,OAAO,IAAI,QAAQ,CAAC;YAClB,IAAI,aAAa;YAEjB,MAAM,cAAc,CAAC;gBACnB,IAAI,CAAC,YAAY;oBACf,aAAa;oBACb,OAAO,OAAO;oBACd,QAAQ;gBACV;YACF;YAEA,MAAM,UAAU,WAAW;gBACzB,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,UAAU;gBAClD,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;oBAAE,OAAO;gBAAyC,GAClD;oBAAE,QAAQ;gBAAI;YAElB,GAAG;YAEH,OAAO,EAAE,CAAC,SAAS,CAAC;gBAClB,aAAa;gBACb,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;oBAAE,OAAO,CAAC,sBAAsB,EAAE,IAAI,OAAO,EAAE;gBAAC,GAChD;oBAAE,QAAQ;gBAAI;YAElB;YAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,SAAS,QAAQ,EAAE;YAElE,OAAO,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,QAAQ,EAAE,CAAC;gBACjD,IAAI,KAAK;oBACP,aAAa;oBACb,QAAQ,KAAK,CAAC,oBAAoB;oBAClC,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;wBAAE,OAAO,CAAC,8BAA8B,EAAE,IAAI,OAAO,EAAE;oBAAC,GACxD;wBAAE,QAAQ;oBAAI;oBAEhB;gBACF;gBAEA,QAAQ,GAAG,CAAC;gBAEZ,4BAA4B;gBAC5B,MAAM,eAAe,CAAC,oCAAoC,EAAE,SAAS,EAAE,CAAC;gBACxE,MAAM,gBAAgB;oBACpB,OAAO;oBACP,QAAQ;oBACR,YAAY;wBAAC;wBAAqB;wBAAe;qBAAqB;gBACxE;gBAEA,OAAO,MAAM,CAAC,SAAS,MAAM,EAAE,eAAe,CAAC,WAAW;oBACxD,IAAI,WAAW;wBACb,aAAa;wBACb,QAAQ,KAAK,CAAC,sBAAsB;wBACpC,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;4BAAE,OAAO,CAAC,wBAAwB,EAAE,UAAU,OAAO,EAAE;wBAAC,GACxD;4BAAE,QAAQ;wBAAI;wBAEhB;oBACF;oBAEA,IAAI,SAAS;oBACb,IAAI,YAAY;oBAEhB,UAAU,EAAE,CAAC,eAAe,CAAC;wBAC3B,YAAY;wBACZ,SAAS,MAAM,IAAI,CAAC,UAAU;wBAE9B,oEAAoE;wBACpE,IAAI,CAAC,QAAQ;4BACX,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU;4BACxC,MAAM,SAAS,WAAW,IAAI,CAAC,CAAC,IAAW,EAAE,IAAI,KAAK;4BACtD,IAAI,UAAU,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;gCACvD,SAAS,OAAO,MAAM,CAAC,EAAE;4BAC3B;wBACF;oBACF;oBAEA,UAAU,EAAE,CAAC,SAAS,CAAC;wBACrB,aAAa;wBACb,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;4BAAE,OAAO,CAAC,8BAA8B,EAAE,aAAa,OAAO,EAAE;wBAAC,GACjE;4BAAE,QAAQ;wBAAI;oBAElB;oBAEA,UAAU,EAAE,CAAC,OAAO;wBAClB,IAAI,CAAC,aAAa,CAAC,QAAQ;4BACzB,aAAa;4BACb,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,UAAU,MAAM,EAAE,OAAO,YAAY,EAAE,UAAU;4BAC7G,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;gCAAE,OAAO,CAAC,wCAAwC,EAAE,UAAU;4BAAC,GAC/D;gCAAE,QAAQ;4BAAI;4BAEhB;wBACF;wBAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,SAAS,UAAU,EAAE,QAAQ;wBAEvD,8BAA8B;wBAC9B,MAAM,gBAAgB;4BACpB,iCAAiC;4BACjC;gCACE,WAAW;gCACX,cAAc;oCACZ,MAAM;oCACN,QAAQ;wCAAC;qCAAI;gCACf;4BACF;4BACA,yCAAyC;4BACzC;gCACE,WAAW;gCACX,cAAc;oCACZ,MAAM;gCACR;4BACF;yBACD;wBAED,IAAI,cAAc;wBAElB,MAAM,YAAY;4BAChB,IAAI,eAAe,cAAc,MAAM,EAAE;gCACvC,aAAa;gCACb,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC3B;oCAAE,OAAO,CAAC,oCAAoC,CAAC;gCAAC,GAChD;oCAAE,QAAQ;gCAAI;gCAEhB;4BACF;4BAEA,MAAM,SAAS,IAAI,qGAAA,CAAA,UAAI,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY;4BAEzD,OAAO,MAAM,CAAC,QAAQ,QAAQ,CAAC;gCAC7B,IAAI,WAAW;oCACb,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE;oCAChE;oCACA,aAAa,kBAAkB;gCACjC,OAAO;oCACL,aAAa;oCACb,YAAY,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wCAC5B,SAAS;wCACT,SAAS,GAAG,SAAS,6CAA6C,EAAE,cAAc,EAAE,CAAC,CAAC;oCACxF;gCACF;4BACF;wBACF;wBAEA;oBACF;gBACF;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsC,GAC/C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}