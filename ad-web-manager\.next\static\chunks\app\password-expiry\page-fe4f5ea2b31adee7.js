(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[890],{646:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},968:(e,s,a)=>{"use strict";a.d(s,{b:()=>i});var t=a(2115),r=a(3655),l=a(5155),n=t.forwardRef((e,s)=>(0,l.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var i=n},1154:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1243:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>l});var t=a(5155);a(2115);var r=a(9434);function l(e){let{className:s,type:a,...l}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...l})}},2783:(e,s,a)=>{Promise.resolve().then(a.bind(a,9424))},3280:(e,s,a)=>{"use strict";a.d(s,{E$:()=>x,bL:()=>o});var t=a(5155),r=a(2115),l=a(646),n=a(4861),i=a(4416),c=a(9434);function d(e){let{id:s,title:a,description:d,variant:o,duration:x=8e3,onClose:m}=e,[u,h]=(0,r.useState)(!1),[p,j]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=setTimeout(()=>h(!0),100),s=setTimeout(()=>{f()},x);return()=>{clearTimeout(e),clearTimeout(s)}},[x]);let f=()=>{j(!0),setTimeout(()=>{m(s)},300)};return(0,t.jsx)("div",{className:(0,c.cn)("fixed bottom-4 right-4 z-50 w-96 max-w-sm p-4 rounded-lg shadow-lg border transition-all duration-300 transform",{"bg-green-50 border-green-200 text-green-800":"success"===o,"bg-red-50 border-red-200 text-red-800":"error"===o,"translate-x-full opacity-0":!u,"translate-x-0 opacity-100":u&&!p,"translate-x-full opacity-0":p}),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:"success"===o?(0,t.jsx)(l.A,{className:"h-5 w-5 text-green-600"}):(0,t.jsx)(n.A,{className:"h-5 w-5 text-red-600"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-semibold",children:a}),(0,t.jsx)("p",{className:"text-sm mt-1 whitespace-pre-line",children:d})]}),(0,t.jsx)("button",{onClick:f,className:"flex-shrink-0 ml-2 p-1 rounded-md hover:bg-black/5 transition-colors",children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})]})})}function o(e){let{notifications:s,onRemove:a}=e;return(0,t.jsx)("div",{className:"fixed bottom-0 right-0 z-50 p-4 space-y-2",children:s.map(e=>(0,t.jsx)(d,{...e,onClose:a},e.id))})}function x(){let[e,s]=(0,r.useState)([]);return{notifications:e,addNotification:e=>{let a=Math.random().toString(36).substr(2,9);s(s=>[...s,{...e,id:a}])},removeNotification:e=>{s(s=>s.filter(s=>s.id!==e))}}}},3904:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4186:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>n});var t=a(5155);a(2115);var r=a(968),l=a(9434);function n(e){let{className:s,...a}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},5127:(e,s,a)=>{"use strict";a.d(s,{A0:()=>n,BF:()=>i,Hj:()=>c,XI:()=>l,nA:()=>o,nd:()=>d});var t=a(5155);a(2115);var r=a(9434);function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",s),...a})})}function n(e){let{className:s,...a}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}},5365:(e,s,a)=>{"use strict";a.d(s,{Fc:()=>i,TN:()=>c});var t=a(5155);a(2115);var r=a(2085),l=a(9434);let n=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(n({variant:a}),s),...r})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...a})}},9424:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var t=a(5155),r=a(2115),l=a(2108),n=a(6695),i=a(285),c=a(5365),d=a(5127),o=a(3280),x=a(420),m=a(3978),u=a(8931),h=a(2523),p=a(5057),j=a(1243),f=a(3904),b=a(4186),y=a(9530),g=a(1154),N=a(9946);let v=(0,N.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var k=a(4861);let w=(0,N.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),A=(0,N.A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);function S(){let{data:e}=(0,l.useSession)(),s=(0,x.X)(),{notifications:a,addNotification:N,removeNotification:S}=(0,o.E$)();if(!e)return null;let[D,E]=(0,r.useState)([]),[M,T]=(0,r.useState)({totalExpiredUsers:0,expiredToday:0,expiredThisWeek:0,expiredThisMonth:0}),[z,B]=(0,r.useState)(!1),[U,Z]=(0,r.useState)(null),[C,F]=(0,r.useState)(null),[L,H]=(0,r.useState)(""),[R,K]=(0,r.useState)(null),[W,_]=(0,r.useState)(!1),[$,G]=(0,r.useState)(""),P=async()=>{B(!0);try{let e=await fetch("/api/password-expiry"),s=await e.json();e.ok?(E(s.users||[]),T(s.stats||{totalExpiredUsers:0,expiredToday:0,expiredThisWeek:0,expiredThisMonth:0}),F(new Date)):N({title:"❌ Y\xfckleme Hatası",description:s.error||"Şifre s\xfcresi dolan kullanıcılar y\xfcklenirken hata oluştu!",variant:"error",duration:8e3})}catch(e){N({title:"❌ Bağlantı Hatası",description:"Sunucuya bağlanırken hata oluştu!",variant:"error",duration:8e3})}finally{B(!1)}},q=async()=>{if(!L.trim())return void G("Kullanıcı adı gerekli");_(!0),G(""),K(null);try{let e=await fetch("/api/search-user?username=".concat(encodeURIComponent(L.trim()))),s=await e.json();e.ok?(K(s.user),G("")):(G(s.error||"Kullanıcı bulunamadı"),K(null))}catch(e){G("Arama sırasında hata oluştu"),K(null)}finally{_(!1)}},O=async e=>{Z(e);try{let s=await fetch("/api/extend-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e})}),a=await s.json();if(s.ok){let s=a.extendedAt?new Date(a.extendedAt):new Date,t=a.newExpiryDate?new Date(a.newExpiryDate):null;a.daysExtended;let r="✅ ".concat(e," kullanıcısının şifre s\xfcresi başarıyla uzatıldı!");a.unlocked&&(r+="\n\uD83D\uDD13 Kullanıcı hesabı unlock edildi!"),t&&(r+="\n\uD83D\uDCC5 Yeni şifre son kullanma tarihi: ".concat(t.toLocaleDateString("tr-TR"))),r+="\n⏰ İşlem zamanı: ".concat(s.toLocaleString("tr-TR")),N({title:"✅ İşlem Başarılı",description:"".concat(e," kullanıcısının şifre s\xfcresi başarıyla uzatıldı").concat(a.unlocked?" ve hesap unlock edildi":"","."),variant:"success",duration:8e3}),R&&R.username===e&&(K(null),H("")),setTimeout(()=>{P()},1e3)}else{let e=a.error||"Şifre s\xfcresi uzatılırken hata oluştu!";N({title:"❌ İşlem Başarısız",description:e,variant:"error",duration:8e3})}}catch(e){console.error("Frontend extend password error:",e),N({title:"❌ Bağlantı Hatası",description:"Bağlantı hatası: ".concat(e instanceof Error?e.message:"Bilinmeyen hata"),variant:"error",duration:8e3})}finally{Z(null)}},Y=e=>{try{return new Date(e).toLocaleString("tr-TR")}catch(s){return e}},I=e=>e<=7?"text-red-600":e<=30?"text-orange-600":"text-gray-600";return((0,r.useEffect)(()=>{(null==e?void 0:e.user.permissions.unlockUsers)&&P()},[e]),e.user.permissions.unlockUsers)?(0,t.jsxs)("div",{className:"min-h-screen bg-background",children:[s.isMobile?(0,t.jsx)(m.c,{currentPath:"/password-expiry"}):(0,t.jsx)(u.u,{currentPath:"/password-expiry"}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"".concat(s.isMobile?"space-y-4":"flex items-center justify-between"),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"".concat(s.isMobile?"text-2xl":"text-3xl"," font-bold text-foreground"),children:"Password Expiry Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Şifre s\xfcresi dolan kullanıcıları g\xf6r\xfcnt\xfcleyin ve şifre s\xfcrelerini uzatın"})]}),(0,t.jsxs)("div",{className:"".concat(s.isMobile?"flex flex-col space-y-2":"flex items-center space-x-4"),children:[C&&(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Son g\xfcncelleme: ",Y(C.toISOString())]}),(0,t.jsxs)(i.$,{onClick:P,disabled:z,className:s.isMobile?"w-full":"",children:[(0,t.jsx)(f.A,{className:"mr-2 h-4 w-4 ".concat(z?"animate-spin":"")}),"Yenile"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsxs)(n.Zp,{className:"border-red-200",children:[(0,t.jsx)(n.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,t.jsx)(j.A,{className:"h-6 w-6 text-red-600"}),(0,t.jsx)(n.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Toplam S\xfcresi Dolan"})]})}),(0,t.jsx)(n.Wu,{className:"text-center",children:(0,t.jsx)("div",{className:"text-4xl font-bold text-red-600",children:M.totalExpiredUsers})})]}),(0,t.jsxs)(n.Zp,{className:"border-orange-200",children:[(0,t.jsx)(n.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,t.jsx)(b.A,{className:"h-6 w-6 text-orange-600"}),(0,t.jsx)(n.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Bug\xfcn Dolan"})]})}),(0,t.jsx)(n.Wu,{className:"text-center",children:(0,t.jsx)("div",{className:"text-4xl font-bold text-orange-600",children:M.expiredToday})})]}),(0,t.jsxs)(n.Zp,{className:"border-yellow-200",children:[(0,t.jsx)(n.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,t.jsx)(b.A,{className:"h-6 w-6 text-yellow-600"}),(0,t.jsx)(n.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Bu Hafta Dolan"})]})}),(0,t.jsx)(n.Wu,{className:"text-center",children:(0,t.jsx)("div",{className:"text-4xl font-bold text-yellow-600",children:M.expiredThisWeek})})]}),(0,t.jsxs)(n.Zp,{className:"border-blue-200",children:[(0,t.jsx)(n.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,t.jsx)(y.A,{className:"h-6 w-6 text-blue-600"}),(0,t.jsx)(n.ZB,{className:"text-base font-semibold text-muted-foreground",children:"Bu Ay Dolan"})]})}),(0,t.jsx)(n.Wu,{className:"text-center",children:(0,t.jsx)("div",{className:"text-4xl font-bold text-blue-600",children:M.expiredThisMonth})})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"text-xl",children:"Kullanıcı Arama"}),(0,t.jsx)(n.BT,{children:"Herhangi bir kullanıcının şifre s\xfcresini uzatmak i\xe7in kullanıcı adını arayın"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(p.J,{htmlFor:"search-username",children:"Kullanıcı Adı"}),(0,t.jsx)(h.p,{id:"search-username",placeholder:"Kullanıcı adını girin...",value:L,onChange:e=>H(e.target.value),onKeyPress:e=>"Enter"===e.key&&q()})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsx)(i.$,{onClick:q,disabled:W,children:W?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Aranıyor..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v,{className:"mr-2 h-4 w-4"}),"Ara"]})})})]}),$&&(0,t.jsxs)(c.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(c.TN,{className:"text-red-800",children:$})]}),R&&(0,t.jsx)(n.Zp,{className:"border-blue-200 bg-blue-50",children:(0,t.jsx)(n.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"".concat(s.isMobile?"space-y-4":"flex items-center justify-between"),children:[(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(w,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("span",{className:"font-medium text-blue-900",children:R.displayName}),(0,t.jsxs)("span",{className:"text-sm text-blue-700",children:["(",R.username,")"]})]}),(0,t.jsxs)("div",{className:"grid ".concat(s.isMobile?"grid-cols-1 gap-2":"grid-cols-1 md:grid-cols-3 gap-4"," text-sm"),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-blue-700",children:"Durum: "}),(0,t.jsx)("span",{className:"font-medium ".concat(R.accountDisabled?"text-red-600":"text-green-600"),children:R.accountDisabled?"Devre Dışı":"Aktif"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-blue-700",children:"Şifre: "}),(0,t.jsx)("span",{className:"font-medium ".concat(R.passwordNeverExpires?"text-green-600":"text-blue-600"),children:R.passwordNeverExpires?"Hi\xe7 Dolmaz":"S\xfcreli"})]}),void 0!==R.daysUntilExpiry&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-blue-700",children:"Kalan S\xfcre: "}),(0,t.jsx)("span",{className:"font-medium ".concat(R.daysUntilExpiry<0?"text-red-600":R.daysUntilExpiry<=7?"text-orange-600":"text-green-600"),children:R.daysUntilExpiry<0?"".concat(Math.abs(R.daysUntilExpiry)," g\xfcn \xf6nce doldu"):"".concat(R.daysUntilExpiry," g\xfcn")})]})]}),R.lastLogon&&(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"text-blue-700",children:"Son Giriş: "}),(0,t.jsx)("span",{className:"text-blue-600",children:Y(R.lastLogon)})]})]}),(0,t.jsx)("div",{className:s.isMobile?"w-full":"",children:!R.accountDisabled&&(0,t.jsx)(i.$,{onClick:()=>O(R.username),disabled:U===R.username,className:"bg-blue-600 hover:bg-blue-700 ".concat(s.isMobile?"w-full":""),children:U===R.username?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uzatılıyor & Unlock..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"S\xfcre Uzat & Unlock"]})})})]})})})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"text-xl",children:"Şifre S\xfcresi Dolan Kullanıcılar"}),(0,t.jsx)(n.BT,{children:"Şifre s\xfcresi dolan kullanıcıları g\xf6r\xfcnt\xfcleyin ve şifre s\xfcrelerini uzatın"})]}),(0,t.jsx)(n.Wu,{children:z?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(g.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,t.jsx)("span",{className:"ml-2",children:"Y\xfckleniyor..."})]}):0===D.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(A,{className:"mx-auto h-12 w-12 text-green-500 mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Şifre S\xfcresi Dolan Kullanıcı Yok"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"T\xfcm kullanıcıların şifre s\xfcreleri ge\xe7erli."})]}):s.isMobile?(0,t.jsx)("div",{className:"space-y-3",children:D.map(e=>(0,t.jsxs)(n.Zp,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg text-foreground",children:e.username}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.displayName})]}),(0,t.jsxs)("div",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(I(e.daysSinceExpiry)," bg-opacity-20"),children:[e.daysSinceExpiry," g\xfcn ge\xe7ti"]})]}),(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Şifre Bitiş:"}),(0,t.jsx)("span",{className:"font-medium text-red-600",children:Y(e.passwordExpires)})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Son Giriş:"}),(0,t.jsx)("span",{className:"font-medium",children:e.lastLogon?Y(e.lastLogon):"Hi\xe7 giriş yapmamış"})]})]}),(0,t.jsx)(i.$,{className:"w-full bg-blue-600 hover:bg-blue-700",onClick:()=>O(e.username),disabled:U===e.username,children:U===e.username?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uzatılıyor & Unlock..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"S\xfcre Uzat & Unlock"]})})]},e.username))}):(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{children:(0,t.jsxs)(d.Hj,{children:[(0,t.jsx)(d.nd,{children:"Kullanıcı Adı"}),(0,t.jsx)(d.nd,{children:"Tam Ad"}),(0,t.jsx)(d.nd,{children:"Şifre Bitiş Tarihi"}),(0,t.jsx)(d.nd,{children:"Ge\xe7en G\xfcn"}),(0,t.jsx)(d.nd,{children:"Son Giriş"}),(0,t.jsx)(d.nd,{children:"İşlemler"})]})}),(0,t.jsx)(d.BF,{children:D.map(e=>(0,t.jsxs)(d.Hj,{children:[(0,t.jsx)(d.nA,{className:"font-medium",children:e.username}),(0,t.jsx)(d.nA,{children:e.displayName}),(0,t.jsx)(d.nA,{children:Y(e.passwordExpires)}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("span",{className:"font-medium ".concat(I(e.daysSinceExpiry)),children:[e.daysSinceExpiry," g\xfcn"]})}),(0,t.jsx)(d.nA,{children:e.lastLogon?Y(e.lastLogon):"Hi\xe7"}),(0,t.jsx)(d.nA,{children:(0,t.jsx)(i.$,{size:"sm",onClick:()=>O(e.username),disabled:U===e.username,className:"bg-blue-600 hover:bg-blue-700",children:U===e.username?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uzatılıyor & Unlock..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"S\xfcre Uzat & Unlock"]})})})]},e.username))})]})})})]})]})}),(0,t.jsx)(o.bL,{notifications:a,onRemove:S})]}):(0,t.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center",children:(0,t.jsxs)(n.Zp,{className:"w-full max-w-md",children:[(0,t.jsxs)(n.aR,{className:"text-center",children:[(0,t.jsx)(j.A,{className:"mx-auto h-12 w-12 text-orange-500 mb-4"}),(0,t.jsx)(n.ZB,{children:"Erişim Reddedildi"}),(0,t.jsx)(n.BT,{children:"Bu sayfaya erişim yetkiniz bulunmamaktadır."})]}),(0,t.jsx)(n.Wu,{className:"text-center",children:(0,t.jsx)(i.$,{onClick:()=>window.history.back(),children:"Geri D\xf6n"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[108,460,349,449,441,684,358],()=>s(2783)),_N_E=e.O()}]);