{"name": "ad-web-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:https": "cross-env HTTPS=true SSL_CRT_FILE=ssl/localhost.crt SSL_KEY_FILE=ssl/localhost.key next dev --turbopack", "build": "next build", "start": "next start", "start:https": "cross-env HTTPS=true SSL_CRT_FILE=ssl/localhost.crt SSL_KEY_FILE=ssl/localhost.key next start", "start:production": "cross-env NODE_ENV=production next start -p 80", "start:domain": "cross-env NODE_ENV=production next start -p 80", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/ldapjs": "^3.0.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "ldapjs": "^3.0.7", "lucide-react": "^0.525.0", "next": "15.3.5", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}