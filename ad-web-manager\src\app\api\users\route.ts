import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getUsers, createUser } from '@/lib/auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has permission to manage users
    if (!session.user.permissions?.manageUsers) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const users = getUsers();
    
    // Remove passwords from response
    const safeUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      role: user.role,
      permissions: user.permissions,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
    }));

    return NextResponse.json({ users: safeUsers });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has permission to manage users
    if (!session.user.permissions?.manageUsers) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const userData = await request.json();
    
    // Validate required fields
    if (!userData.username || !userData.password || !userData.role) {
      return NextResponse.json(
        { error: 'Username, password, and role are required' },
        { status: 400 }
      );
    }

    // Validate role
    if (!['admin', 'user'].includes(userData.role)) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      );
    }

    // Create user
    const newUser = createUser({
      username: userData.username,
      password: userData.password,
      role: userData.role,
      permissions: userData.permissions || {
        viewUsers: false,
        unlockUsers: false,
        manageSettings: false,
        manageUsers: false,
      },
    });

    // Return user without password
    const safeUser = {
      id: newUser.id,
      username: newUser.username,
      role: newUser.role,
      permissions: newUser.permissions,
      createdAt: newUser.createdAt,
    };

    return NextResponse.json({ user: safeUser });
  } catch (error: any) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
