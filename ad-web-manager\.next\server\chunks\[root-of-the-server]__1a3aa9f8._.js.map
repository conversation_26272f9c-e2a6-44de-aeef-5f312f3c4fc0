{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20H.BAYRAKTAR%20YATIRIM%20HOLDING%20A.S/PC/Masa%C3%BCst%C3%BC/AD_Web/ad-web-manager/src/app/api/settings/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport fs from 'fs';\nimport path from 'path';\n\nconst SETTINGS_FILE = path.join(process.cwd(), 'ldap-settings.json');\n\ninterface LdapSettings {\n  server: string;\n  port: string;\n  baseDN: string;\n  username: string;\n  password: string;\n  useSSL: boolean;\n}\n\nconst defaultSettings: LdapSettings = {\n  server: '',\n  port: '389',\n  baseDN: '',\n  username: '',\n  password: '',\n  useSSL: false\n};\n\n// GET - Load settings\nexport async function GET() {\n  try {\n    if (fs.existsSync(SETTINGS_FILE)) {\n      const data = fs.readFileSync(SETTINGS_FILE, 'utf8');\n      const settings = JSON.parse(data);\n      // Don't return password in GET request for security\n      return NextResponse.json({\n        ...settings,\n        password: settings.password ? '••••••••' : ''\n      });\n    } else {\n      return NextResponse.json(defaultSettings);\n    }\n  } catch (error) {\n    console.error('Error loading settings:', error);\n    return NextResponse.json(\n      { error: 'Ayarlar yüklenirken hata oluştu' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST - Save settings\nexport async function POST(request: NextRequest) {\n  try {\n    const settings: LdapSettings = await request.json();\n    \n    // Validate required fields\n    if (!settings.server || !settings.baseDN || !settings.username) {\n      return NextResponse.json(\n        { error: 'Server, Base DN ve Username alanları zorunludur' },\n        { status: 400 }\n      );\n    }\n\n    // If password is masked, load the existing password\n    if (settings.password === '••••••••') {\n      if (fs.existsSync(SETTINGS_FILE)) {\n        const existingData = fs.readFileSync(SETTINGS_FILE, 'utf8');\n        const existingSettings = JSON.parse(existingData);\n        settings.password = existingSettings.password;\n      }\n    }\n\n    // Save settings to file\n    fs.writeFileSync(SETTINGS_FILE, JSON.stringify(settings, null, 2));\n    \n    return NextResponse.json({ success: true });\n  } catch (error) {\n    console.error('Error saving settings:', error);\n    return NextResponse.json(\n      { error: 'Ayarlar kaydedilirken hata oluştu' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAW/C,MAAM,kBAAgC;IACpC,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,UAAU;IACV,UAAU;IACV,QAAQ;AACV;AAGO,eAAe;IACpB,IAAI;QACF,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,gBAAgB;YAChC,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,eAAe;YAC5C,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,oDAAoD;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,GAAG,QAAQ;gBACX,UAAU,SAAS,QAAQ,GAAG,aAAa;YAC7C;QACF,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAyB,MAAM,QAAQ,IAAI;QAEjD,2BAA2B;QAC3B,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,QAAQ,EAAE;YAC9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkD,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,oDAAoD;QACpD,IAAI,SAAS,QAAQ,KAAK,YAAY;YACpC,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,gBAAgB;gBAChC,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,eAAe;gBACpD,MAAM,mBAAmB,KAAK,KAAK,CAAC;gBACpC,SAAS,QAAQ,GAAG,iBAAiB,QAAQ;YAC/C;QACF;QAEA,wBAAwB;QACxB,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,eAAe,KAAK,SAAS,CAAC,UAAU,MAAM;QAE/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAoC,GAC7C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}