@echo off
title AD Web Manager Server
color 0A

echo.
echo ========================================
echo    AD Web Manager Server Baslatiliyor
echo ========================================
echo.

REM Gerekli dizine git
cd /d "%~dp0"

echo [1/4] Node.js kontrolu yapiliyor...
echo Node.js ve npm mevcut.
echo.

echo [2/4] Dependencies kontrolu yapiliyor...
if not exist "node_modules" (
    echo Dependencies bulunamadi. Yukleniyor...
    call npm install
) else (
    echo Dependencies mevcut.
)
echo.

echo [3/4] LDAP ayarlari kontrolu yapiliyor...
if not exist "ldap-settings.json" (
    echo LDAP ayarlari olusturuluyor...
    echo {"server":"**********","port":"389","baseDN":"DC=egefrn,DC=bayraktar,DC=com","username":"<EMAIL>","password":"Ebt1991.,","useSSL":false} > ldap-settings.json
    echo LDAP ayarlari olusturuldu.
) else (
    echo LDAP ayarlari mevcut.
)
echo.

if not exist ".env.local" (
    echo Environment dosyasi olusturuluyor...
    echo NEXTAUTH_SECRET=your-secret-key-here-change-this-in-production> .env.local
    echo NEXTAUTH_URL=http://localhost:3000>> .env.local
)

echo [4/4] Server baslatiliyor...
echo.
echo ========================================
echo   Server http://localhost:3000 adresinde
echo   Kapatmak icin Ctrl+C basin
echo ========================================
echo.

call npm run dev

echo.
echo Server kapatildi.
pause
