# AD Web Manager Server Durdurma Script'i
# PowerShell versiyonu

Write-Host ""
Write-Host "========================================" -ForegroundColor Red
Write-Host "   AD Web Manager Server Durduruluyor" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Red
Write-Host ""

# Gerekli dizine git
Set-Location $PSScriptRoot

# Node.js process'lerini bul ve durdur
Write-Host "[1/3] Node.js process'leri aranıyor..." -ForegroundColor Yellow

try {
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        foreach ($process in $nodeProcesses) {
            Write-Host "Node.js process bulundu: $($process.Id) - $($process.ProcessName)" -ForegroundColor Cyan
            try {
                Stop-Process -Id $process.Id -Force
                Write-Host "✅ Process $($process.Id) durduruldu" -ForegroundColor Green
            } catch {
                Write-Host "⚠️  Process $($process.Id) durdurulamadı: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "✅ Node.js process'i bulunamadı" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Node.js process kontrolünde hata: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

# Port 3000-3010 arasını kontrol et
Write-Host "[2/3] Port 3000-3010 arası kontrol ediliyor..." -ForegroundColor Yellow

for ($port = 3000; $port -le 3010; $port++) {
    try {
        $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connections) {
            foreach ($conn in $connections) {
                $processId = $conn.OwningProcess
                if ($processId -and $processId -ne 0) {
                    try {
                        $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
                        if ($process) {
                            Write-Host "Port $port'de process bulundu: $processId - $($process.ProcessName)" -ForegroundColor Cyan
                            Stop-Process -Id $processId -Force
                            Write-Host "✅ Port $port'deki process durduruldu" -ForegroundColor Green
                        }
                    } catch {
                        Write-Host "⚠️  Port $port'deki process durdurulamadı: $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }
            }
        }
    } catch {
        # Port kullanımda değilse sessizce devam et
    }
}

Write-Host ""

# PowerShell process'lerini kontrol et (sadece bilgi için)
Write-Host "[3/3] Temizlik yapılıyor..." -ForegroundColor Yellow

try {
    $psProcesses = Get-Process -Name "powershell" -ErrorAction SilentlyContinue
    if ($psProcesses) {
        Write-Host "Aktif PowerShell process'leri:" -ForegroundColor Cyan
        foreach ($process in $psProcesses) {
            Write-Host "  - Process ID: $($process.Id)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "PowerShell process kontrolünde hata" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   AD Web Manager Server Durduruldu" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "✅ Tüm Node.js ve ilgili process'ler durduruldu." -ForegroundColor Green
Write-Host "🚀 Server'ı yeniden başlatmak için start-server.bat çalıştırın." -ForegroundColor Cyan
Write-Host ""
Read-Host "Devam etmek için Enter'a basın"
