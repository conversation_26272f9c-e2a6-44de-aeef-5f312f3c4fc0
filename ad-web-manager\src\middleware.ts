import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

export default withAuth(
  function middleware(req) {
    const hostname = req.headers.get('host') || '';

    // Mobile subdomain detection
    if (hostname.startsWith('m.')) {
      // Mobile subdomain detected - add mobile context
      const response = NextResponse.next();
      response.headers.set('x-mobile-version', 'true');
      return response;
    }

    // Check for mobile user agent
    const userAgent = req.headers.get('user-agent') || '';
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

    if (isMobile) {
      const response = NextResponse.next();
      response.headers.set('x-is-mobile', 'true');
      return response;
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to login page without authentication
        if (req.nextUrl.pathname === '/login') {
          return true;
        }

        // Require authentication for all other pages
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - bayraktar_holding_logo.jpeg (logo file)
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)',
  ],
};
