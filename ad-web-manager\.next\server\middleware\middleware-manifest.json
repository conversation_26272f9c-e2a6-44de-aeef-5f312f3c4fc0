{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "VxsZ3hRKeHcjFiOsyWz2ZuNDEfY8h4a6QtwEExCtPGY=", "__NEXT_PREVIEW_MODE_ID": "492708dc2a39cf83484cdaecc2636f7b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "af9e72760553649fe8771034c761b587ee6c1fc908ba199f2e35d2aa44c3cd06", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e239e2e402115da75a6e3d51a8709acd3100f7e3adec1d337e5182044e8b3320"}}}, "instrumentation": null, "functions": {}}