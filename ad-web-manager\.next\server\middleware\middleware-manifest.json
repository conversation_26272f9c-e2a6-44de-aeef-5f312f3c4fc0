{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ioihxw0T6uLY6tVdvT/KSIElCQ4mXNoUEabkMtzHbR4=", "__NEXT_PREVIEW_MODE_ID": "4e8947cb1e1b8dc9200c739fdaa4b8c9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "11f84c21c6b01014847625f5197cbbb9bfcb269d53663bf4b79dc40563e35ad8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f68b56982b6efef2fd8f19e6edb692792f5904ad4dba6e395966f19d206ce434"}}}, "instrumentation": null, "functions": {}}