@echo off
title AD Web Manager HTTPS Tunnel
color 0A

echo.
echo ========================================
echo   AD Web Manager HTTPS Tunnel Baslatiliyor
echo ========================================
echo.

REM Gerekli dizine git
cd /d "%~dp0"

echo [1/3] LocalTunnel kontrolu yapiliyor...
where lt >nul 2>&1
if errorlevel 1 (
    echo LocalTunnel bulunamadi. Yukleniyor...
    call npm install -g localtunnel
    if errorlevel 1 (
        echo HATA: LocalTunnel yuklenemedi!
        pause
        exit /b 1
    )
) else (
    echo LocalTunnel mevcut.
)
echo.

echo [2/3] HTTP server kontrolu yapiliyor...
curl -s -o nul -w "%%{http_code}" http://localhost:3001/ | findstr "200" >nul
if errorlevel 1 (
    echo HTTP server calismiyorsa once start-server.bat calistirin!
    echo Server durumu kontrol ediliyor...
    timeout /t 3 >nul
) else (
    echo HTTP server calisir durumda.
)
echo.

echo [3/3] HTTPS tunnel baslatiliyor...
echo.
echo ========================================
echo   HTTPS Tunnel Baslatiliyor
echo   Kapatmak icin Ctrl+C basin
echo ========================================
echo.
echo BILGI: Tunnel URL'i asagida gorunecek
echo Bu URL'i tarayicida acin - GERCEK SSL sertifikali!
echo.

REM LocalTunnel ile HTTPS tunnel baslat
npx localtunnel --port 3001

echo.
echo Tunnel kapatildi.
pause
