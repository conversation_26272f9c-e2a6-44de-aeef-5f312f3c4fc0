(()=>{var e={};e.id=979,e.ids=[979],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73010:e=>{"use strict";e.exports=require("ldapjs")},78335:()=>{},94653:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>D,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>k});var s={};t.r(s),t.d(s,{GET:()=>x});var o=t(96559),a=t(48088),n=t(37719),u=t(32190),i=t(73010),l=t.n(i),c=t(29021),p=t.n(c),d=t(33873);let m=t.n(d)().join(process.cwd(),"ldap-settings.json");async function x(){try{let e=function(){try{if(p().existsSync(m)){let e=p().readFileSync(m,"utf8");return JSON.parse(e)}return null}catch(e){return console.error("Error loading settings:",e),null}}();if(!e)return u.NextResponse.json({error:"LDAP ayarları bulunamadı. L\xfctfen \xf6nce ayarları yapılandırın."},{status:400});if(!e.server||!e.baseDN||!e.username||!e.password)return u.NextResponse.json({error:"LDAP ayarları eksik. L\xfctfen ayarları kontrol edin."},{status:400});let r=e.useSSL?"ldaps":"ldap",t=`${r}://${e.server}:${e.port}`,s=l().createClient({url:t,timeout:3e4,connectTimeout:1e4,tlsOptions:{rejectUnauthorized:!1}});return new Promise(r=>{let t=!1,o=e=>{t||(t=!0,s.destroy(),r(e))},a=setTimeout(()=>{o(u.NextResponse.json({error:"LDAP bağlantısı zaman aşımına uğradı"},{status:408}))},35e3);s.on("error",e=>{clearTimeout(a),console.error("LDAP connection error:",e),o(u.NextResponse.json({error:`LDAP bağlantı hatası: ${e.message}`},{status:500}))}),s.bind(e.username,e.password,r=>{if(r){clearTimeout(a),console.error("LDAP bind error:",r),o(u.NextResponse.json({error:`LDAP kimlik doğrulama hatası: ${r.message}`},{status:401}));return}let t=[];s.search(e.baseDN,{scope:"sub",filter:"(&(objectClass=user)(|(lockoutTime>=1)(userAccountControl:1.2.840.113556.1.4.803:=8388608)))",attributes:["sAMAccountName","displayName","lockoutTime","pwdLastSet","accountExpires","lastLogon","userAccountControl","msDS-UserPasswordExpiryTimeComputed"]},(e,r)=>{if(e){clearTimeout(a),console.error("LDAP search error:",e),o(u.NextResponse.json({error:`LDAP arama hatası: ${e.message}`},{status:500}));return}r.on("searchEntry",e=>{try{let r=e.pojo.attributes,s=e=>{let t=r.find(r=>r.type===e);return t&&t.values&&t.values.length>0?t.values[0]:""},o=s("lockoutTime"),a=s("pwdLastSet"),n=s("accountExpires"),u=s("lastLogon"),i=s("msDS-UserPasswordExpiryTimeComputed"),l=e=>{if(!e||"0"===e||"9223372036854775807"===e)return"";try{let r=new Date("1601-01-01T00:00:00Z").getTime();return new Date(r+parseInt(e)/1e4).toISOString()}catch{return""}},c="";if(i&&"0"!==i)c=l(i);else if(a&&"0"!==a)try{let e=l(a);if(e){let r=new Date(e);r.setDate(r.getDate()+90),c=r.toISOString()}}catch{c=""}let p={username:s("sAMAccountName"),displayName:s("displayName"),lockoutTime:l(o),passwordExpires:c,accountExpires:l(n),lastLogon:l(u)};p.username&&t.push(p)}catch(e){console.error("Error processing search entry:",e)}}),r.on("error",e=>{clearTimeout(a),console.error("LDAP search result error:",e),o(u.NextResponse.json({error:`LDAP arama sonu\xe7 hatası: ${e.message}`},{status:500}))}),r.on("end",()=>{clearTimeout(a);let e=new Date,r=new Date(e.getFullYear(),e.getMonth(),e.getDate()),s={totalLocked:t.filter(e=>e.lockoutTime).length,lockedToday:t.filter(e=>!!e.lockoutTime&&new Date(e.lockoutTime)>=r).length,passwordExpired:t.filter(r=>!!r.passwordExpires&&new Date(r.passwordExpires)<=e).length,lastUpdated:e.toISOString()};o(u.NextResponse.json({success:!0,users:t,count:t.length,stats:s}))})})})})}catch(e){return console.error("Get locked users error:",e),u.NextResponse.json({error:"Kilitlenen kullanıcılar alınırken hata oluştu"},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/locked-users/route",pathname:"/api/locked-users",filename:"route",bundlePath:"app/api/locked-users/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\locked-users\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:k,serverHooks:y}=g;function D(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:k})}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580],()=>t(94653));module.exports=s})();