(()=>{var e={};e.id=541,e.ids=[541],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,r)=>{"use strict";r.d(t,{A0:()=>s,BF:()=>l,Hj:()=>o,XI:()=>a,nA:()=>d,nd:()=>c});var n=r(60687);r(43210);var i=r(4780);function a({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,n.jsx)("table",{"data-slot":"table",className:(0,i.cn)("w-full caption-bottom text-sm",e),...t})})}function s({className:e,...t}){return(0,n.jsx)("thead",{"data-slot":"table-header",className:(0,i.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,n.jsx)("tbody",{"data-slot":"table-body",className:(0,i.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,n.jsx)("tr",{"data-slot":"table-row",className:(0,i.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,n.jsx)("th",{"data-slot":"table-head",className:(0,i.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,n.jsx)("td",{"data-slot":"table-cell",className:(0,i.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13687:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>rW});var n=r(60687),i=r(43210),a=r(85814),s=r.n(a),l=r(82136),o=r(44493),c=r(29523),d=r(89667),u=r(54300),f=r(82978),p=r(91821),m=r(6211),h=r(26134),x=r(11860),g=r(4780);function v({...e}){return(0,n.jsx)(h.bL,{"data-slot":"dialog",...e})}function y({...e}){return(0,n.jsx)(h.l9,{"data-slot":"dialog-trigger",...e})}function w({...e}){return(0,n.jsx)(h.ZL,{"data-slot":"dialog-portal",...e})}function b({className:e,...t}){return(0,n.jsx)(h.hJ,{"data-slot":"dialog-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function j({className:e,children:t,showCloseButton:r=!0,...i}){return(0,n.jsxs)(w,{"data-slot":"dialog-portal",children:[(0,n.jsx)(b,{}),(0,n.jsxs)(h.UC,{"data-slot":"dialog-content",className:(0,g.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...i,children:[t,r&&(0,n.jsxs)(h.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,n.jsx)(x.A,{}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function k({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,g.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function S({className:e,...t}){return(0,n.jsx)(h.hE,{"data-slot":"dialog-title",className:(0,g.cn)("text-lg leading-none font-semibold",e),...t})}function N({className:e,...t}){return(0,n.jsx)(h.VY,{"data-slot":"dialog-description",className:(0,g.cn)("text-muted-foreground text-sm",e),...t})}var C=r(51215);function A(e,[t,r]){return Math.min(r,Math.max(t,e))}var R=r(70569),T=r(11273),D=r(98599),P=r(8730),E=new WeakMap;function M(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=L(t),i=n>=0?n:r+n;return i<0||i>=r?-1:i}(e,t);return -1===r?void 0:e[r]}function L(e){return e!=e||0===e?0:Math.trunc(e)}var O=i.createContext(void 0),I=r(31355),H=r(1359),_=r(32547),z=r(96963);let U=["top","right","bottom","left"],B=Math.min,K=Math.max,F=Math.round,G=Math.floor,W=e=>({x:e,y:e}),V={left:"right",right:"left",bottom:"top",top:"bottom"},Y={start:"end",end:"start"};function $(e,t){return"function"==typeof e?e(t):e}function q(e){return e.split("-")[0]}function X(e){return e.split("-")[1]}function J(e){return"x"===e?"y":"x"}function Z(e){return"y"===e?"height":"width"}let Q=new Set(["top","bottom"]);function ee(e){return Q.has(q(e))?"y":"x"}function et(e){return e.replace(/start|end/g,e=>Y[e])}let er=["left","right"],en=["right","left"],ei=["top","bottom"],ea=["bottom","top"];function es(e){return e.replace(/left|right|bottom|top/g,e=>V[e])}function el(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function eo(e){let{x:t,y:r,width:n,height:i}=e;return{width:n,height:i,top:r,left:t,right:t+n,bottom:r+i,x:t,y:r}}function ec(e,t,r){let n,{reference:i,floating:a}=e,s=ee(t),l=J(ee(t)),o=Z(l),c=q(t),d="y"===s,u=i.x+i.width/2-a.width/2,f=i.y+i.height/2-a.height/2,p=i[o]/2-a[o]/2;switch(c){case"top":n={x:u,y:i.y-a.height};break;case"bottom":n={x:u,y:i.y+i.height};break;case"right":n={x:i.x+i.width,y:f};break;case"left":n={x:i.x-a.width,y:f};break;default:n={x:i.x,y:i.y}}switch(X(t)){case"start":n[l]-=p*(r&&d?-1:1);break;case"end":n[l]+=p*(r&&d?-1:1)}return n}let ed=async(e,t,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:a=[],platform:s}=r,l=a.filter(Boolean),o=await (null==s.isRTL?void 0:s.isRTL(t)),c=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:d,y:u}=ec(c,n,o),f=n,p={},m=0;for(let r=0;r<l.length;r++){let{name:a,fn:h}=l[r],{x:x,y:g,data:v,reset:y}=await h({x:d,y:u,initialPlacement:n,placement:f,strategy:i,middlewareData:p,rects:c,platform:s,elements:{reference:e,floating:t}});d=null!=x?x:d,u=null!=g?g:u,p={...p,[a]:{...p[a],...v}},y&&m<=50&&(m++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(c=!0===y.rects?await s.getElementRects({reference:e,floating:t,strategy:i}):y.rects),{x:d,y:u}=ec(c,f,o)),r=-1)}return{x:d,y:u,placement:f,strategy:i,middlewareData:p}};async function eu(e,t){var r;void 0===t&&(t={});let{x:n,y:i,platform:a,rects:s,elements:l,strategy:o}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:f=!1,padding:p=0}=$(t,e),m=el(p),h=l[f?"floating"===u?"reference":"floating":u],x=eo(await a.getClippingRect({element:null==(r=await (null==a.isElement?void 0:a.isElement(h)))||r?h:h.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(l.floating)),boundary:c,rootBoundary:d,strategy:o})),g="floating"===u?{x:n,y:i,width:s.floating.width,height:s.floating.height}:s.reference,v=await (null==a.getOffsetParent?void 0:a.getOffsetParent(l.floating)),y=await (null==a.isElement?void 0:a.isElement(v))&&await (null==a.getScale?void 0:a.getScale(v))||{x:1,y:1},w=eo(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:v,strategy:o}):g);return{top:(x.top-w.top+m.top)/y.y,bottom:(w.bottom-x.bottom+m.bottom)/y.y,left:(x.left-w.left+m.left)/y.x,right:(w.right-x.right+m.right)/y.x}}function ef(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ep(e){return U.some(t=>e[t]>=0)}let em=new Set(["left","top"]);async function eh(e,t){let{placement:r,platform:n,elements:i}=e,a=await (null==n.isRTL?void 0:n.isRTL(i.floating)),s=q(r),l=X(r),o="y"===ee(r),c=em.has(s)?-1:1,d=a&&o?-1:1,u=$(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return l&&"number"==typeof m&&(p="end"===l?-1*m:m),o?{x:p*d,y:f*c}:{x:f*c,y:p*d}}function ex(){return"undefined"!=typeof window}function eg(e){return ew(e)?(e.nodeName||"").toLowerCase():"#document"}function ev(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ey(e){var t;return null==(t=(ew(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ew(e){return!!ex()&&(e instanceof Node||e instanceof ev(e).Node)}function eb(e){return!!ex()&&(e instanceof Element||e instanceof ev(e).Element)}function ej(e){return!!ex()&&(e instanceof HTMLElement||e instanceof ev(e).HTMLElement)}function ek(e){return!!ex()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ev(e).ShadowRoot)}let eS=new Set(["inline","contents"]);function eN(e){let{overflow:t,overflowX:r,overflowY:n,display:i}=eI(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!eS.has(i)}let eC=new Set(["table","td","th"]),eA=[":popover-open",":modal"];function eR(e){return eA.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eT=["transform","translate","scale","rotate","perspective"],eD=["transform","translate","scale","rotate","perspective","filter"],eP=["paint","layout","strict","content"];function eE(e){let t=eM(),r=eb(e)?eI(e):e;return eT.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||eD.some(e=>(r.willChange||"").includes(e))||eP.some(e=>(r.contain||"").includes(e))}function eM(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eL=new Set(["html","body","#document"]);function eO(e){return eL.has(eg(e))}function eI(e){return ev(e).getComputedStyle(e)}function eH(e){return eb(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function e_(e){if("html"===eg(e))return e;let t=e.assignedSlot||e.parentNode||ek(e)&&e.host||ey(e);return ek(t)?t.host:t}function ez(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let i=function e(t){let r=e_(t);return eO(r)?t.ownerDocument?t.ownerDocument.body:t.body:ej(r)&&eN(r)?r:e(r)}(e),a=i===(null==(n=e.ownerDocument)?void 0:n.body),s=ev(i);if(a){let e=eU(s);return t.concat(s,s.visualViewport||[],eN(i)?i:[],e&&r?ez(e):[])}return t.concat(i,ez(i,[],r))}function eU(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eB(e){let t=eI(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,i=ej(e),a=i?e.offsetWidth:r,s=i?e.offsetHeight:n,l=F(r)!==a||F(n)!==s;return l&&(r=a,n=s),{width:r,height:n,$:l}}function eK(e){return eb(e)?e:e.contextElement}function eF(e){let t=eK(e);if(!ej(t))return W(1);let r=t.getBoundingClientRect(),{width:n,height:i,$:a}=eB(t),s=(a?F(r.width):r.width)/n,l=(a?F(r.height):r.height)/i;return s&&Number.isFinite(s)||(s=1),l&&Number.isFinite(l)||(l=1),{x:s,y:l}}let eG=W(0);function eW(e){let t=ev(e);return eM()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eG}function eV(e,t,r,n){var i;void 0===t&&(t=!1),void 0===r&&(r=!1);let a=e.getBoundingClientRect(),s=eK(e),l=W(1);t&&(n?eb(n)&&(l=eF(n)):l=eF(e));let o=(void 0===(i=r)&&(i=!1),n&&(!i||n===ev(s))&&i)?eW(s):W(0),c=(a.left+o.x)/l.x,d=(a.top+o.y)/l.y,u=a.width/l.x,f=a.height/l.y;if(s){let e=ev(s),t=n&&eb(n)?ev(n):n,r=e,i=eU(r);for(;i&&n&&t!==r;){let e=eF(i),t=i.getBoundingClientRect(),n=eI(i),a=t.left+(i.clientLeft+parseFloat(n.paddingLeft))*e.x,s=t.top+(i.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,u*=e.x,f*=e.y,c+=a,d+=s,i=eU(r=ev(i))}}return eo({width:u,height:f,x:c,y:d})}function eY(e,t){let r=eH(e).scrollLeft;return t?t.left+r:eV(ey(e)).left+r}function e$(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:eY(e,n)),y:n.top+t.scrollTop}}let eq=new Set(["absolute","fixed"]);function eX(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=ev(e),n=ey(e),i=r.visualViewport,a=n.clientWidth,s=n.clientHeight,l=0,o=0;if(i){a=i.width,s=i.height;let e=eM();(!e||e&&"fixed"===t)&&(l=i.offsetLeft,o=i.offsetTop)}return{width:a,height:s,x:l,y:o}}(e,r);else if("document"===t)n=function(e){let t=ey(e),r=eH(e),n=e.ownerDocument.body,i=K(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=K(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),s=-r.scrollLeft+eY(e),l=-r.scrollTop;return"rtl"===eI(n).direction&&(s+=K(t.clientWidth,n.clientWidth)-i),{width:i,height:a,x:s,y:l}}(ey(e));else if(eb(t))n=function(e,t){let r=eV(e,!0,"fixed"===t),n=r.top+e.clientTop,i=r.left+e.clientLeft,a=ej(e)?eF(e):W(1),s=e.clientWidth*a.x,l=e.clientHeight*a.y;return{width:s,height:l,x:i*a.x,y:n*a.y}}(t,r);else{let r=eW(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return eo(n)}function eJ(e){return"static"===eI(e).position}function eZ(e,t){if(!ej(e)||"fixed"===eI(e).position)return null;if(t)return t(e);let r=e.offsetParent;return ey(e)===r&&(r=r.ownerDocument.body),r}function eQ(e,t){var r;let n=ev(e);if(eR(e))return n;if(!ej(e)){let t=e_(e);for(;t&&!eO(t);){if(eb(t)&&!eJ(t))return t;t=e_(t)}return n}let i=eZ(e,t);for(;i&&(r=i,eC.has(eg(r)))&&eJ(i);)i=eZ(i,t);return i&&eO(i)&&eJ(i)&&!eE(i)?n:i||function(e){let t=e_(e);for(;ej(t)&&!eO(t);){if(eE(t))return t;if(eR(t))break;t=e_(t)}return null}(e)||n}let e0=async function(e){let t=this.getOffsetParent||eQ,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=ej(t),i=ey(t),a="fixed"===r,s=eV(e,!0,a,t),l={scrollLeft:0,scrollTop:0},o=W(0);if(n||!n&&!a)if(("body"!==eg(t)||eN(i))&&(l=eH(t)),n){let e=eV(t,!0,a,t);o.x=e.x+t.clientLeft,o.y=e.y+t.clientTop}else i&&(o.x=eY(i));a&&!n&&i&&(o.x=eY(i));let c=!i||n||a?W(0):e$(i,l);return{x:s.left+l.scrollLeft-o.x-c.x,y:s.top+l.scrollTop-o.y-c.y,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},e1={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:i}=e,a="fixed"===i,s=ey(n),l=!!t&&eR(t.floating);if(n===s||l&&a)return r;let o={scrollLeft:0,scrollTop:0},c=W(1),d=W(0),u=ej(n);if((u||!u&&!a)&&(("body"!==eg(n)||eN(s))&&(o=eH(n)),ej(n))){let e=eV(n);c=eF(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let f=!s||u||a?W(0):e$(s,o,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-o.scrollLeft*c.x+d.x+f.x,y:r.y*c.y-o.scrollTop*c.y+d.y+f.y}},getDocumentElement:ey,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:i}=e,a=[..."clippingAncestors"===r?eR(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=ez(e,[],!1).filter(e=>eb(e)&&"body"!==eg(e)),i=null,a="fixed"===eI(e).position,s=a?e_(e):e;for(;eb(s)&&!eO(s);){let t=eI(s),r=eE(s);r||"fixed"!==t.position||(i=null),(a?!r&&!i:!r&&"static"===t.position&&!!i&&eq.has(i.position)||eN(s)&&!r&&function e(t,r){let n=e_(t);return!(n===r||!eb(n)||eO(n))&&("fixed"===eI(n).position||e(n,r))}(e,s))?n=n.filter(e=>e!==s):i=t,s=e_(s)}return t.set(e,n),n}(t,this._c):[].concat(r),n],s=a[0],l=a.reduce((e,r)=>{let n=eX(t,r,i);return e.top=K(n.top,e.top),e.right=B(n.right,e.right),e.bottom=B(n.bottom,e.bottom),e.left=K(n.left,e.left),e},eX(t,s,i));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eQ,getElementRects:e0,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=eB(e);return{width:t,height:r}},getScale:eF,isElement:eb,isRTL:function(e){return"rtl"===eI(e).direction}};function e2(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e4=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:i,rects:a,platform:s,elements:l,middlewareData:o}=t,{element:c,padding:d=0}=$(e,t)||{};if(null==c)return{};let u=el(d),f={x:r,y:n},p=J(ee(i)),m=Z(p),h=await s.getDimensions(c),x="y"===p,g=x?"clientHeight":"clientWidth",v=a.reference[m]+a.reference[p]-f[p]-a.floating[m],y=f[p]-a.reference[p],w=await (null==s.getOffsetParent?void 0:s.getOffsetParent(c)),b=w?w[g]:0;b&&await (null==s.isElement?void 0:s.isElement(w))||(b=l.floating[g]||a.floating[m]);let j=b/2-h[m]/2-1,k=B(u[x?"top":"left"],j),S=B(u[x?"bottom":"right"],j),N=b-h[m]-S,C=b/2-h[m]/2+(v/2-y/2),A=K(k,B(C,N)),R=!o.arrow&&null!=X(i)&&C!==A&&a.reference[m]/2-(C<k?k:S)-h[m]/2<0,T=R?C<k?C-k:C-N:0;return{[p]:f[p]+T,data:{[p]:A,centerOffset:C-A-T,...R&&{alignmentOffset:T}},reset:R}}}),e3=(e,t,r)=>{let n=new Map,i={platform:e1,...r},a={...i.platform,_c:n};return ed(e,t,{...i,platform:a})};var e5="undefined"!=typeof document?i.useLayoutEffect:function(){};function e8(e,t){let r,n,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!e8(e[n],t[n]))return!1;return!0}if((r=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,i[n]))return!1;for(n=r;0!=n--;){let r=i[n];if(("_owner"!==r||!e.$$typeof)&&!e8(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function e6(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e9(e,t){let r=e6(e);return Math.round(t*r)/r}function e7(e){let t=i.useRef(e);return e5(()=>{t.current=e}),t}let te=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?e4({element:r.current,padding:n}).fn(t):{}:r?e4({element:r,padding:n}).fn(t):{}}}),tt=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:i,y:a,placement:s,middlewareData:l}=t,o=await eh(t,e);return s===(null==(r=l.offset)?void 0:r.placement)&&null!=(n=l.arrow)&&n.alignmentOffset?{}:{x:i+o.x,y:a+o.y,data:{...o,placement:s}}}}}(e),options:[e,t]}),tr=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:i}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:l={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...o}=$(e,t),c={x:r,y:n},d=await eu(t,o),u=ee(q(i)),f=J(u),p=c[f],m=c[u];if(a){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+d[e],n=p-d[t];p=K(r,B(p,n))}if(s){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",r=m+d[e],n=m-d[t];m=K(r,B(m,n))}let h=l.fn({...t,[f]:p,[u]:m});return{...h,data:{x:h.x-r,y:h.y-n,enabled:{[f]:a,[u]:s}}}}}}(e),options:[e,t]}),tn=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:i,rects:a,middlewareData:s}=t,{offset:l=0,mainAxis:o=!0,crossAxis:c=!0}=$(e,t),d={x:r,y:n},u=ee(i),f=J(u),p=d[f],m=d[u],h=$(l,t),x="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(o){let e="y"===f?"height":"width",t=a.reference[f]-a.floating[e]+x.mainAxis,r=a.reference[f]+a.reference[e]-x.mainAxis;p<t?p=t:p>r&&(p=r)}if(c){var g,v;let e="y"===f?"width":"height",t=em.has(q(i)),r=a.reference[u]-a.floating[e]+(t&&(null==(g=s.offset)?void 0:g[u])||0)+(t?0:x.crossAxis),n=a.reference[u]+a.reference[e]+(t?0:(null==(v=s.offset)?void 0:v[u])||0)-(t?x.crossAxis:0);m<r?m=r:m>n&&(m=n)}return{[f]:p,[u]:m}}}}(e),options:[e,t]}),ti=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,i,a,s;let{placement:l,middlewareData:o,rects:c,initialPlacement:d,platform:u,elements:f}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:v=!0,...y}=$(e,t);if(null!=(r=o.arrow)&&r.alignmentOffset)return{};let w=q(l),b=ee(d),j=q(d)===d,k=await (null==u.isRTL?void 0:u.isRTL(f.floating)),S=h||(j||!v?[es(d)]:function(e){let t=es(e);return[et(e),t,et(t)]}(d)),N="none"!==g;!h&&N&&S.push(...function(e,t,r,n){let i=X(e),a=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?en:er;return t?er:en;case"left":case"right":return t?ei:ea;default:return[]}}(q(e),"start"===r,n);return i&&(a=a.map(e=>e+"-"+i),t&&(a=a.concat(a.map(et)))),a}(d,v,g,k));let C=[d,...S],A=await eu(t,y),R=[],T=(null==(n=o.flip)?void 0:n.overflows)||[];if(p&&R.push(A[w]),m){let e=function(e,t,r){void 0===r&&(r=!1);let n=X(e),i=J(ee(e)),a=Z(i),s="x"===i?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[a]>t.floating[a]&&(s=es(s)),[s,es(s)]}(l,c,k);R.push(A[e[0]],A[e[1]])}if(T=[...T,{placement:l,overflows:R}],!R.every(e=>e<=0)){let e=((null==(i=o.flip)?void 0:i.index)||0)+1,t=C[e];if(t&&("alignment"!==m||b===ee(t)||T.every(e=>e.overflows[0]>0&&ee(e.placement)===b)))return{data:{index:e,overflows:T},reset:{placement:t}};let r=null==(a=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!r)switch(x){case"bestFit":{let e=null==(s=T.filter(e=>{if(N){let t=ee(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(r=e);break}case"initialPlacement":r=d}if(l!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),ta=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let i,a,{placement:s,rects:l,platform:o,elements:c}=t,{apply:d=()=>{},...u}=$(e,t),f=await eu(t,u),p=q(s),m=X(s),h="y"===ee(s),{width:x,height:g}=l.floating;"top"===p||"bottom"===p?(i=p,a=m===(await (null==o.isRTL?void 0:o.isRTL(c.floating))?"start":"end")?"left":"right"):(a=p,i="end"===m?"top":"bottom");let v=g-f.top-f.bottom,y=x-f.left-f.right,w=B(g-f[i],v),b=B(x-f[a],y),j=!t.middlewareData.shift,k=w,S=b;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(S=y),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(k=v),j&&!m){let e=K(f.left,0),t=K(f.right,0),r=K(f.top,0),n=K(f.bottom,0);h?S=x-2*(0!==e||0!==t?e+t:K(f.left,f.right)):k=g-2*(0!==r||0!==n?r+n:K(f.top,f.bottom))}await d({...t,availableWidth:S,availableHeight:k});let N=await o.getDimensions(c.floating);return x!==N.width||g!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ts=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...i}=$(e,t);switch(n){case"referenceHidden":{let e=ef(await eu(t,{...i,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ep(e)}}}case"escaped":{let e=ef(await eu(t,{...i,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:ep(e)}}}default:return{}}}}}(e),options:[e,t]}),tl=(e,t)=>({...te(e),options:[e,t]});var to=r(14163),tc=i.forwardRef((e,t)=>{let{children:r,width:i=10,height:a=5,...s}=e;return(0,n.jsx)(to.sG.svg,{...s,ref:t,width:i,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,n.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tc.displayName="Arrow";var td=r(13495),tu=r(66156),tf=r(18853),tp="Popper",[tm,th]=(0,T.A)(tp),[tx,tg]=tm(tp),tv=e=>{let{__scopePopper:t,children:r}=e,[a,s]=i.useState(null);return(0,n.jsx)(tx,{scope:t,anchor:a,onAnchorChange:s,children:r})};tv.displayName=tp;var ty="PopperAnchor",tw=i.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:a,...s}=e,l=tg(ty,r),o=i.useRef(null),c=(0,D.s)(t,o);return i.useEffect(()=>{l.onAnchorChange(a?.current||o.current)}),a?null:(0,n.jsx)(to.sG.div,{...s,ref:c})});tw.displayName=ty;var tb="PopperContent",[tj,tk]=tm(tb),tS=i.forwardRef((e,t)=>{let{__scopePopper:r,side:a="bottom",sideOffset:s=0,align:l="center",alignOffset:o=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:m=!1,updatePositionStrategy:h="optimized",onPlaced:x,...g}=e,v=tg(tb,r),[y,w]=i.useState(null),b=(0,D.s)(t,e=>w(e)),[j,k]=i.useState(null),S=(0,tf.X)(j),N=S?.width??0,A=S?.height??0,R="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},T=Array.isArray(u)?u:[u],P=T.length>0,E={padding:R,boundary:T.filter(tR),altBoundary:P},{refs:M,floatingStyles:L,placement:O,isPositioned:I,middlewareData:H}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:a,elements:{reference:s,floating:l}={},transform:o=!0,whileElementsMounted:c,open:d}=e,[u,f]=i.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=i.useState(n);e8(p,n)||m(n);let[h,x]=i.useState(null),[g,v]=i.useState(null),y=i.useCallback(e=>{e!==k.current&&(k.current=e,x(e))},[]),w=i.useCallback(e=>{e!==S.current&&(S.current=e,v(e))},[]),b=s||h,j=l||g,k=i.useRef(null),S=i.useRef(null),N=i.useRef(u),A=null!=c,R=e7(c),T=e7(a),D=e7(d),P=i.useCallback(()=>{if(!k.current||!S.current)return;let e={placement:t,strategy:r,middleware:p};T.current&&(e.platform=T.current),e3(k.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==D.current};E.current&&!e8(N.current,t)&&(N.current=t,C.flushSync(()=>{f(t)}))})},[p,t,r,T,D]);e5(()=>{!1===d&&N.current.isPositioned&&(N.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[d]);let E=i.useRef(!1);e5(()=>(E.current=!0,()=>{E.current=!1}),[]),e5(()=>{if(b&&(k.current=b),j&&(S.current=j),b&&j){if(R.current)return R.current(b,j,P);P()}},[b,j,P,R,A]);let M=i.useMemo(()=>({reference:k,floating:S,setReference:y,setFloating:w}),[y,w]),L=i.useMemo(()=>({reference:b,floating:j}),[b,j]),O=i.useMemo(()=>{let e={position:r,left:0,top:0};if(!L.floating)return e;let t=e9(L.floating,u.x),n=e9(L.floating,u.y);return o?{...e,transform:"translate("+t+"px, "+n+"px)",...e6(L.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,o,L.floating,u.x,u.y]);return i.useMemo(()=>({...u,update:P,refs:M,elements:L,floatingStyles:O}),[u,P,M,L,O])}({strategy:"fixed",placement:a+("center"!==l?"-"+l:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let i;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:o="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,d=eK(e),u=a||s?[...d?ez(d):[],...ez(t)]:[];u.forEach(e=>{a&&e.addEventListener("scroll",r,{passive:!0}),s&&e.addEventListener("resize",r)});let f=d&&o?function(e,t){let r,n=null,i=ey(e);function a(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function s(l,o){void 0===l&&(l=!1),void 0===o&&(o=1),a();let c=e.getBoundingClientRect(),{left:d,top:u,width:f,height:p}=c;if(l||t(),!f||!p)return;let m=G(u),h=G(i.clientWidth-(d+f)),x={rootMargin:-m+"px "+-h+"px "+-G(i.clientHeight-(u+p))+"px "+-G(d)+"px",threshold:K(0,B(1,o))||1},g=!0;function v(t){let n=t[0].intersectionRatio;if(n!==o){if(!g)return s();n?s(!1,n):r=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==n||e2(c,e.getBoundingClientRect())||s(),g=!1}try{n=new IntersectionObserver(v,{...x,root:i.ownerDocument})}catch(e){n=new IntersectionObserver(v,x)}n.observe(e)}(!0),a}(d,r):null,p=-1,m=null;l&&(m=new ResizeObserver(e=>{let[n]=e;n&&n.target===d&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),r()}),d&&!c&&m.observe(d),m.observe(t));let h=c?eV(e):null;return c&&function t(){let n=eV(e);h&&!e2(h,n)&&r(),h=n,i=requestAnimationFrame(t)}(),r(),()=>{var e;u.forEach(e=>{a&&e.removeEventListener("scroll",r),s&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=m)||e.disconnect(),m=null,c&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===h}),elements:{reference:v.anchor},middleware:[tt({mainAxis:s+A,alignmentAxis:o}),d&&tr({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?tn():void 0,...E}),d&&ti({...E}),ta({...E,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:i,height:a}=t.reference,s=e.floating.style;s.setProperty("--radix-popper-available-width",`${r}px`),s.setProperty("--radix-popper-available-height",`${n}px`),s.setProperty("--radix-popper-anchor-width",`${i}px`),s.setProperty("--radix-popper-anchor-height",`${a}px`)}}),j&&tl({element:j,padding:c}),tT({arrowWidth:N,arrowHeight:A}),m&&ts({strategy:"referenceHidden",...E})]}),[_,z]=tD(O),U=(0,td.c)(x);(0,tu.N)(()=>{I&&U?.()},[I,U]);let F=H.arrow?.x,W=H.arrow?.y,V=H.arrow?.centerOffset!==0,[Y,$]=i.useState();return(0,tu.N)(()=>{y&&$(window.getComputedStyle(y).zIndex)},[y]),(0,n.jsx)("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...L,transform:I?L.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[H.transformOrigin?.x,H.transformOrigin?.y].join(" "),...H.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,n.jsx)(tj,{scope:r,placedSide:_,onArrowChange:k,arrowX:F,arrowY:W,shouldHideArrow:V,children:(0,n.jsx)(to.sG.div,{"data-side":_,"data-align":z,...g,ref:b,style:{...g.style,animation:I?void 0:"none"}})})})});tS.displayName=tb;var tN="PopperArrow",tC={top:"bottom",right:"left",bottom:"top",left:"right"},tA=i.forwardRef(function(e,t){let{__scopePopper:r,...i}=e,a=tk(tN,r),s=tC[a.placedSide];return(0,n.jsx)("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:(0,n.jsx)(tc,{...i,ref:t,style:{...i.style,display:"block"}})})});function tR(e){return null!==e}tA.displayName=tN;var tT=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:i}=t,a=i.arrow?.centerOffset!==0,s=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[o,c]=tD(r),d={start:"0%",center:"50%",end:"100%"}[c],u=(i.arrow?.x??0)+s/2,f=(i.arrow?.y??0)+l/2,p="",m="";return"bottom"===o?(p=a?d:`${u}px`,m=`${-l}px`):"top"===o?(p=a?d:`${u}px`,m=`${n.floating.height+l}px`):"right"===o?(p=`${-l}px`,m=a?d:`${f}px`):"left"===o&&(p=`${n.floating.width+l}px`,m=a?d:`${f}px`),{data:{x:p,y:m}}}});function tD(e){let[t,r="center"]=e.split("-");return[t,r]}var tP=r(25028),tE=r(65551),tM=r(83721),tL=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});i.forwardRef((e,t)=>(0,n.jsx)(to.sG.span,{...e,ref:t,style:{...tL,...e.style}})).displayName="VisuallyHidden";var tO=r(63376),tI=r(42247),tH=[" ","Enter","ArrowUp","ArrowDown"],t_=[" ","Enter"],tz="Select",[tU,tB,tK]=function(e){let t=e+"CollectionProvider",[r,a]=(0,T.A)(t),[s,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),o=e=>{let{scope:t,children:r}=e,a=i.useRef(null),l=i.useRef(new Map).current;return(0,n.jsx)(s,{scope:t,itemMap:l,collectionRef:a,children:r})};o.displayName=t;let c=e+"CollectionSlot",d=(0,P.TL)(c),u=i.forwardRef((e,t)=>{let{scope:r,children:i}=e,a=l(c,r),s=(0,D.s)(t,a.collectionRef);return(0,n.jsx)(d,{ref:s,children:i})});u.displayName=c;let f=e+"CollectionItemSlot",p="data-radix-collection-item",m=(0,P.TL)(f),h=i.forwardRef((e,t)=>{let{scope:r,children:a,...s}=e,o=i.useRef(null),c=(0,D.s)(t,o),d=l(f,r);return i.useEffect(()=>(d.itemMap.set(o,{ref:o,...s}),()=>void d.itemMap.delete(o))),(0,n.jsx)(m,{...{[p]:""},ref:c,children:a})});return h.displayName=f,[{Provider:o,Slot:u,ItemSlot:h},function(t){let r=l(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}(tz),[tF,tG]=(0,T.A)(tz,[tK,th]),tW=th(),[tV,tY]=tF(tz),[t$,tq]=tF(tz),tX=e=>{let{__scopeSelect:t,children:r,open:a,defaultOpen:s,onOpenChange:l,value:o,defaultValue:c,onValueChange:d,dir:u,name:f,autoComplete:p,disabled:m,required:h,form:x}=e,g=tW(t),[v,y]=i.useState(null),[w,b]=i.useState(null),[j,k]=i.useState(!1),S=function(e){let t=i.useContext(O);return e||t||"ltr"}(u),[N,C]=(0,tE.i)({prop:a,defaultProp:s??!1,onChange:l,caller:tz}),[A,R]=(0,tE.i)({prop:o,defaultProp:c,onChange:d,caller:tz}),T=i.useRef(null),D=!v||x||!!v.closest("form"),[P,E]=i.useState(new Set),M=Array.from(P).map(e=>e.props.value).join(";");return(0,n.jsx)(tv,{...g,children:(0,n.jsxs)(tV,{required:h,scope:t,trigger:v,onTriggerChange:y,valueNode:w,onValueNodeChange:b,valueNodeHasChildren:j,onValueNodeHasChildrenChange:k,contentId:(0,z.B)(),value:A,onValueChange:R,open:N,onOpenChange:C,dir:S,triggerPointerDownPosRef:T,disabled:m,children:[(0,n.jsx)(tU.Provider,{scope:t,children:(0,n.jsx)(t$,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(e=>{E(t=>new Set(t).add(e))},[]),onNativeOptionRemove:i.useCallback(e=>{E(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),D?(0,n.jsxs)(rk,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:p,value:A,onChange:e=>R(e.target.value),disabled:m,form:x,children:[void 0===A?(0,n.jsx)("option",{value:""}):null,Array.from(P)]},M):null]})})};tX.displayName=tz;var tJ="SelectTrigger",tZ=i.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:a=!1,...s}=e,l=tW(r),o=tY(tJ,r),c=o.disabled||a,d=(0,D.s)(t,o.onTriggerChange),u=tB(r),f=i.useRef("touch"),[p,m,h]=rN(e=>{let t=u().filter(e=>!e.disabled),r=t.find(e=>e.value===o.value),n=rC(t,e,r);void 0!==n&&o.onValueChange(n.value)}),x=e=>{c||(o.onOpenChange(!0),h()),e&&(o.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,n.jsx)(tw,{asChild:!0,...l,children:(0,n.jsx)(to.sG.button,{type:"button",role:"combobox","aria-controls":o.contentId,"aria-expanded":o.open,"aria-required":o.required,"aria-autocomplete":"none",dir:o.dir,"data-state":o.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":rS(o.value)?"":void 0,...s,ref:d,onClick:(0,R.m)(s.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,R.m)(s.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,R.m)(s.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&tH.includes(e.key)&&(x(),e.preventDefault())})})})});tZ.displayName=tJ;var tQ="SelectValue",t0=i.forwardRef((e,t)=>{let{__scopeSelect:r,className:i,style:a,children:s,placeholder:l="",...o}=e,c=tY(tQ,r),{onValueNodeHasChildrenChange:d}=c,u=void 0!==s,f=(0,D.s)(t,c.onValueNodeChange);return(0,tu.N)(()=>{d(u)},[d,u]),(0,n.jsx)(to.sG.span,{...o,ref:f,style:{pointerEvents:"none"},children:rS(c.value)?(0,n.jsx)(n.Fragment,{children:l}):s})});t0.displayName=tQ;var t1=i.forwardRef((e,t)=>{let{__scopeSelect:r,children:i,...a}=e;return(0,n.jsx)(to.sG.span,{"aria-hidden":!0,...a,ref:t,children:i||"▼"})});t1.displayName="SelectIcon";var t2=e=>(0,n.jsx)(tP.Z,{asChild:!0,...e});t2.displayName="SelectPortal";var t4="SelectContent",t3=i.forwardRef((e,t)=>{let r=tY(t4,e.__scopeSelect),[a,s]=i.useState();return((0,tu.N)(()=>{s(new DocumentFragment)},[]),r.open)?(0,n.jsx)(t9,{...e,ref:t}):a?C.createPortal((0,n.jsx)(t5,{scope:e.__scopeSelect,children:(0,n.jsx)(tU.Slot,{scope:e.__scopeSelect,children:(0,n.jsx)("div",{children:e.children})})}),a):null});t3.displayName=t4;var[t5,t8]=tF(t4),t6=(0,P.TL)("SelectContent.RemoveScroll"),t9=i.forwardRef((e,t)=>{let{__scopeSelect:r,position:a="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:l,onPointerDownOutside:o,side:c,sideOffset:d,align:u,alignOffset:f,arrowPadding:p,collisionBoundary:m,collisionPadding:h,sticky:x,hideWhenDetached:g,avoidCollisions:v,...y}=e,w=tY(t4,r),[b,j]=i.useState(null),[k,S]=i.useState(null),N=(0,D.s)(t,e=>j(e)),[C,A]=i.useState(null),[T,P]=i.useState(null),E=tB(r),[M,L]=i.useState(!1),O=i.useRef(!1);i.useEffect(()=>{if(b)return(0,tO.Eq)(b)},[b]),(0,H.Oh)();let z=i.useCallback(e=>{let[t,...r]=E().map(e=>e.ref.current),[n]=r.slice(-1),i=document.activeElement;for(let r of e)if(r===i||(r?.scrollIntoView({block:"nearest"}),r===t&&k&&(k.scrollTop=0),r===n&&k&&(k.scrollTop=k.scrollHeight),r?.focus(),document.activeElement!==i))return},[E,k]),U=i.useCallback(()=>z([C,b]),[z,C,b]);i.useEffect(()=>{M&&U()},[M,U]);let{onOpenChange:B,triggerPointerDownPosRef:K}=w;i.useEffect(()=>{if(b){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(K.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(K.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():b.contains(r.target)||B(!1),document.removeEventListener("pointermove",t),K.current=null};return null!==K.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[b,B,K]),i.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[F,G]=rN(e=>{let t=E().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=rC(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),W=i.useCallback((e,t,r)=>{let n=!O.current&&!r;(void 0!==w.value&&w.value===t||n)&&(A(e),n&&(O.current=!0))},[w.value]),V=i.useCallback(()=>b?.focus(),[b]),Y=i.useCallback((e,t,r)=>{let n=!O.current&&!r;(void 0!==w.value&&w.value===t||n)&&P(e)},[w.value]),$="popper"===a?re:t7,q=$===re?{side:c,sideOffset:d,align:u,alignOffset:f,arrowPadding:p,collisionBoundary:m,collisionPadding:h,sticky:x,hideWhenDetached:g,avoidCollisions:v}:{};return(0,n.jsx)(t5,{scope:r,content:b,viewport:k,onViewportChange:S,itemRefCallback:W,selectedItem:C,onItemLeave:V,itemTextRefCallback:Y,focusSelectedItem:U,selectedItemText:T,position:a,isPositioned:M,searchRef:F,children:(0,n.jsx)(tI.A,{as:t6,allowPinchZoom:!0,children:(0,n.jsx)(_.n,{asChild:!0,trapped:w.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,R.m)(s,e=>{w.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,n.jsx)(I.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:o,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>w.onOpenChange(!1),children:(0,n.jsx)($,{role:"listbox",id:w.contentId,"data-state":w.open?"open":"closed",dir:w.dir,onContextMenu:e=>e.preventDefault(),...y,...q,onPlaced:()=>L(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:(0,R.m)(y.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=E().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});t9.displayName="SelectContentImpl";var t7=i.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:a,...s}=e,l=tY(t4,r),o=t8(t4,r),[c,d]=i.useState(null),[u,f]=i.useState(null),p=(0,D.s)(t,e=>f(e)),m=tB(r),h=i.useRef(!1),x=i.useRef(!0),{viewport:g,selectedItem:v,selectedItemText:y,focusSelectedItem:w}=o,b=i.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&u&&g&&v&&y){let e=l.trigger.getBoundingClientRect(),t=u.getBoundingClientRect(),r=l.valueNode.getBoundingClientRect(),n=y.getBoundingClientRect();if("rtl"!==l.dir){let i=n.left-t.left,a=r.left-i,s=e.left-a,l=e.width+s,o=Math.max(l,t.width),d=A(a,[10,Math.max(10,window.innerWidth-10-o)]);c.style.minWidth=l+"px",c.style.left=d+"px"}else{let i=t.right-n.right,a=window.innerWidth-r.right-i,s=window.innerWidth-e.right-a,l=e.width+s,o=Math.max(l,t.width),d=A(a,[10,Math.max(10,window.innerWidth-10-o)]);c.style.minWidth=l+"px",c.style.right=d+"px"}let i=m(),s=window.innerHeight-20,o=g.scrollHeight,d=window.getComputedStyle(u),f=parseInt(d.borderTopWidth,10),p=parseInt(d.paddingTop,10),x=parseInt(d.borderBottomWidth,10),w=f+p+o+parseInt(d.paddingBottom,10)+x,b=Math.min(5*v.offsetHeight,w),j=window.getComputedStyle(g),k=parseInt(j.paddingTop,10),S=parseInt(j.paddingBottom,10),N=e.top+e.height/2-10,C=v.offsetHeight/2,R=f+p+(v.offsetTop+C);if(R<=N){let e=i.length>0&&v===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-N,C+(e?S:0)+(u.clientHeight-g.offsetTop-g.offsetHeight)+x);c.style.height=R+t+"px"}else{let e=i.length>0&&v===i[0].ref.current;c.style.top="0px";let t=Math.max(N,f+g.offsetTop+(e?k:0)+C);c.style.height=t+(w-R)+"px",g.scrollTop=R-N+g.offsetTop}c.style.margin="10px 0",c.style.minHeight=b+"px",c.style.maxHeight=s+"px",a?.(),requestAnimationFrame(()=>h.current=!0)}},[m,l.trigger,l.valueNode,c,u,g,v,y,l.dir,a]);(0,tu.N)(()=>b(),[b]);let[j,k]=i.useState();(0,tu.N)(()=>{u&&k(window.getComputedStyle(u).zIndex)},[u]);let S=i.useCallback(e=>{e&&!0===x.current&&(b(),w?.(),x.current=!1)},[b,w]);return(0,n.jsx)(rt,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:S,children:(0,n.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,n.jsx)(to.sG.div,{...s,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})});t7.displayName="SelectItemAlignedPosition";var re=i.forwardRef((e,t)=>{let{__scopeSelect:r,align:i="start",collisionPadding:a=10,...s}=e,l=tW(r);return(0,n.jsx)(tS,{...l,...s,ref:t,align:i,collisionPadding:a,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});re.displayName="SelectPopperPosition";var[rt,rr]=tF(t4,{}),rn="SelectViewport",ri=i.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:a,...s}=e,l=t8(rn,r),o=rr(rn,r),c=(0,D.s)(t,l.onViewportChange),d=i.useRef(0);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,n.jsx)(tU.Slot,{scope:r,children:(0,n.jsx)(to.sG.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:(0,R.m)(s.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=o;if(n?.current&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,i=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(i<n){let a=i+e,s=Math.min(n,a),l=a-s;r.style.height=s+"px","0px"===r.style.bottom&&(t.scrollTop=l>0?l:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});ri.displayName=rn;var ra="SelectGroup",[rs,rl]=tF(ra);i.forwardRef((e,t)=>{let{__scopeSelect:r,...i}=e,a=(0,z.B)();return(0,n.jsx)(rs,{scope:r,id:a,children:(0,n.jsx)(to.sG.div,{role:"group","aria-labelledby":a,...i,ref:t})})}).displayName=ra;var ro="SelectLabel";i.forwardRef((e,t)=>{let{__scopeSelect:r,...i}=e,a=rl(ro,r);return(0,n.jsx)(to.sG.div,{id:a.id,...i,ref:t})}).displayName=ro;var rc="SelectItem",[rd,ru]=tF(rc),rf=i.forwardRef((e,t)=>{let{__scopeSelect:r,value:a,disabled:s=!1,textValue:l,...o}=e,c=tY(rc,r),d=t8(rc,r),u=c.value===a,[f,p]=i.useState(l??""),[m,h]=i.useState(!1),x=(0,D.s)(t,e=>d.itemRefCallback?.(e,a,s)),g=(0,z.B)(),v=i.useRef("touch"),y=()=>{s||(c.onValueChange(a),c.onOpenChange(!1))};if(""===a)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,n.jsx)(rd,{scope:r,value:a,disabled:s,textId:g,isSelected:u,onItemTextChange:i.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,n.jsx)(tU.ItemSlot,{scope:r,value:a,disabled:s,textValue:f,children:(0,n.jsx)(to.sG.div,{role:"option","aria-labelledby":g,"data-highlighted":m?"":void 0,"aria-selected":u&&m,"data-state":u?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...o,ref:x,onFocus:(0,R.m)(o.onFocus,()=>h(!0)),onBlur:(0,R.m)(o.onBlur,()=>h(!1)),onClick:(0,R.m)(o.onClick,()=>{"mouse"!==v.current&&y()}),onPointerUp:(0,R.m)(o.onPointerUp,()=>{"mouse"===v.current&&y()}),onPointerDown:(0,R.m)(o.onPointerDown,e=>{v.current=e.pointerType}),onPointerMove:(0,R.m)(o.onPointerMove,e=>{v.current=e.pointerType,s?d.onItemLeave?.():"mouse"===v.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,R.m)(o.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,R.m)(o.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(t_.includes(e.key)&&y()," "===e.key&&e.preventDefault())})})})})});rf.displayName=rc;var rp="SelectItemText",rm=i.forwardRef((e,t)=>{let{__scopeSelect:r,className:a,style:s,...l}=e,o=tY(rp,r),c=t8(rp,r),d=ru(rp,r),u=tq(rp,r),[f,p]=i.useState(null),m=(0,D.s)(t,e=>p(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),h=f?.textContent,x=i.useMemo(()=>(0,n.jsx)("option",{value:d.value,disabled:d.disabled,children:h},d.value),[d.disabled,d.value,h]),{onNativeOptionAdd:g,onNativeOptionRemove:v}=u;return(0,tu.N)(()=>(g(x),()=>v(x)),[g,v,x]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(to.sG.span,{id:d.textId,...l,ref:m}),d.isSelected&&o.valueNode&&!o.valueNodeHasChildren?C.createPortal(l.children,o.valueNode):null]})});rm.displayName=rp;var rh="SelectItemIndicator",rx=i.forwardRef((e,t)=>{let{__scopeSelect:r,...i}=e;return ru(rh,r).isSelected?(0,n.jsx)(to.sG.span,{"aria-hidden":!0,...i,ref:t}):null});rx.displayName=rh;var rg="SelectScrollUpButton",rv=i.forwardRef((e,t)=>{let r=t8(rg,e.__scopeSelect),a=rr(rg,e.__scopeSelect),[s,l]=i.useState(!1),o=(0,D.s)(t,a.onScrollButtonChange);return(0,tu.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){l(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),s?(0,n.jsx)(rb,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});rv.displayName=rg;var ry="SelectScrollDownButton",rw=i.forwardRef((e,t)=>{let r=t8(ry,e.__scopeSelect),a=rr(ry,e.__scopeSelect),[s,l]=i.useState(!1),o=(0,D.s)(t,a.onScrollButtonChange);return(0,tu.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),s?(0,n.jsx)(rb,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});rw.displayName=ry;var rb=i.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:a,...s}=e,l=t8("SelectScrollButton",r),o=i.useRef(null),c=tB(r),d=i.useCallback(()=>{null!==o.current&&(window.clearInterval(o.current),o.current=null)},[]);return i.useEffect(()=>()=>d(),[d]),(0,tu.N)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,n.jsx)(to.sG.div,{"aria-hidden":!0,...s,ref:t,style:{flexShrink:0,...s.style},onPointerDown:(0,R.m)(s.onPointerDown,()=>{null===o.current&&(o.current=window.setInterval(a,50))}),onPointerMove:(0,R.m)(s.onPointerMove,()=>{l.onItemLeave?.(),null===o.current&&(o.current=window.setInterval(a,50))}),onPointerLeave:(0,R.m)(s.onPointerLeave,()=>{d()})})});i.forwardRef((e,t)=>{let{__scopeSelect:r,...i}=e;return(0,n.jsx)(to.sG.div,{"aria-hidden":!0,...i,ref:t})}).displayName="SelectSeparator";var rj="SelectArrow";i.forwardRef((e,t)=>{let{__scopeSelect:r,...i}=e,a=tW(r),s=tY(rj,r),l=t8(rj,r);return s.open&&"popper"===l.position?(0,n.jsx)(tA,{...a,...i,ref:t}):null}).displayName=rj;var rk=i.forwardRef(({__scopeSelect:e,value:t,...r},a)=>{let s=i.useRef(null),l=(0,D.s)(a,s),o=(0,tM.Z)(t);return i.useEffect(()=>{let e=s.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(o!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[o,t]),(0,n.jsx)(to.sG.select,{...r,style:{...tL,...r.style},ref:l,defaultValue:t})});function rS(e){return""===e||void 0===e}function rN(e){let t=(0,td.c)(e),r=i.useRef(""),n=i.useRef(0),a=i.useCallback(e=>{let i=r.current+e;t(i),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(i)},[t]),s=i.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,a,s]}function rC(e,t,r){var n,i;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,s=r?e.indexOf(r):-1,l=(n=e,i=Math.max(s,0),n.map((e,t)=>n[(i+t)%n.length]));1===a.length&&(l=l.filter(e=>e!==r));let o=l.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return o!==r?o:void 0}rk.displayName="SelectBubbleInput";var rA=r(62688);let rR=(0,rA.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var rT=r(13964);let rD=(0,rA.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);function rP({...e}){return(0,n.jsx)(tX,{"data-slot":"select",...e})}function rE({...e}){return(0,n.jsx)(t0,{"data-slot":"select-value",...e})}function rM({className:e,size:t="default",children:r,...i}){return(0,n.jsxs)(tZ,{"data-slot":"select-trigger","data-size":t,className:(0,g.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[r,(0,n.jsx)(t1,{asChild:!0,children:(0,n.jsx)(rR,{className:"size-4 opacity-50"})})]})}function rL({className:e,children:t,position:r="popper",...i}){return(0,n.jsx)(t2,{children:(0,n.jsxs)(t3,{"data-slot":"select-content",className:(0,g.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...i,children:[(0,n.jsx)(rI,{}),(0,n.jsx)(ri,{className:(0,g.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,n.jsx)(rH,{})]})})}function rO({className:e,children:t,...r}){return(0,n.jsxs)(rf,{"data-slot":"select-item",className:(0,g.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,n.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(rx,{children:(0,n.jsx)(rT.A,{className:"size-4"})})}),(0,n.jsx)(rm,{children:t})]})}function rI({className:e,...t}){return(0,n.jsx)(rv,{"data-slot":"select-scroll-up-button",className:(0,g.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(rD,{className:"size-4"})})}function rH({className:e,...t}){return(0,n.jsx)(rw,{"data-slot":"select-scroll-down-button",className:(0,g.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(rR,{className:"size-4"})})}var r_=r(24236),rz=r(35879),rU=r(9263),rB=r(23026),rK=r(41862);let rF=(0,rA.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),rG=(0,rA.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function rW(){let{data:e,update:t}=(0,l.useSession)(),r=(0,r_.X)(),[a,h]=(0,i.useState)([]),[x,g]=(0,i.useState)(!1),[w,b]=(0,i.useState)(""),[C,A]=(0,i.useState)(""),[R,T]=(0,i.useState)(!1),[D,P]=(0,i.useState)(null),[E,M]=(0,i.useState)({username:"",password:"",role:"user",permissions:{viewUsers:!0,unlockUsers:!1,manageSettings:!1,manageUsers:!1}});if(!e)return null;if(!e.user.permissions.manageUsers)return(0,n.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center",children:(0,n.jsxs)(o.Zp,{children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsx)(o.ZB,{children:"Erişim Reddedildi"}),(0,n.jsx)(o.BT,{children:"Bu sayfaya erişim yetkiniz bulunmamaktadır."})]}),(0,n.jsx)(o.Wu,{children:(0,n.jsx)(c.$,{asChild:!0,children:(0,n.jsx)(s(),{href:"/",children:"Ana Sayfaya D\xf6n"})})})]})});let L=async()=>{g(!0);try{let e=await fetch("/api/users");if(e.ok){let t=await e.json();h(t.users)}else b("Kullanıcılar y\xfcklenirken hata oluştu!"),A("error")}catch(e){b("Bağlantı hatası!"),A("error")}finally{g(!1)}},O=async e=>{e.preventDefault(),g(!0),b("");try{let e=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(E)}),t=await e.json();e.ok?(b("Kullanıcı başarıyla oluşturuldu!"),A("success"),T(!1),M({username:"",password:"",role:"user",permissions:{viewUsers:!0,unlockUsers:!1,manageSettings:!1,manageUsers:!1}}),L()):(b(t.error||"Kullanıcı oluşturulurken hata oluştu!"),A("error"))}catch(e){b("Bağlantı hatası!"),A("error")}finally{g(!1)}},I=async e=>{if(confirm("Bu kullanıcıyı silmek istediğinizden emin misiniz?")){g(!0);try{let t=await fetch(`/api/users/${e}`,{method:"DELETE"}),r=await t.json();t.ok?(b("Kullanıcı başarıyla silindi!"),A("success"),L()):(b(r.error||"Kullanıcı silinirken hata oluştu!"),A("error"))}catch(e){b("Bağlantı hatası!"),A("error")}finally{g(!1)}}},H=e=>{try{return new Date(e).toLocaleString("tr-TR")}catch{return e}},_=e=>{P(e),M({username:e.username,password:"",role:e.role,permissions:{...e.permissions}}),T(!0)},z=async t=>{if(t.preventDefault(),D){g(!0),b("");try{let t=await fetch(`/api/users/${D.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({role:E.role,permissions:E.permissions,...E.password&&{password:E.password}})}),r=await t.json();if(t.ok){b("Kullanıcı başarıyla g\xfcncellendi!"),A("success"),T(!1),P(null),M({username:"",password:"",role:"user",permissions:{viewUsers:!1,unlockUsers:!1,manageSettings:!1,manageUsers:!1}}),L();let t=e?.user?.username||e?.user?.name,r=D.username===t;console.log("Debug - User update check:",{editingUsername:D.username,currentUsername:t,sessionUser:e?.user,isCurrentUser:r}),r?(console.log("Current user permissions updated, forcing logout..."),b("Yetkiniz g\xfcncellendi. Yeniden giriş yapmanız gerekiyor..."),A("success"),setTimeout(()=>{console.log("Signing out..."),signOut({callbackUrl:"/login"})},2e3)):console.log("Different user updated, no logout needed")}else b(r.error||"Kullanıcı g\xfcncellenirken hata oluştu!"),A("error")}catch(e){b("Bağlantı hatası!"),A("error")}finally{g(!1)}}};return(0,n.jsxs)("div",{className:"min-h-screen bg-background",children:[r.isMobile?(0,n.jsx)(rz.c,{currentPath:"/manage-users"}):(0,n.jsx)(rU.u,{currentPath:"/manage-users"}),(0,n.jsx)("main",{className:`
        ${r.isMobile?"pb-safe px-4 py-4":"container mx-auto px-4 py-6"}
      `,children:(0,n.jsxs)("div",{className:`${r.isMobile?"space-y-4":"space-y-6"}`,children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-foreground",children:"Kullanıcı Y\xf6netimi"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Sistem kullanıcılarını y\xf6netin"})]}),(0,n.jsxs)(v,{open:R,onOpenChange:T,children:[(0,n.jsx)(y,{asChild:!0,children:(0,n.jsxs)(c.$,{onClick:()=>{P(null),M({username:"",password:"",role:"user",permissions:{viewUsers:!0,unlockUsers:!1,manageSettings:!1,manageUsers:!1}})},children:[(0,n.jsx)(rB.A,{className:"mr-2 h-4 w-4"}),"Yeni Kullanıcı"]})}),(0,n.jsxs)(j,{className:"max-w-md",children:[(0,n.jsxs)(k,{children:[(0,n.jsx)(S,{children:D?"Kullanıcı D\xfczenle":"Yeni Kullanıcı Oluştur"}),(0,n.jsx)(N,{children:D?"Kullanıcı bilgilerini ve yetkilerini g\xfcncelleyin.":"Yeni bir kullanıcı hesabı oluşturun ve yetkilerini belirleyin."})]}),(0,n.jsxs)("form",{onSubmit:D?z:O,className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.J,{htmlFor:"username",children:"Kullanıcı Adı"}),(0,n.jsx)(d.p,{id:"username",value:E.username,onChange:e=>M({...E,username:e.target.value}),placeholder:"Kullanıcı adı",disabled:!!D,required:!D})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.J,{htmlFor:"password",children:D?"Yeni Şifre (Opsiyonel)":"Şifre"}),(0,n.jsx)(d.p,{id:"password",type:"password",value:E.password,onChange:e=>M({...E,password:e.target.value}),placeholder:D?"Boş bırakılırsa değişmez":"Şifre",required:!D})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.J,{htmlFor:"role",children:"Rol"}),(0,n.jsxs)(rP,{value:E.role,onValueChange:e=>M({...E,role:e}),children:[(0,n.jsx)(rM,{children:(0,n.jsx)(rE,{})}),(0,n.jsxs)(rL,{children:[(0,n.jsx)(rO,{value:"user",children:"User"}),(0,n.jsx)(rO,{value:"admin",children:"Admin"})]})]})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)(u.J,{children:"Yetkiler"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(f.S,{id:"viewUsers",checked:E.permissions.viewUsers,onCheckedChange:e=>M({...E,permissions:{...E.permissions,viewUsers:e}})}),(0,n.jsx)(u.J,{htmlFor:"viewUsers",children:"Kullanıcıları G\xf6r\xfcnt\xfcle"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(f.S,{id:"unlockUsers",checked:E.permissions.unlockUsers,onCheckedChange:e=>M({...E,permissions:{...E.permissions,unlockUsers:e}})}),(0,n.jsx)(u.J,{htmlFor:"unlockUsers",children:"Kullanıcı Unlock"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(f.S,{id:"manageSettings",checked:E.permissions.manageSettings,onCheckedChange:e=>M({...E,permissions:{...E.permissions,manageSettings:e}})}),(0,n.jsx)(u.J,{htmlFor:"manageSettings",children:"Ayarları Y\xf6net"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(f.S,{id:"manageUsers",checked:E.permissions.manageUsers,onCheckedChange:e=>M({...E,permissions:{...E.permissions,manageUsers:e}})}),(0,n.jsx)(u.J,{htmlFor:"manageUsers",children:"Kullanıcıları Y\xf6net"})]})]})]}),(0,n.jsxs)(c.$,{type:"submit",className:"w-full",disabled:x,children:[x&&(0,n.jsx)(rK.A,{className:"mr-2 h-4 w-4 animate-spin"}),x?D?"G\xfcncelleniyor...":"Oluşturuluyor...":D?"Kullanıcı G\xfcncelle":"Kullanıcı Oluştur"]})]})]})]})]}),w&&(0,n.jsx)(p.Fc,{className:"error"===C?"border-destructive":"border-green-500",children:(0,n.jsx)(p.TN,{className:"error"===C?"text-destructive":"text-green-700",children:w})}),(0,n.jsxs)(o.Zp,{children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsx)(o.ZB,{children:"Kullanıcılar"}),(0,n.jsx)(o.BT,{children:"Sistem kullanıcılarının listesi ve y\xf6netimi"})]}),(0,n.jsx)(o.Wu,{children:x?(0,n.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,n.jsx)(rK.A,{className:"h-8 w-8 animate-spin"}),(0,n.jsx)("span",{className:"ml-2",children:"Y\xfckleniyor..."})]}):r.isMobile?(0,n.jsx)("div",{className:"space-y-3",children:a.map(e=>(0,n.jsxs)(o.Zp,{className:"p-4",children:[(0,n.jsx)("div",{className:"flex justify-between items-start mb-3",children:(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("h3",{className:"font-semibold text-lg text-foreground",children:e.username}),(0,n.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,n.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"admin"===e.role?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}`,children:"admin"===e.role?"Admin":"Kullanıcı"})})]})}),(0,n.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:"Yetkiler:"}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-1 mt-1",children:[e.permissions.viewUsers&&(0,n.jsx)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded text-xs",children:"\uD83D\uDC41️ G\xf6r\xfcnt\xfcle"}),e.permissions.unlockUsers&&(0,n.jsx)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs",children:"\uD83D\uDD13 Unlock"}),e.permissions.manageSettings&&(0,n.jsx)("span",{className:"bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs",children:"⚙️ Ayarlar"}),e.permissions.manageUsers&&(0,n.jsx)("span",{className:"bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs",children:"\uD83D\uDC65 Kullanıcılar"})]})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Oluşturulma:"}),(0,n.jsx)("span",{className:"font-medium",children:H(e.createdAt)})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Son Giriş:"}),(0,n.jsx)("span",{className:"font-medium",children:e.lastLogin?H(e.lastLogin):"Hi\xe7 giriş yapmamış"})]})]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(c.$,{className:"flex-1",variant:"outline",onClick:()=>_(e),children:[(0,n.jsx)(rF,{className:"mr-2 h-4 w-4"}),"D\xfczenle"]}),"admin"!==e.username&&(0,n.jsx)(c.$,{variant:"destructive",onClick:()=>I(e.id),children:(0,n.jsx)(rG,{className:"h-4 w-4"})})]})]},e.id))}):(0,n.jsx)("div",{className:"rounded-md border",children:(0,n.jsxs)(m.XI,{children:[(0,n.jsx)(m.A0,{children:(0,n.jsxs)(m.Hj,{children:[(0,n.jsx)(m.nd,{children:"Kullanıcı Adı"}),(0,n.jsx)(m.nd,{children:"Rol"}),(0,n.jsx)(m.nd,{children:"Yetkiler"}),(0,n.jsx)(m.nd,{children:"Oluşturulma"}),(0,n.jsx)(m.nd,{children:"Son Giriş"}),(0,n.jsx)(m.nd,{children:"İşlemler"})]})}),(0,n.jsx)(m.BF,{children:a.map(e=>(0,n.jsxs)(m.Hj,{children:[(0,n.jsx)(m.nA,{className:"font-medium",children:e.username}),(0,n.jsx)(m.nA,{children:(0,n.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${"admin"===e.role?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}`,children:e.role})}),(0,n.jsx)(m.nA,{children:(0,n.jsxs)("div",{className:"text-xs space-y-1",children:[e.permissions.viewUsers&&(0,n.jsx)("div",{children:"\uD83D\uDC41️ View Users"}),e.permissions.unlockUsers&&(0,n.jsx)("div",{children:"\uD83D\uDD13 Unlock Users"}),e.permissions.manageSettings&&(0,n.jsx)("div",{children:"⚙️ Manage Settings"}),e.permissions.manageUsers&&(0,n.jsx)("div",{children:"\uD83D\uDC65 Manage Users"})]})}),(0,n.jsx)(m.nA,{children:H(e.createdAt)}),(0,n.jsx)(m.nA,{children:e.lastLogin?H(e.lastLogin):"Hi\xe7"}),(0,n.jsx)(m.nA,{children:(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>_(e),children:(0,n.jsx)(rF,{className:"h-4 w-4"})}),"admin"!==e.username&&(0,n.jsx)(c.$,{size:"sm",variant:"destructive",onClick:()=>I(e.id),children:(0,n.jsx)(rG,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]})]})}),!1]})}},13964:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14641:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>f,tree:()=>c});var n=r(65239),i=r(48088),a=r(88170),s=r.n(a),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["manage-users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,40157)),"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\manage-users\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\manage-users\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/manage-users/page",pathname:"/manage-users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},18853:(e,t,r)=>{"use strict";r.d(t,{X:()=>a});var n=r(43210),i=r(66156);function a(e){let[t,r]=n.useState(void 0);return(0,i.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32980:(e,t,r)=>{Promise.resolve().then(r.bind(r,40157))},33873:e=>{"use strict";e.exports=require("path")},40157:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\\\PC\\\\Masa\xfcst\xfc\\\\AD_Web\\\\ad-web-manager\\\\src\\\\app\\\\manage-users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\manage-users\\page.tsx","default")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51540:(e,t,r)=>{Promise.resolve().then(r.bind(r,13687))},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var n=r(60687),i=r(43210),a=r(14163),s=i.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var l=r(4780);function o({className:e,...t}){return(0,n.jsx)(s,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},82978:(e,t,r)=>{"use strict";r.d(t,{S:()=>D});var n=r(60687),i=r(43210),a=r(98599),s=r(11273),l=r(70569),o=r(65551),c=r(83721),d=r(18853),u=r(46059),f=r(14163),p="Checkbox",[m,h]=(0,s.A)(p),[x,g]=m(p);function v(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:s,disabled:l,form:c,name:d,onCheckedChange:u,required:f,value:m="on",internal_do_not_use_render:h}=e,[g,v]=(0,o.i)({prop:r,defaultProp:s??!1,onChange:u,caller:p}),[y,w]=i.useState(null),[b,j]=i.useState(null),k=i.useRef(!1),S=!y||!!c||!!y.closest("form"),N={checked:g,disabled:l,setChecked:v,control:y,setControl:w,name:d,form:c,value:m,hasConsumerStoppedPropagationRef:k,required:f,defaultChecked:!C(s)&&s,isFormControl:S,bubbleInput:b,setBubbleInput:j};return(0,n.jsx)(x,{scope:t,...N,children:"function"==typeof h?h(N):a})}var y="CheckboxTrigger",w=i.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...s},o)=>{let{control:c,value:d,disabled:u,checked:p,required:m,setControl:h,setChecked:x,hasConsumerStoppedPropagationRef:v,isFormControl:w,bubbleInput:b}=g(y,e),j=(0,a.s)(o,h),k=i.useRef(p);return i.useEffect(()=>{let e=c?.form;if(e){let t=()=>x(k.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,x]),(0,n.jsx)(f.sG.button,{type:"button",role:"checkbox","aria-checked":C(p)?"mixed":p,"aria-required":m,"data-state":A(p),"data-disabled":u?"":void 0,disabled:u,value:d,...s,ref:j,onKeyDown:(0,l.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(r,e=>{x(e=>!!C(e)||!e),b&&w&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})})});w.displayName=y;var b=i.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:i,checked:a,defaultChecked:s,required:l,disabled:o,value:c,onCheckedChange:d,form:u,...f}=e;return(0,n.jsx)(v,{__scopeCheckbox:r,checked:a,defaultChecked:s,disabled:o,required:l,onCheckedChange:d,name:i,form:u,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(w,{...f,ref:t,__scopeCheckbox:r}),e&&(0,n.jsx)(N,{__scopeCheckbox:r})]})})});b.displayName=p;var j="CheckboxIndicator",k=i.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:i,...a}=e,s=g(j,r);return(0,n.jsx)(u.C,{present:i||C(s.checked)||!0===s.checked,children:(0,n.jsx)(f.sG.span,{"data-state":A(s.checked),"data-disabled":s.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});k.displayName=j;var S="CheckboxBubbleInput",N=i.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:s,hasConsumerStoppedPropagationRef:l,checked:o,defaultChecked:u,required:p,disabled:m,name:h,value:x,form:v,bubbleInput:y,setBubbleInput:w}=g(S,e),b=(0,a.s)(r,w),j=(0,c.Z)(o),k=(0,d.X)(s);i.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(j!==o&&e){let r=new Event("click",{bubbles:t});y.indeterminate=C(o),e.call(y,!C(o)&&o),y.dispatchEvent(r)}},[y,j,o,l]);let N=i.useRef(!C(o)&&o);return(0,n.jsx)(f.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??N.current,required:p,disabled:m,name:h,value:x,form:v,...t,tabIndex:-1,ref:b,style:{...t.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function A(e){return C(e)?"indeterminate":e?"checked":"unchecked"}N.displayName=S;var R=r(13964),T=r(4780);function D({className:e,...t}){return(0,n.jsx)(b,{"data-slot":"checkbox",className:(0,T.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,n.jsx)(k,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,n.jsx)(R.A,{className:"size-3.5"})})})}},83721:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(43210);function i(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(60687);r(43210);var i=r(4780);function a({className:e,type:t,...r}){return(0,n.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>o});var n=r(60687);r(43210);var i=r(24224),a=r(4780);let s=(0,i.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,n.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,a.cn)(s({variant:t}),e),...r})}function o({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"alert-description",className:(0,a.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[243,310,895,443,121],()=>r(14641));module.exports=n})();