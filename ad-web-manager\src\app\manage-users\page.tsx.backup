'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, User<PERSON><PERSON>, Edit, Trash2, LogOut, CheckCircle, XCircle } from 'lucide-react';

interface User {
  id: string;
  username: string;
  role: 'admin' | 'user';
  permissions: {
    viewUsers: boolean;
    unlockUsers: boolean;
    manageSettings: boolean;
    manageUsers: boolean;
  };
  createdAt: string;
  lastLogin?: string;
}

interface NewUser {
  username: string;
  password: string;
  role: 'admin' | 'user';
  permissions: {
    viewUsers: boolean;
    unlockUsers: boolean;
    manageSettings: boolean;
    manageUsers: boolean;
  };
}

export default function ManageUsers() {
  const { data: session, update: updateSession } = useSession();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  
  const [newUser, setNewUser] = useState<NewUser>({
    username: '',
    password: '',
    role: 'user',
    permissions: {
      viewUsers: true,
      unlockUsers: false,
      manageSettings: false,
      manageUsers: false,
    }
  });

  useEffect(() => {
    if (session?.user.permissions.manageUsers) {
      loadUsers();
    }
  }, [session]);

  if (!session) {
    return null;
  }

  if (!session.user.permissions.manageUsers) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card>
          <CardHeader>
            <CardTitle>Erişim Reddedildi</CardTitle>
            <CardDescription>Bu sayfaya erişim yetkiniz bulunmamaktadır.</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/">Ana Sayfaya Dön</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleLogout = () => {
    signOut({ callbackUrl: '/login' });
  };

  const refreshCurrentUserSession = async () => {
    try {
      console.log('🔄 Refreshing session for current user...');
      const response = await fetch('/api/auth/refresh-session', {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Session refresh data:', data);
        // Update the session with new user data
        await updateSession(data.user);
        console.log('✅ Session updated successfully');
      } else {
        console.error('❌ Session refresh failed:', response.status);
      }
    } catch (error) {
      console.error('❌ Error refreshing session:', error);
    }
  };

  const loadUsers = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      } else {
        setMessage('Kullanıcılar yüklenirken hata oluştu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Bağlantı hatası!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newUser),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage('Kullanıcı başarıyla oluşturuldu!');
        setMessageType('success');
        setIsDialogOpen(false);
        setNewUser({
          username: '',
          password: '',
          role: 'user',
          permissions: {
            viewUsers: true,
            unlockUsers: false,
            manageSettings: false,
            manageUsers: false,
          }
        });
        loadUsers();
      } else {
        setMessage(result.error || 'Kullanıcı oluşturulurken hata oluştu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Bağlantı hatası!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Bu kullanıcıyı silmek istediğinizden emin misiniz?')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (response.ok) {
        setMessage('Kullanıcı başarıyla silindi!');
        setMessageType('success');
        loadUsers();
      } else {
        setMessage(result.error || 'Kullanıcı silinirken hata oluştu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Bağlantı hatası!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('tr-TR');
    } catch {
      return dateString;
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setNewUser({
      username: user.username,
      password: '', // Password will be empty for editing
      role: user.role,
      permissions: { ...user.permissions }
    });
    setIsDialogOpen(true);
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingUser) return;

    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch(`/api/users/${editingUser.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role: newUser.role,
          permissions: newUser.permissions,
          ...(newUser.password && { password: newUser.password }) // Only include password if provided
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage('Kullanıcı başarıyla güncellendi!');
        setMessageType('success');
        setIsDialogOpen(false);
        setEditingUser(null);
        setNewUser({
          username: '',
          password: '',
          role: 'user',
          permissions: {
            viewUsers: false,
            unlockUsers: false,
            manageSettings: false,
            manageUsers: false,
          }
        });
        loadUsers();

        // Check if the updated user is the current user (by username - more reliable)
        const currentUsername = session?.user?.username || session?.user?.name;
        const isCurrentUser = editingUser.username === currentUsername;

        console.log('Debug - User update check:', {
          editingUsername: editingUser.username,
          currentUsername: currentUsername,
          sessionUser: session?.user,
          isCurrentUser: isCurrentUser
        });

        if (isCurrentUser) {
          console.log('Current user permissions updated, forcing logout...');
          setMessage('Yetkiniz güncellendi. Yeniden giriş yapmanız gerekiyor...');
          setMessageType('success');

          setTimeout(() => {
            console.log('Signing out...');
            signOut({ callbackUrl: '/login' });
          }, 2000);
        } else {
          console.log('Different user updated, no logout needed');
        }

        // Alternative: Force logout for ANY permission change (more aggressive but safer)
        // Uncomment the lines below if the above doesn't work
        /*
        setMessage('Kullanıcı yetkileri güncellendi. Güvenlik için yeniden giriş yapmanız gerekiyor...');
        setMessageType('success');

        setTimeout(() => {
          signOut({ callbackUrl: '/login' });
        }, 2000);
        */
      } else {
        setMessage(result.error || 'Kullanıcı güncellenirken hata oluştu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Bağlantı hatası!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="border-b bg-card">
        <div className="container mx-auto px-4">
          <div className="flex h-24 items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/Bayraktar Holding Logo.png"
                alt="Bayraktar Holding Logo"
                width={160}
                height={160}
                className="rounded-md"
              />
              <span className="text-xl font-bold text-primary">AD Web Manager</span>
            </Link>
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-sm font-medium text-muted-foreground hover:text-primary">
                Dashboard
              </Link>
              {session.user.permissions.viewUsers && (
                <Link href="/users" className="text-sm font-medium text-muted-foreground hover:text-primary">
                  Locked Users
                </Link>
              <Link href="/password-expiry" className="text-sm font-medium text-muted-foreground hover:text-primary">
                Password Expiry
              </Link>
              )}
              {session.user.permissions.manageSettings && (
                <Link href="/settings" className="text-sm font-medium text-muted-foreground hover:text-primary">
                  Settings
                </Link>
              )}
              <Link href="/manage-users" className="text-sm font-medium text-foreground hover:text-primary">
                Manage Users
              </Link>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  {session.user.name} ({session.user.role})
                </span>
                <Button variant="ghost" size="sm" onClick={handleLogout}>
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-2xl">Kullanıcı Yönetimi</CardTitle>
                <CardDescription>
                  Sistem kullanıcılarını oluşturun ve yetkileri düzenleyin
                </CardDescription>
              </div>
              <Dialog open={isDialogOpen} onOpenChange={(open) => {
                setIsDialogOpen(open);
                if (!open) {
                  setEditingUser(null);
                  setNewUser({
                    username: '',
                    password: '',
                    role: 'user',
                    permissions: {
                      viewUsers: false,
                      unlockUsers: false,
                      manageSettings: false,
                      manageUsers: false,
                    }
                  });
                }
              }}>
                <DialogTrigger asChild>
                  <Button>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Yeni Kullanıcı
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>
                      {editingUser ? 'Kullanıcı Düzenle' : 'Yeni Kullanıcı Oluştur'}
                    </DialogTitle>
                    <DialogDescription>
                      {editingUser
                        ? 'Kullanıcı bilgilerini ve yetkilerini güncelleyin.'
                        : 'Yeni bir kullanıcı hesabı oluşturun ve yetkilerini belirleyin.'
                      }
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={editingUser ? handleUpdateUser : handleCreateUser} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="username">Kullanıcı Adı</Label>
                      <Input
                        id="username"
                        value={newUser.username}
                        onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                        placeholder="Kullanıcı adı"
                        disabled={!!editingUser}
                        required={!editingUser}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password">
                        {editingUser ? 'Yeni Şifre (Opsiyonel)' : 'Şifre'}
                      </Label>
                      <Input
                        id="password"
                        type="password"
                        value={newUser.password}
                        onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                        placeholder={editingUser ? 'Boş bırakılırsa değişmez' : 'Şifre'}
                        required={!editingUser}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="role">Rol</Label>
                      <Select value={newUser.role} onValueChange={(value: 'admin' | 'user') => setNewUser({...newUser, role: value})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="user">User</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-3">
                      <Label>Yetkiler</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="viewUsers"
                            checked={newUser.permissions.viewUsers}
                            onCheckedChange={(checked) => 
                              setNewUser({
                                ...newUser, 
                                permissions: {...newUser.permissions, viewUsers: checked as boolean}
                              })
                            }
                          />
                          <Label htmlFor="viewUsers">Kullanıcıları Görüntüle</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="unlockUsers"
                            checked={newUser.permissions.unlockUsers}
                            onCheckedChange={(checked) => 
                              setNewUser({
                                ...newUser, 
                                permissions: {...newUser.permissions, unlockUsers: checked as boolean}
                              })
                            }
                          />
                          <Label htmlFor="unlockUsers">Kullanıcı Unlock</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="manageSettings"
                            checked={newUser.permissions.manageSettings}
                            onCheckedChange={(checked) => 
                              setNewUser({
                                ...newUser, 
                                permissions: {...newUser.permissions, manageSettings: checked as boolean}
                              })
                            }
                          />
                          <Label htmlFor="manageSettings">Ayarları Yönet</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="manageUsers"
                            checked={newUser.permissions.manageUsers}
                            onCheckedChange={(checked) => 
                              setNewUser({
                                ...newUser, 
                                permissions: {...newUser.permissions, manageUsers: checked as boolean}
                              })
                            }
                          />
                          <Label htmlFor="manageUsers">Kullanıcıları Yönet</Label>
                        </div>
                      </div>
                    </div>
                    <Button type="submit" className="w-full" disabled={isLoading}>
                      {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      {isLoading
                        ? (editingUser ? 'Güncelleniyor...' : 'Oluşturuluyor...')
                        : (editingUser ? 'Kullanıcı Güncelle' : 'Kullanıcı Oluştur')
                      }
                    </Button>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            {message && (
              <Alert className={`mb-6 ${messageType === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                <div className="flex items-center">
                  {messageType === 'success' ? (
                    <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600 mr-2" />
                  )}
                  <AlertDescription className={messageType === 'success' ? 'text-green-800' : 'text-red-800'}>
                    {message}
                  </AlertDescription>
                </div>
              </Alert>
            )}

            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Yükleniyor...</span>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Kullanıcı Adı</TableHead>
                      <TableHead>Rol</TableHead>
                      <TableHead>Yetkiler</TableHead>
                      <TableHead>Oluşturulma</TableHead>
                      <TableHead>Son Giriş</TableHead>
                      <TableHead>İşlemler</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.username}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                          }`}>
                            {user.role}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="text-xs space-y-1">
                            {user.permissions.viewUsers && <div>👁️ View Users</div>}
                            {user.permissions.unlockUsers && <div>🔓 Unlock Users</div>}
                            {user.permissions.manageSettings && <div>⚙️ Manage Settings</div>}
                            {user.permissions.manageUsers && <div>👥 Manage Users</div>}
                          </div>
                        </TableCell>
                        <TableCell>{formatDate(user.createdAt)}</TableCell>
                        <TableCell>{user.lastLogin ? formatDate(user.lastLogin) : 'Hiç'}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditUser(user)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {user.username !== 'admin' && (
                              <Button 
                                size="sm" 
                                variant="destructive"
                                onClick={() => handleDeleteUser(user.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
