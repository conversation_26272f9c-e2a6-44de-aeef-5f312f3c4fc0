@echo off
title AD Web Manager Mobile Test
color 0A

echo.
echo ========================================
echo   AD Web Manager Mobil Test
echo ========================================
echo.

echo [1/3] HTTP server kontrolu yapiliyor...
curl -s -o nul -w "%%{http_code}" http://localhost:3000/ | findstr "200" >nul
if errorlevel 1 (
    echo HATA: HTTP server calismiyorsa once start-server.bat calistirin!
    pause
    exit /b 1
) else (
    echo HTTP server calisir durumda.
)
echo.

echo [2/3] Hosts dosyasina mobil subdomain ekleniyor...
echo 127.0.0.1 m.localhost >> C:\Windows\System32\drivers\etc\hosts 2>nul
if errorlevel 1 (
    echo UYARI: Admin yetkisi gerekiyor. <PERSON> o<PERSON>ak hosts dosyasina ekleyin:
    echo 127.0.0.1 m.localhost
    echo.
) else (
    echo Mobil subdomain eklendi.
)
echo.

echo [3/3] Test URL'leri:
echo.
echo ========================================
echo   Test URL'leri
echo ========================================
echo.
echo DESKTOP VERSION:
echo http://localhost:3000
echo.
echo MOBILE SUBDOMAIN:
echo http://m.localhost:3000
echo.
echo MOBILE USER AGENT TEST:
echo curl -H "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)" http://localhost:3000
echo.
echo ========================================
echo   Mobil Test Tamamlandi
echo ========================================
echo.
echo BILGI: Tarayicida Developer Tools (F12) acip
echo Device Toolbar (Ctrl+Shift+M) ile mobil gorunumu test edin
echo.
pause
