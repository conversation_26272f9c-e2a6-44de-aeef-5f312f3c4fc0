{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "VxsZ3hRKeHcjFiOsyWz2ZuNDEfY8h4a6QtwEExCtPGY=", "__NEXT_PREVIEW_MODE_ID": "b4e5677cf3eada7d8a94819e76346a9f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f21a5d5fdbeef934dd084bf2c34965e87a02dc18b125ef1728762ce648e1e765", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d6f8e3b1b31335ae0a54e55b4e9327e356a099c7e975c0da52c6f53eebf19e85"}}}, "instrumentation": null, "functions": {}}