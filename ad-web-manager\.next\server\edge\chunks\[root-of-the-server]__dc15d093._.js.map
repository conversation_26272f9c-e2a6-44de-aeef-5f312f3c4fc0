{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware';\n\nexport default withAuth(\n  function middleware(req) {\n    // Add any additional middleware logic here\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // Allow access to login page without authentication\n        if (req.nextUrl.pathname === '/login') {\n          return true;\n        }\n        \n        // Require authentication for all other pages\n        return !!token;\n      },\n    },\n  }\n);\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api/auth (NextAuth API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - bayraktar_holding_logo.jpeg (logo file)\n     */\n    '/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;AACrB,2CAA2C;AAC7C,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,oDAAoD;YACpD,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,UAAU;gBACrC,OAAO;YACT;YAEA,6CAA6C;YAC7C,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}