(()=>{var e={};e.id=526,e.ids=[526],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73010:e=>{"use strict";e.exports=require("ldapjs")},78335:()=>{},79478:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>j,routeModule:()=>f,serverHooks:()=>A,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var t={};s.r(t),s.d(t,{POST:()=>x});var o=s(96559),n=s(48088),a=s(37719),u=s(32190),i=s(73010),l=s.n(i),c=s(29021),p=s.n(c),d=s(33873);let m=s.n(d)().join(process.cwd(),"ldap-settings.json");async function x(e){try{let{username:r}=await e.json();if(!r)return u.NextResponse.json({error:"Kullanıcı adı gereklidir"},{status:400});let s=function(){try{if(p().existsSync(m)){let e=p().readFileSync(m,"utf8");return JSON.parse(e)}return null}catch(e){return console.error("Error loading settings:",e),null}}();if(!s)return u.NextResponse.json({error:"LDAP ayarları bulunamadı. L\xfctfen \xf6nce ayarları yapılandırın."},{status:400});if(!s.server||!s.baseDN||!s.username||!s.password)return u.NextResponse.json({error:"LDAP ayarları eksik. L\xfctfen ayarları kontrol edin."},{status:400});let t=s.useSSL?"ldaps":"ldap",o=`${t}://${s.server}:${s.port}`;console.log(`Attempting LDAP connection to: ${o}`),console.log(`Username for unlock: ${r}`);let n=l().createClient({url:o,timeout:6e4,connectTimeout:2e4,tlsOptions:{rejectUnauthorized:!1}});return new Promise(e=>{let t=!1,o=r=>{t||(t=!0,n.destroy(),e(r))},a=setTimeout(()=>{console.error(`LDAP timeout for user: ${r}`),o(u.NextResponse.json({error:"LDAP işlemi zaman aşımına uğradı (60s)"},{status:408}))},65e3);n.on("error",e=>{clearTimeout(a),console.error("LDAP connection error:",e),o(u.NextResponse.json({error:`LDAP bağlantı hatası: ${e.message}`},{status:500}))}),console.log(`Attempting LDAP bind with user: ${s.username}`),n.bind(s.username,s.password,e=>{if(e){clearTimeout(a),console.error("LDAP bind error:",e),o(u.NextResponse.json({error:`LDAP kimlik doğrulama hatası: ${e.message}`},{status:401}));return}console.log("LDAP bind successful, searching for user...");let t=`(&(objectClass=user)(sAMAccountName=${r}))`;n.search(s.baseDN,{scope:"sub",filter:t,attributes:["distinguishedName","lockoutTime","userAccountControl"]},(e,s)=>{if(e){clearTimeout(a),console.error("LDAP search error:",e),o(u.NextResponse.json({error:`Kullanıcı arama hatası: ${e.message}`},{status:500}));return}let t="",i=!1;s.on("searchEntry",e=>{if(i=!0,!(t=e.pojo.objectName)){let r=e.pojo.attributes.find(e=>"distinguishedName"===e.type);r&&r.values&&r.values.length>0&&(t=r.values[0])}}),s.on("error",e=>{clearTimeout(a),console.error("LDAP search result error:",e),o(u.NextResponse.json({error:`Kullanıcı arama sonu\xe7 hatası: ${e.message}`},{status:500}))}),s.on("end",()=>{if(!i||!t){clearTimeout(a),console.error(`User not found or DN not available. Found: ${i}, DN: ${t}, Username: ${r}`),o(u.NextResponse.json({error:`Kullanıcı bulunamadı veya DN alınamadı: ${r}`},{status:404}));return}console.log(`Found user ${r} with DN: ${t}`);let e=[{operation:"replace",modification:{type:"lockoutTime",values:["0"]}},{operation:"delete",modification:{type:"lockoutTime"}}],s=0,c=()=>{if(s>=e.length){clearTimeout(a),o(u.NextResponse.json({error:`T\xfcm unlock y\xf6ntemleri başarısız oldu`},{status:500}));return}let i=new(l()).Change(e[s]);n.modify(t,i,e=>{e?(console.error(`LDAP modify error (method ${s+1}):`,e),s++,c()):(clearTimeout(a),o(u.NextResponse.json({success:!0,message:`${r} kullanıcısı başarıyla unlock edildi (method ${s+1})`})))})};c()})})})})}catch(e){return console.error("Unlock user error:",e),u.NextResponse.json({error:"Unlock işlemi sırasında hata oluştu"},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/unlock-user/route",pathname:"/api/unlock-user",filename:"route",bundlePath:"app/api/unlock-user/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\api\\unlock-user\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:A}=f;function j(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580],()=>s(79478));module.exports=t})();