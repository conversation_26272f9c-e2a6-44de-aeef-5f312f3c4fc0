(()=>{var e={};e.id=520,e.ids=[520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var s=t(49384),a=t(82348);function i(...e){return(0,a.QP)((0,s.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17392:(e,r,t)=>{Promise.resolve().then(t.bind(t,79025))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24016:(e,r,t)=>{Promise.resolve().then(t.bind(t,84199))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:i=!1,...d}){let l=i?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:t,className:e})),...d})}},33873:e=>{"use strict";e.exports=require("path")},35071:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},41862:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=t(60687);t(43210);var a=t(4780);function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},51303:(e,r,t)=>{Promise.resolve().then(t.bind(t,94934))},54300:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(60687),a=t(43210),i=t(14163),n=a.forwardRef((e,r)=>(0,s.jsx)(i.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var o=t(4780);function d({className:e,...r}){return(0,s.jsx)(n,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},61135:()=>{},61397:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63313:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(60687),a=t(43210),i=t(82136),n=t(65773),o=t(44493),d=t(29523),l=t(89667),c=t(54300),u=t(91821),p=t(35071),m=t(41862);function v(){let[e,r]=(0,a.useState)(""),[t,v]=(0,a.useState)(""),[g,b]=(0,a.useState)(!1),[x,h]=(0,a.useState)(""),f=(0,n.useRouter)(),y=async r=>{r.preventDefault(),b(!0),h("");try{let r=await (0,i.signIn)("credentials",{username:e,password:t,redirect:!1,callbackUrl:"/"});r?.error?h("Kullanıcı adı veya şifre hatalı!"):r?.ok&&await (0,i.getSession)()?(f.push("/"),f.refresh()):h("Giriş başarısız!")}catch(e){h("Bir hata oluştu!")}finally{b(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",suppressHydrationWarning:!0,children:(0,s.jsx)("div",{className:"w-full max-w-md",children:(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 flex items-center justify-center",children:(0,s.jsx)("img",{src:"/bayraktar_holding_logo.jpeg",alt:"Bayraktar Holding Logo",width:150,height:150,className:"rounded-lg object-contain"})}),(0,s.jsx)(o.ZB,{className:"text-2xl",children:"AD Web Manager"}),(0,s.jsx)(o.BT,{children:"Sisteme giriş yapmak i\xe7in kullanıcı bilgilerinizi girin"})]}),(0,s.jsxs)(o.Wu,{children:[x&&(0,s.jsxs)(u.Fc,{className:"mb-6 border-red-200 bg-red-50",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)(u.TN,{className:"text-red-800",children:x})]}),(0,s.jsxs)("form",{onSubmit:y,className:"space-y-4",suppressHydrationWarning:!0,children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"username",children:"Kullanıcı Adı"}),(0,s.jsx)(l.p,{id:"username",type:"text",value:e,onChange:e=>r(e.target.value),placeholder:"Kullanıcı adınızı girin",required:!0,disabled:g,suppressHydrationWarning:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"password",children:"Şifre"}),(0,s.jsx)(l.p,{id:"password",type:"password",value:t,onChange:e=>v(e.target.value),placeholder:"Şifrenizi girin",required:!0,disabled:g,suppressHydrationWarning:!0})]}),(0,s.jsxs)(d.$,{type:"submit",className:"w-full",disabled:g,children:[g&&(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),g?"Giriş yapılıyor...":"Giriş Yap"]})]})]})]})})})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79025:(e,r,t)=>{"use strict";t.d(r,{SessionProvider:()=>i});var s=t(60687),a=t(82136);function i({children:e}){return(0,s.jsx)(a.SessionProvider,{children:e})}},79551:e=>{"use strict";e.exports=require("url")},84199:(e,r,t)=>{"use strict";t.d(r,{SessionProvider:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\components\\SessionProvider.tsx","SessionProvider")},87237:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var a=t(4780);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},90623:(e,r,t)=>{Promise.resolve().then(t.bind(t,63313))},91821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>d});var s=t(60687);t(43210);var a=t(24224),i=t(4780);let n=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:r,...t}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:r}),e),...t})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...r})}},93317:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>l});var s=t(37413),a=t(22376),i=t.n(a),n=t(68726),o=t.n(n);t(61135);var d=t(84199);let l={title:"AD Web Manager",description:"Active Directory Web Management Tool"};function c({children:e}){return(0,s.jsx)("html",{lang:"tr",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,suppressHydrationWarning:!0,children:(0,s.jsx)(d.SessionProvider,{children:e})})})}},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\\\PC\\\\Masa\xfcst\xfc\\\\AD_Web\\\\ad-web-manager\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - H.BAYRAKTAR YATIRIM HOLDING A.S\\PC\\Masa\xfcst\xfc\\AD_Web\\ad-web-manager\\src\\app\\login\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,310,895],()=>t(61397));module.exports=s})();