{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "VxsZ3hRKeHcjFiOsyWz2ZuNDEfY8h4a6QtwEExCtPGY=", "__NEXT_PREVIEW_MODE_ID": "c9ae5f2cfce8f34eb693a5027f509b70", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "46dfea07dd501ef3717b20039b50fdb09b4c2c5433b65fe527671a22d12048a2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "863d5cc62e14832517ab691a9a1a1857cc7ea5ff291c859c3e594bcbb532b7b0"}}}, "instrumentation": null, "functions": {}}