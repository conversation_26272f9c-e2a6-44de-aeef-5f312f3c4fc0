import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import ldap from 'ldapjs';
import fs from 'fs';
import path from 'path';

const SETTINGS_FILE = path.join(process.cwd(), 'ldap-settings.json');

interface LdapSettings {
  server: string;
  port: string;
  baseDN: string;
  username: string;
  password: string;
  useSSL: boolean;
}

function loadSettings(): LdapSettings | null {
  try {
    if (fs.existsSync(SETTINGS_FILE)) {
      const data = fs.readFileSync(SETTINGS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return null;
  } catch (error) {
    console.error('Error loading settings:', error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { username } = await request.json();

    if (!username) {
      return NextResponse.json(
        { error: 'Kullanıcı adı gereklidir' },
        { status: 400 }
      );
    }

    console.log(`Starting password extension for user: ${username}`);

    const settings = loadSettings();

    if (!settings) {
      return NextResponse.json(
        { error: 'LDAP ayarları bulunamadı. Lütfen önce ayarları yapılandırın.' },
        { status: 400 }
      );
    }

    if (!settings.server || !settings.baseDN || !settings.username || !settings.password) {
      return NextResponse.json(
        { error: 'LDAP ayarları eksik. Lütfen ayarları kontrol edin.' },
        { status: 400 }
      );
    }

    const protocol = settings.useSSL ? 'ldaps' : 'ldap';
    const url = `${protocol}://${settings.server}:${settings.port}`;

    console.log(`Attempting LDAP connection to: ${url}`);

    const client = ldap.createClient({
      url: url,
      timeout: 60000,
      connectTimeout: 20000,
      tlsOptions: {
        rejectUnauthorized: false
      }
    });

    return new Promise((resolve) => {
      let isResolved = false;

      const resolveOnce = (response: NextResponse) => {
        if (!isResolved) {
          isResolved = true;
          client.destroy();
          resolve(response);
        }
      };

      const timeout = setTimeout(() => {
        console.error(`LDAP timeout for user: ${username}`);
        resolveOnce(NextResponse.json(
          { error: 'LDAP işlemi zaman aşımına uğradı (60s)' },
          { status: 408 }
        ));
      }, 65000);

      client.on('error', (err) => {
        clearTimeout(timeout);
        console.error('LDAP connection error:', err);
        resolveOnce(NextResponse.json(
          { error: `LDAP bağlantı hatası: ${err.message}` },
          { status: 500 }
        ));
      });

      console.log(`Attempting LDAP bind with user: ${settings.username}`);

      client.bind(settings.username, settings.password, (err) => {
        if (err) {
          clearTimeout(timeout);
          console.error('LDAP bind error:', err);
          resolveOnce(NextResponse.json(
            { error: `LDAP kimlik doğrulama hatası: ${err.message}` },
            { status: 401 }
          ));
          return;
        }

        console.log('LDAP bind successful, searching for user...');

        // First, find the user's DN
        const searchFilter = `(&(objectClass=user)(sAMAccountName=${username}))`;
        const searchOptions = {
          scope: 'sub' as const,
          filter: searchFilter,
          attributes: ['distinguishedName', 'pwdLastSet']
        };

        client.search(settings.baseDN, searchOptions, (searchErr, searchRes) => {
          if (searchErr) {
            clearTimeout(timeout);
            console.error('LDAP search error:', searchErr);
            resolveOnce(NextResponse.json(
              { error: `Kullanıcı arama hatası: ${searchErr.message}` },
              { status: 500 }
            ));
            return;
          }

          let userDN = '';
          let foundUser = false;

          searchRes.on('searchEntry', (entry) => {
            foundUser = true;
            userDN = entry.pojo.objectName;
            console.log(`Found user ${username} with DN: ${userDN}`);

            // Clean up DN - remove escape sequences and fix encoding issues
            if (userDN) {
              // Remove backslash escape sequences like \c4\b0 and replace with proper characters
              userDN = userDN.replace(/\\c4\\b0/g, 'İ'); // Turkish İ character
              userDN = userDN.replace(/\\c4\\b1/g, 'ı'); // Turkish ı character
              userDN = userDN.replace(/\\c5\\9f/g, 'ş'); // Turkish ş character
              userDN = userDN.replace(/\\c5\\9e/g, 'Ş'); // Turkish Ş character
              userDN = userDN.replace(/\\c3\\bc/g, 'ü'); // Turkish ü character
              userDN = userDN.replace(/\\c3\\9c/g, 'Ü'); // Turkish Ü character
              userDN = userDN.replace(/\\c3\\b6/g, 'ö'); // Turkish ö character
              userDN = userDN.replace(/\\c3\\96/g, 'Ö'); // Turkish Ö character
              userDN = userDN.replace(/\\c4\\9f/g, 'ğ'); // Turkish ğ character
              userDN = userDN.replace(/\\c4\\9e/g, 'Ğ'); // Turkish Ğ character
              userDN = userDN.replace(/\\c3\\a7/g, 'ç'); // Turkish ç character
              userDN = userDN.replace(/\\c3\\87/g, 'Ç'); // Turkish Ç character
              // Remove any remaining backslash escape sequences
              userDN = userDN.replace(/\\[a-fA-F0-9]{2}/g, '');
              // Clean up any double spaces
              userDN = userDN.replace(/\s+/g, ' ');
              console.log(`Cleaned DN: ${userDN}`);
            }

            // Also try to get DN from attributes if objectName is not available
            if (!userDN) {
              const attributes = entry.pojo.attributes;
              const dnAttr = attributes.find((a: any) => a.type === 'distinguishedName');
              if (dnAttr && dnAttr.values && dnAttr.values.length > 0) {
                userDN = dnAttr.values[0];
                // Clean this DN too
                if (userDN) {
                  // Remove backslash escape sequences like \c4\b0 and replace with proper characters
                  userDN = userDN.replace(/\\c4\\b0/g, 'İ'); // Turkish İ character
                  userDN = userDN.replace(/\\c4\\b1/g, 'ı'); // Turkish ı character
                  userDN = userDN.replace(/\\c5\\9f/g, 'ş'); // Turkish ş character
                  userDN = userDN.replace(/\\c5\\9e/g, 'Ş'); // Turkish Ş character
                  userDN = userDN.replace(/\\c3\\bc/g, 'ü'); // Turkish ü character
                  userDN = userDN.replace(/\\c3\\9c/g, 'Ü'); // Turkish Ü character
                  userDN = userDN.replace(/\\c3\\b6/g, 'ö'); // Turkish ö character
                  userDN = userDN.replace(/\\c3\\96/g, 'Ö'); // Turkish Ö character
                  userDN = userDN.replace(/\\c4\\9f/g, 'ğ'); // Turkish ğ character
                  userDN = userDN.replace(/\\c4\\9e/g, 'Ğ'); // Turkish Ğ character
                  userDN = userDN.replace(/\\c3\\a7/g, 'ç'); // Turkish ç character
                  userDN = userDN.replace(/\\c3\\87/g, 'Ç'); // Turkish Ç character
                  // Remove any remaining backslash escape sequences
                  userDN = userDN.replace(/\\[a-fA-F0-9]{2}/g, '');
                  // Clean up any double spaces
                  userDN = userDN.replace(/\s+/g, ' ');
                }
                console.log(`Using distinguishedName attribute as DN: ${userDN}`);
              }
            }

            // Final check - if still no DN, log error
            if (!userDN) {
              console.error(`No DN found for user ${username}. Entry:`, entry.pojo);
            }
          });

          searchRes.on('error', (err) => {
            clearTimeout(timeout);
            console.error('LDAP search result error:', err);
            resolveOnce(NextResponse.json(
              { error: `Kullanıcı arama sonuç hatası: ${err.message}` },
              { status: 500 }
            ));
          });

          searchRes.on('end', () => {
            if (!foundUser || !userDN) {
              clearTimeout(timeout);
              console.error(`User not found or DN not available. Found: ${foundUser}, DN: ${userDN}, Username: ${username}`);
              resolveOnce(NextResponse.json(
                { error: `Kullanıcı bulunamadı: ${username}` },
                { status: 404 }
              ));
              return;
            }

            console.log(`Found user ${username} with DN: ${userDN}`);

            // Step 1: Set pwdLastSet to 0 (forces password change)
            console.log('Step 1: Setting pwdLastSet to 0');
            const change1 = new ldap.Change({
              operation: 'replace',
              modification: {
                type: 'pwdLastSet',
                values: ['0']
              }
            });

            client.modify(userDN, change1, (modifyErr1) => {
              if (modifyErr1) {
                clearTimeout(timeout);
                console.error('LDAP modify error (step 1):', modifyErr1);
                resolveOnce(NextResponse.json(
                  { error: `Şifre uzatma hatası (adım 1): ${modifyErr1.message}` },
                  { status: 500 }
                ));
                return;
              }

              console.log('Step 1 completed successfully');

              // Step 2: Set pwdLastSet to -1 (sets to current time)
              console.log('Step 2: Setting pwdLastSet to -1');
              const change2 = new ldap.Change({
                operation: 'replace',
                modification: {
                  type: 'pwdLastSet',
                  values: ['-1']
                }
              });

              client.modify(userDN, change2, (modifyErr2) => {
                if (modifyErr2) {
                  clearTimeout(timeout);
                  console.error('LDAP modify error (step 2):', modifyErr2);
                  resolveOnce(NextResponse.json(
                    { error: `Şifre uzatma hatası (adım 2): ${modifyErr2.message}` },
                    { status: 500 }
                  ));
                  return;
                }

                console.log('Step 2 completed successfully');

                // Step 3: Unlock the user account (set lockoutTime to 0)
                console.log('Step 3: Unlocking user account');
                const change3 = new ldap.Change({
                  operation: 'replace',
                  modification: {
                    type: 'lockoutTime',
                    values: ['0']
                  }
                });

                client.modify(userDN, change3, (modifyErr3) => {
                  // Don't fail if unlock fails, just log it
                  if (modifyErr3) {
                    console.log('Step 3 info: User was not locked or unlock not needed:', modifyErr3.message);
                  } else {
                    console.log('Step 3 completed successfully - User unlocked');
                  }

                  clearTimeout(timeout);

                  // Calculate new expiry date (90 days from now)
                  const currentDate = new Date();
                  const newExpiryDate = new Date(currentDate.getTime() + (90 * 24 * 60 * 60 * 1000));

                  console.log(`Password extension and unlock completed successfully for user: ${username}`);

                  resolveOnce(NextResponse.json({
                    success: true,
                    message: `Password extended and user unlocked successfully for user: ${username}`,
                    username: username,
                    extendedAt: currentDate.toISOString(),
                    newExpiryDate: newExpiryDate.toISOString(),
                    daysExtended: 90,
                    unlocked: true
                  }));
                });
              });
            });
          });
        });
      });
    });

  } catch (error: any) {
    console.error('Extend password API error:', error);
    return NextResponse.json(
      { error: `Şifre uzatma hatası: ${error.message}` },
      { status: 500 }
    );
  }
}