/** @type {import('next').NextConfig} */
const nextConfig = {
  // Keep Next.js development indicator in default position (bottom-left)
  devIndicators: {
    position: 'bottom-left',
  },

  // HTTP configuration for development
  async rewrites() {
    return []
  },

  // External packages configuration
  serverExternalPackages: ['ldapjs'],

  // Suppress hydration warnings caused by browser extensions
  reactStrictMode: false,

  // Webpack configuration to handle client-side issues
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },
}

module.exports = nextConfig
