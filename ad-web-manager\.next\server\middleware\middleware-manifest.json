{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ioihxw0T6uLY6tVdvT/KSIElCQ4mXNoUEabkMtzHbR4=", "__NEXT_PREVIEW_MODE_ID": "0c993f699e3825e897cfe823bcd2e8d2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5822b12f8075c56c76da5abd97b66ce2761abd2c54f4b1f94b70be7cfbb34df2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "368315fa94f761b444bfd150d7ebb79b47fbf2fc29cf264e90a6b91cd38e939f"}}}, "instrumentation": null, "functions": {}}