{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ioihxw0T6uLY6tVdvT/KSIElCQ4mXNoUEabkMtzHbR4=", "__NEXT_PREVIEW_MODE_ID": "d399c0efa68dfdcb53405b18352ad7b7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f63c25dacc2af89711acb65ab4cfd391022faf58d85e92c5600402a2aa97c3b8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "36b3f93a8deb38ab478710da5c70c5b906a6398a583f2eeea9e45a12cffcb400"}}}, "instrumentation": null, "functions": {}}