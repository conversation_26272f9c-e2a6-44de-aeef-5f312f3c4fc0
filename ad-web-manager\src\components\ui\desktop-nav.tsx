'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { LogOut } from 'lucide-react';

interface DesktopNavProps {
  currentPath?: string;
}

export function DesktopNav({ currentPath = '/' }: DesktopNavProps) {
  const { data: session } = useSession();

  if (!session) {
    return null;
  }

  const handleLogout = () => {
    signOut({ callbackUrl: '/login' });
  };

  const navItems = [
    {
      href: '/',
      label: 'Dashboard',
      show: true
    },
    {
      href: '/users',
      label: 'Locked Users',
      show: true
    },
    {
      href: '/password-expiry',
      label: 'Password Expiry',
      show: true
    },
    {
      href: '/manage-users',
      label: 'Manage Users',
      show: session.user.permissions.manageUsers
    },
    {
      href: '/settings',
      label: 'Settings',
      show: session.user.permissions.manageSettings
    }
  ];

  return (
    <nav className="border-b bg-card">
      <div className="container mx-auto px-4">
        <div className="flex h-16 lg:h-20 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <Image
              src="/bayraktar_holding_logo.jpeg"
              alt="Bayraktar Holding Logo"
              width={120}
              height={120}
              className="h-10 lg:h-12 w-auto"
              priority
              quality={100}
              unoptimized
            />
            <span className="text-lg lg:text-xl font-bold text-primary">AD Web Manager</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="flex items-center space-x-6">
            {navItems.filter(item => item.show).map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`text-sm font-medium hover:text-primary transition-colors ${
                  currentPath === item.href ? 'text-primary' : 'text-muted-foreground'
                }`}
              >
                {item.label}
              </Link>
            ))}
            
            {/* User Info */}
            <div className="flex items-center space-x-3 border-l pl-6">
              <div className="text-right">
                <div className="text-sm font-medium text-foreground">
                  {session.user.name}
                </div>
                <div className="text-xs text-muted-foreground">
                  {session.user.role}
                </div>
              </div>
              <Button onClick={handleLogout} variant="outline" size="sm">
                <LogOut className="mr-2 h-4 w-4" />
                Çıkış
              </Button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
