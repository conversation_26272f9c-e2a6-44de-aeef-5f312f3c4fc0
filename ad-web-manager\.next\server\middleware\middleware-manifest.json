{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|bayraktar_holding_logo.jpeg).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "VxsZ3hRKeHcjFiOsyWz2ZuNDEfY8h4a6QtwEExCtPGY=", "__NEXT_PREVIEW_MODE_ID": "48f23522535a616d729d628d8574a1cb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1e3ccc4f4e508a97fafc9da7c3449356895990b0a02ccabfabeec32aa6183ee2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bd9ada55c81cee587b101f554b29c49d3bbb24d7fdb9e8352f42efbb593b41a7"}}}, "instrumentation": null, "functions": {}}