'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useNotifications, NotificationContainer } from '@/components/ui/notification';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { MobileNav } from '@/components/ui/mobile-nav';
import { DesktopNav } from '@/components/ui/desktop-nav';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Refresh<PERSON><PERSON>, CheckCircle, XCircle, Clock, LogOut, Use<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Round, Search, User } from 'lucide-react';

interface ExpiredUser {
  username: string;
  displayName: string;
  passwordExpires: string;
  daysSinceExpiry: number;
  lastLogon?: string;
  department?: string;
}

interface DashboardStats {
  totalExpiredUsers: number;
  expiredToday: number;
  expiredThisWeek: number;
  expiredThisMonth: number;
}

interface SearchedUser {
  username: string;
  displayName: string;
  passwordExpires?: string;
  daysUntilExpiry?: number;
  lastLogon?: string;
  department?: string;
  passwordNeverExpires: boolean;
  accountDisabled: boolean;
}

export default function PasswordExpiry() {
  const { data: session } = useSession();
  const device = useDeviceDetection();
  const { notifications, addNotification, removeNotification } = useNotifications();

  // Early return if no session
  if (!session) {
    return null; // Will be redirected by middleware
  }

  const [expiredUsers, setExpiredUsers] = useState<ExpiredUser[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    totalExpiredUsers: 0,
    expiredToday: 0,
    expiredThisWeek: 0,
    expiredThisMonth: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isExtending, setIsExtending] = useState<string | null>(null);
  // Toast notifications are used instead of inline messages
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [searchUsername, setSearchUsername] = useState('');
  const [searchedUser, setSearchedUser] = useState<SearchedUser | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState('');

  const handleLogout = () => {
    signOut({ callbackUrl: '/login' });
  };

  const loadExpiredUsers = async () => {
    setIsLoading(true);

    try {
      const response = await fetch('/api/password-expiry');
      const data = await response.json();

      if (response.ok) {
        setExpiredUsers(data.users || []);
        setStats(data.stats || {
          totalExpiredUsers: 0,
          expiredToday: 0,
          expiredThisWeek: 0,
          expiredThisMonth: 0
        });
        setLastRefresh(new Date());
      } else {
        addNotification({
          title: "❌ Yükleme Hatası",
          description: data.error || 'Şifre süresi dolan kullanıcılar yüklenirken hata oluştu!',
          variant: "error",
          duration: 8000,
        });
      }
    } catch (error) {
      addNotification({
        title: "❌ Bağlantı Hatası",
        description: 'Sunucuya bağlanırken hata oluştu!',
        variant: "error",
        duration: 8000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const searchUser = async () => {
    if (!searchUsername.trim()) {
      setSearchError('Kullanıcı adı gerekli');
      return;
    }

    setIsSearching(true);
    setSearchError('');
    setSearchedUser(null);

    try {
      const response = await fetch(`/api/search-user?username=${encodeURIComponent(searchUsername.trim())}`);
      const data = await response.json();

      if (response.ok) {
        setSearchedUser(data.user);
        setSearchError('');
      } else {
        setSearchError(data.error || 'Kullanıcı bulunamadı');
        setSearchedUser(null);
      }
    } catch (error) {
      setSearchError('Arama sırasında hata oluştu');
      setSearchedUser(null);
    } finally {
      setIsSearching(false);
    }
  };

  const extendPassword = async (username: string) => {
    setIsExtending(username);
    
    try {
      const response = await fetch('/api/extend-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        const extendedAt = data.extendedAt ? new Date(data.extendedAt) : new Date();
        const newExpiryDate = data.newExpiryDate ? new Date(data.newExpiryDate) : null;
        const daysExtended = data.daysExtended || 90;

        let successMessage = `✅ ${username} kullanıcısının şifre süresi başarıyla uzatıldı!`;

        if (data.unlocked) {
          successMessage += `\n🔓 Kullanıcı hesabı unlock edildi!`;
        }

        if (newExpiryDate) {
          successMessage += `\n📅 Yeni şifre son kullanma tarihi: ${newExpiryDate.toLocaleDateString('tr-TR')}`;
        }

        successMessage += `\n⏰ İşlem zamanı: ${extendedAt.toLocaleString('tr-TR')}`;
        successMessage += `\n📈 Uzatılan süre: ${daysExtended} gün`;

        // Success message now handled by notification

        // Show success notification
        addNotification({
          title: "✅ İşlem Başarılı",
          description: `${username} kullanıcısının şifre süresi ${daysExtended} gün uzatıldı${data.unlocked ? ' ve hesap unlock edildi' : ''}.`,
          variant: "success",
          duration: 8000,
        });

        // Clear search results if user was found via search
        if (searchedUser && searchedUser.username === username) {
          setSearchedUser(null);
          setSearchUsername('');
        }

        // Refresh the list after a short delay to allow AD to update
        setTimeout(() => {
          loadExpiredUsers();
        }, 1000);
      } else {
        const errorMsg = data.error || 'Şifre süresi uzatılırken hata oluştu!';
        addNotification({
          title: "❌ İşlem Başarısız",
          description: errorMsg,
          variant: "error",
          duration: 8000,
        });
      }
    } catch (error) {
      console.error('Frontend extend password error:', error);
      const errorMsg = `Bağlantı hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`;
      addNotification({
        title: "❌ Bağlantı Hatası",
        description: errorMsg,
        variant: "error",
        duration: 8000,
      });
    } finally {
      setIsExtending(null);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('tr-TR');
    } catch {
      return dateString;
    }
  };

  const getDaysColor = (days: number) => {
    if (days <= 7) return 'text-red-600';
    if (days <= 30) return 'text-orange-600';
    return 'text-gray-600';
  };

  useEffect(() => {
    if (session?.user.permissions.unlockUsers) {
      loadExpiredUsers();
    }
  }, [session]);

  // Toast notifications handle auto-clear automatically

  if (!session.user.permissions.unlockUsers) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertTriangle className="mx-auto h-12 w-12 text-orange-500 mb-4" />
            <CardTitle>Erişim Reddedildi</CardTitle>
            <CardDescription>
              Bu sayfaya erişim yetkiniz bulunmamaktadır.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button onClick={() => window.history.back()}>
              Geri Dön
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="border-b bg-card">
        <div className="container mx-auto px-4">
          <div className="flex h-24 items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/Bayraktar Holding Logo.png"
                alt="Bayraktar Holding Logo"
                width={160}
                height={160}
                className="rounded-md"
              />
              <span className="text-xl font-bold text-primary">AD Web Manager</span>
            </Link>
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-sm font-medium text-muted-foreground hover:text-primary">
                Dashboard
              </Link>
              <Link href="/users" className="text-sm font-medium text-muted-foreground hover:text-primary">
                Locked Users
              </Link>
              <Link href="/password-expiry" className="text-sm font-medium text-foreground hover:text-primary">
                Password Expiry
              </Link>
              {session.user.permissions.manageSettings && (
                <Link href="/settings" className="text-sm font-medium text-muted-foreground hover:text-primary">
                  Settings
                </Link>
              )}
              {session.user.permissions.manageUsers && (
                <Link href="/manage-users" className="text-sm font-medium text-muted-foreground hover:text-primary">
                  Manage Users
                </Link>
              )}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  {session.user.name} ({session.user.role})
                </span>
                <Button variant="ghost" size="sm" onClick={handleLogout}>
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Password Expiry Management</h1>
              <p className="text-muted-foreground">
                Şifre süresi dolan kullanıcıları görüntüleyin ve şifre sürelerini uzatın
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {lastRefresh && (
                <span className="text-sm text-muted-foreground">
                  Son güncelleme: {formatDate(lastRefresh.toISOString())}
                </span>
              )}
              <Button onClick={loadExpiredUsers} disabled={isLoading}>
                <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                Yenile
              </Button>
            </div>
          </div>

          {/* Dashboard Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="border-red-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-center space-x-3">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                  <CardTitle className="text-base font-semibold text-muted-foreground">Toplam Süresi Dolan</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-4xl font-bold text-red-600">{stats.totalExpiredUsers}</div>
              </CardContent>
            </Card>

            <Card className="border-orange-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-center space-x-3">
                  <Clock className="h-6 w-6 text-orange-600" />
                  <CardTitle className="text-base font-semibold text-muted-foreground">Bugün Dolan</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-4xl font-bold text-orange-600">{stats.expiredToday}</div>
              </CardContent>
            </Card>

            <Card className="border-yellow-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-center space-x-3">
                  <Clock className="h-6 w-6 text-yellow-600" />
                  <CardTitle className="text-base font-semibold text-muted-foreground">Bu Hafta Dolan</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-4xl font-bold text-yellow-600">{stats.expiredThisWeek}</div>
              </CardContent>
            </Card>

            <Card className="border-blue-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-center space-x-3">
                  <KeyRound className="h-6 w-6 text-blue-600" />
                  <CardTitle className="text-base font-semibold text-muted-foreground">Bu Ay Dolan</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-4xl font-bold text-blue-600">{stats.expiredThisMonth}</div>
              </CardContent>
            </Card>
          </div>

          {/* Toast notifications will appear in bottom-right corner */}

          {/* User Search */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Kullanıcı Arama</CardTitle>
              <CardDescription>
                Herhangi bir kullanıcının şifre süresini uzatmak için kullanıcı adını arayın
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex space-x-4">
                  <div className="flex-1">
                    <Label htmlFor="search-username">Kullanıcı Adı</Label>
                    <Input
                      id="search-username"
                      placeholder="Kullanıcı adını girin..."
                      value={searchUsername}
                      onChange={(e) => setSearchUsername(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && searchUser()}
                    />
                  </div>
                  <div className="flex items-end">
                    <Button onClick={searchUser} disabled={isSearching}>
                      {isSearching ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Aranıyor...
                        </>
                      ) : (
                        <>
                          <Search className="mr-2 h-4 w-4" />
                          Ara
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {/* Search Error */}
                {searchError && (
                  <Alert className="border-red-200 bg-red-50">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      {searchError}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Search Result */}
                {searchedUser && (
                  <Card className="border-blue-200 bg-blue-50">
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <User className="h-5 w-5 text-blue-600" />
                            <span className="font-medium text-blue-900">{searchedUser.displayName}</span>
                            <span className="text-sm text-blue-700">({searchedUser.username})</span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <span className="text-blue-700">Durum: </span>
                              <span className={`font-medium ${
                                searchedUser.accountDisabled ? 'text-red-600' : 'text-green-600'
                              }`}>
                                {searchedUser.accountDisabled ? 'Devre Dışı' : 'Aktif'}
                              </span>
                            </div>

                            <div>
                              <span className="text-blue-700">Şifre: </span>
                              <span className={`font-medium ${
                                searchedUser.passwordNeverExpires ? 'text-green-600' : 'text-blue-600'
                              }`}>
                                {searchedUser.passwordNeverExpires ? 'Hiç Dolmaz' : 'Süreli'}
                              </span>
                            </div>

                            {searchedUser.daysUntilExpiry !== undefined && (
                              <div>
                                <span className="text-blue-700">Kalan Süre: </span>
                                <span className={`font-medium ${
                                  searchedUser.daysUntilExpiry < 0 ? 'text-red-600' :
                                  searchedUser.daysUntilExpiry <= 7 ? 'text-orange-600' : 'text-green-600'
                                }`}>
                                  {searchedUser.daysUntilExpiry < 0
                                    ? `${Math.abs(searchedUser.daysUntilExpiry)} gün önce doldu`
                                    : `${searchedUser.daysUntilExpiry} gün`
                                  }
                                </span>
                              </div>
                            )}
                          </div>

                          {searchedUser.lastLogon && (
                            <div className="text-sm">
                              <span className="text-blue-700">Son Giriş: </span>
                              <span className="text-blue-600">{formatDate(searchedUser.lastLogon)}</span>
                            </div>
                          )}
                        </div>

                        <div>
                          {!searchedUser.accountDisabled && (
                            <Button
                              onClick={() => extendPassword(searchedUser.username)}
                              disabled={isExtending === searchedUser.username}
                              className="bg-blue-600 hover:bg-blue-700"
                            >
                              {isExtending === searchedUser.username ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Uzatılıyor & Unlock...
                                </>
                              ) : (
                                <>
                                  <KeyRound className="mr-2 h-4 w-4" />
                                  Süre Uzat & Unlock
                                </>
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Şifre Süresi Dolan Kullanıcılar</CardTitle>
              <CardDescription>
                Şifre süresi dolan kullanıcıları görüntüleyin ve şifre sürelerini uzatın
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Yükleniyor...</span>
                </div>
              ) : expiredUsers.length === 0 ? (
                <div className="text-center py-8">
                  <UserCheck className="mx-auto h-12 w-12 text-green-500 mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">Şifre Süresi Dolan Kullanıcı Yok</h3>
                  <p className="text-muted-foreground">Tüm kullanıcıların şifre süreleri geçerli.</p>
                </div>
              ) : (
                device.isMobile ? (
                  // Mobile Card Layout
                  <div className="space-y-3">
                    {expiredUsers.map((user) => (
                      <Card key={user.username} className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg text-foreground">{user.username}</h3>
                            <p className="text-sm text-muted-foreground">{user.displayName}</p>
                          </div>
                          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getDaysColor(user.daysSinceExpiry)} bg-opacity-20`}>
                            {user.daysSinceExpiry} gün geçti
                          </div>
                        </div>

                        <div className="space-y-2 mb-4">
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Şifre Bitiş:</span>
                            <span className="font-medium text-red-600">
                              {formatDate(user.passwordExpires)}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Son Giriş:</span>
                            <span className="font-medium">
                              {user.lastLogon ? formatDate(user.lastLogon) : 'Hiç giriş yapmamış'}
                            </span>
                          </div>
                        </div>

                        <Button
                          className="w-full bg-blue-600 hover:bg-blue-700"
                          onClick={() => extendPassword(user.username)}
                          disabled={isExtending === user.username}
                        >
                          {isExtending === user.username ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Uzatılıyor & Unlock...
                            </>
                          ) : (
                            <>
                              <KeyRound className="mr-2 h-4 w-4" />
                              Süre Uzat & Unlock
                            </>
                          )}
                        </Button>
                      </Card>
                    ))}
                  </div>
                ) : (
                  // Desktop Table Layout
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Kullanıcı Adı</TableHead>
                          <TableHead>Tam Ad</TableHead>
                          <TableHead>Şifre Bitiş Tarihi</TableHead>
                          <TableHead>Geçen Gün</TableHead>
                          <TableHead>Son Giriş</TableHead>
                          <TableHead>İşlemler</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {expiredUsers.map((user) => (
                          <TableRow key={user.username}>
                            <TableCell className="font-medium">{user.username}</TableCell>
                            <TableCell>{user.displayName}</TableCell>
                            <TableCell>{formatDate(user.passwordExpires)}</TableCell>
                            <TableCell>
                              <span className={`font-medium ${getDaysColor(user.daysSinceExpiry)}`}>
                                {user.daysSinceExpiry} gün
                              </span>
                            </TableCell>
                            <TableCell>{user.lastLogon ? formatDate(user.lastLogon) : 'Hiç'}</TableCell>
                            <TableCell>
                              <Button
                                size="sm"
                                onClick={() => extendPassword(user.username)}
                                disabled={isExtending === user.username}
                                className="bg-blue-600 hover:bg-blue-700"
                              >
                                {isExtending === user.username ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Uzatılıyor & Unlock...
                                  </>
                                ) : (
                                  <>
                                    <KeyRound className="mr-2 h-4 w-4" />
                                    Süre Uzat & Unlock
                                  </>
                                )}
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Notification Container */}
      <NotificationContainer
        notifications={notifications}
        onRemove={removeNotification}
      />
    </div>
  );
}
