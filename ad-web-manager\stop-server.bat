@echo off
title AD Web Manager Server - Stop
color 0C

echo.
echo ========================================
echo    AD Web Manager Server Durduruluyor
echo ========================================
echo.

REM Node.js process'lerini bul ve durdur
echo [1/3] Node.js process'leri aranıyor...

REM Next.js development server'ını durdur
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq node.exe" /fo csv ^| find "node.exe"') do (
    echo Node.js process bulundu: %%i
    taskkill /pid %%i /f >nul 2>&1
    if not errorlevel 1 (
        echo ✓ Process %%i durduruldu
    )
)

echo.
echo [2/3] Port 3000-3010 arasi kontrol ediliyor...

REM 3000-3010 portlarında çalışan process'leri durdur
for /l %%p in (3000,1,3010) do (
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%%p "') do (
        if not "%%a"=="0" (
            echo Port %%p'de process bulundu: %%a
            taskkill /pid %%a /f >nul 2>&1
            if not errorlevel 1 (
                echo ✓ Port %%p'deki process durduruldu
            )
        )
    )
)

echo.
echo [3/3] Temizlik yapiliyor...

REM PowerShell process'lerini kontrol et
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq powershell.exe" /fo csv ^| find "powershell.exe"') do (
    echo PowerShell process kontrol ediliyor: %%i
)

echo.
echo ========================================
echo   AD Web Manager Server Durduruldu
echo ========================================
echo.
echo Tum Node.js ve ilgili process'ler durduruldu.
echo Server'i yeniden baslatmak icin start-server.bat calistirin.
echo.
pause
